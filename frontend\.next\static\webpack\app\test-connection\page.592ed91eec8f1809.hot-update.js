"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-connection/page",{

/***/ "(app-pages-browser)/./src/app/test-connection/page.tsx":
/*!******************************************!*\
  !*** ./src/app/test-connection/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestConnectionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_connection_diagnostic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/connection-diagnostic */ \"(app-pages-browser)/./src/components/connection-diagnostic.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n// Hook to handle client-side only values\nfunction useClientSide() {\n    _s();\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useClientSide.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"useClientSide.useEffect\"], []);\n    return isClient;\n}\n_s(useClientSide, \"k460N28PNzD7zo1YW47Q9UigQis=\");\nfunction TestConnectionPage() {\n    _s1();\n    const [testResults, setTestResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const runManualTests = async ()=>{\n        setIsLoading(true);\n        setTestResults([]);\n        const tests = [\n            {\n                name: 'Direct Health Check',\n                test: async ()=>{\n                    const response = await fetch('http://localhost:8000/health');\n                    const data = await response.json();\n                    return {\n                        success: true,\n                        data\n                    };\n                }\n            },\n            {\n                name: 'API Client Health Check',\n                test: async ()=>{\n                    const data = await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.get('/health');\n                    return {\n                        success: true,\n                        data\n                    };\n                }\n            },\n            {\n                name: 'API Client Connection Test',\n                test: async ()=>{\n                    const result = await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.testConnection();\n                    return result;\n                }\n            },\n            {\n                name: 'Auth Endpoint Test',\n                test: async ()=>{\n                    try {\n                        await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.post('/auth/login', {\n                            email: '<EMAIL>',\n                            password: 'test'\n                        });\n                        return {\n                            success: false,\n                            message: 'Unexpected success'\n                        };\n                    } catch (error) {\n                        if (error.message.includes('Validation error') || error.message.includes('Invalid credentials') || error.message.includes('User not found')) {\n                            return {\n                                success: true,\n                                message: 'Expected validation error',\n                                error: error.message\n                            };\n                        } else {\n                            return {\n                                success: false,\n                                message: error.message,\n                                error\n                            };\n                        }\n                    }\n                }\n            }\n        ];\n        const results = [];\n        for (const test of tests){\n            try {\n                const result = await test.test();\n                results.push({\n                    name: test.name,\n                    ...result\n                });\n            } catch (error) {\n                results.push({\n                    name: test.name,\n                    success: false,\n                    message: error.message,\n                    error\n                });\n            }\n        }\n        setTestResults(results);\n        setIsLoading(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestConnectionPage.useEffect\": ()=>{\n            runManualTests();\n        }\n    }[\"TestConnectionPage.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-8 space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"Connection Test Page\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground mt-2\",\n                        children: \"Diagnose connection issues between frontend and backend\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                lineNumber: 102,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            children: \"Environment Configuration\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Frontend URL:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 29\n                                        }, this),\n                                        \" \",\n                                         true ? window.location.origin : 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Backend URL:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 29\n                                        }, this),\n                                        \" \",\n                                        \"http://localhost:8000\" || 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Debug Mode:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 29\n                                        }, this),\n                                        \" \",\n                                        \"true\" || 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Node Env:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 29\n                                        }, this),\n                                        \" \",\n                                        \"development\" || 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                lineNumber: 110,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: \"Manual Connection Tests\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"These tests check different aspects of the connection\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: runManualTests,\n                                disabled: isLoading,\n                                children: isLoading ? 'Running Tests...' : 'Run Manual Tests'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 21\n                            }, this),\n                            testResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: testResults.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                        variant: result.success ? 'default' : 'destructive',\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium\",\n                                                    children: result.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm mt-1\",\n                                                    children: [\n                                                        result.success ? '✅' : '❌',\n                                                        \" \",\n                                                        result.message\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 41\n                                                }, this),\n                                                result.data && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                                    className: \"mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                            className: \"cursor-pointer text-xs\",\n                                                            children: \"Show Data\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 49\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            className: \"text-xs mt-1 p-2 bg-muted rounded\",\n                                                            children: JSON.stringify(result.data, null, 2)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 45\n                                                }, this),\n                                                result.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                                    className: \"mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                            className: \"cursor-pointer text-xs\",\n                                                            children: \"Show Error\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 49\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            className: \"text-xs mt-1 p-2 bg-muted rounded\",\n                                                            children: JSON.stringify(result.error, null, 2)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 33\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                lineNumber: 133,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_connection_diagnostic__WEBPACK_IMPORTED_MODULE_2__.ConnectionDiagnostic, {}, void 0, false, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                lineNumber: 179,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            children: \"Quick Links\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"http://localhost:8000/health\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"p-3 border rounded-lg hover:bg-muted transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: \"Backend Health\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"http://localhost:8000/health\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"http://localhost:8000/docs\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"p-3 border rounded-lg hover:bg-muted transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: \"API Documentation\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"http://localhost:8000/docs\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/login\",\n                                    className: \"p-3 border rounded-lg hover:bg-muted transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: \"Login Page\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Test authentication\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n        lineNumber: 101,\n        columnNumber: 9\n    }, this);\n}\n_s1(TestConnectionPage, \"Uj4TUIf3Ycuw5fO4gvklHvP1xKg=\");\n_c = TestConnectionPage;\nvar _c;\n$RefreshReg$(_c, \"TestConnectionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/test-connection/page.tsx\n"));

/***/ })

});