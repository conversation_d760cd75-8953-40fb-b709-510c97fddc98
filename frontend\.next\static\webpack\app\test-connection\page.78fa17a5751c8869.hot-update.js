"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-connection/page",{

/***/ "(app-pages-browser)/./src/app/test-connection/page.tsx":
/*!******************************************!*\
  !*** ./src/app/test-connection/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestConnectionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_connection_diagnostic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/connection-diagnostic */ \"(app-pages-browser)/./src/components/connection-diagnostic.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _hooks_use_client_side__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-client-side */ \"(app-pages-browser)/./src/hooks/use-client-side.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction TestConnectionPage() {\n    _s();\n    const [testResults, setTestResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const currentOrigin = (0,_hooks_use_client_side__WEBPACK_IMPORTED_MODULE_7__.useCurrentOrigin)();\n    const runManualTests = async ()=>{\n        setIsLoading(true);\n        setTestResults([]);\n        const tests = [\n            {\n                name: 'Direct Health Check',\n                test: async ()=>{\n                    const response = await fetch('http://localhost:8000/health');\n                    const data = await response.json();\n                    return {\n                        success: true,\n                        data\n                    };\n                }\n            },\n            {\n                name: 'API Client Health Check',\n                test: async ()=>{\n                    const data = await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.get('/health');\n                    return {\n                        success: true,\n                        data\n                    };\n                }\n            },\n            {\n                name: 'API Client Connection Test',\n                test: async ()=>{\n                    const result = await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.testConnection();\n                    return result;\n                }\n            },\n            {\n                name: 'Auth Endpoint Test',\n                test: async ()=>{\n                    try {\n                        await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.post('/auth/login', {\n                            email_or_username: '<EMAIL>',\n                            password: 'testpassword'\n                        });\n                        return {\n                            success: false,\n                            message: 'Unexpected success'\n                        };\n                    } catch (error) {\n                        if (error.message.includes('Validation error') || error.message.includes('Invalid credentials') || error.message.includes('User not found')) {\n                            return {\n                                success: true,\n                                message: 'Expected validation error',\n                                error: error.message\n                            };\n                        } else {\n                            var _error_response, _error_response1;\n                            return {\n                                success: false,\n                                message: error.message || 'Auth endpoint error',\n                                error: {\n                                    message: error.message,\n                                    code: error.code,\n                                    status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                                    data: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data\n                                }\n                            };\n                        }\n                    }\n                }\n            }\n        ];\n        const results = [];\n        for (const test of tests){\n            try {\n                const result = await test.test();\n                results.push({\n                    name: test.name,\n                    ...result\n                });\n            } catch (error) {\n                var _error_response, _error_response1;\n                results.push({\n                    name: test.name,\n                    success: false,\n                    message: error.message || 'Test failed',\n                    error: {\n                        message: error.message,\n                        code: error.code,\n                        status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                        data: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data\n                    }\n                });\n            }\n        }\n        setTestResults(results);\n        setIsLoading(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestConnectionPage.useEffect\": ()=>{\n            runManualTests();\n        }\n    }[\"TestConnectionPage.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-8 space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"Connection Test Page\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground mt-2\",\n                        children: \"Diagnose connection issues between frontend and backend\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                lineNumber: 107,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            children: \"Environment Configuration\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Frontend URL:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 29\n                                        }, this),\n                                        \" \",\n                                        currentOrigin\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Backend URL:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 29\n                                        }, this),\n                                        \" \",\n                                        \"http://localhost:8000\" || 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Debug Mode:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 29\n                                        }, this),\n                                        \" \",\n                                        \"true\" || 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Node Env:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 29\n                                        }, this),\n                                        \" \",\n                                        \"development\" || 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: \"Manual Connection Tests\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"These tests check different aspects of the connection\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: runManualTests,\n                                disabled: isLoading,\n                                children: isLoading ? 'Running Tests...' : 'Run Manual Tests'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 21\n                            }, this),\n                            testResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: testResults.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                        variant: result.success ? 'default' : 'destructive',\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium\",\n                                                    children: result.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm mt-1\",\n                                                    children: [\n                                                        result.success ? '✅' : '❌',\n                                                        \" \",\n                                                        result.message\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 41\n                                                }, this),\n                                                result.data && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                                    className: \"mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                            className: \"cursor-pointer text-xs\",\n                                                            children: \"Show Data\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 49\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            className: \"text-xs mt-1 p-2 bg-muted rounded\",\n                                                            children: JSON.stringify(result.data, null, 2)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 45\n                                                }, this),\n                                                result.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                                    className: \"mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                            className: \"cursor-pointer text-xs\",\n                                                            children: \"Show Error\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 49\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            className: \"text-xs mt-1 p-2 bg-muted rounded\",\n                                                            children: JSON.stringify(result.error, null, 2)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 33\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_connection_diagnostic__WEBPACK_IMPORTED_MODULE_2__.ConnectionDiagnostic, {}, void 0, false, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                lineNumber: 184,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            children: \"Quick Links\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"http://localhost:8000/health\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"p-3 border rounded-lg hover:bg-muted transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: \"Backend Health\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"http://localhost:8000/health\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"http://localhost:8000/docs\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"p-3 border rounded-lg hover:bg-muted transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: \"API Documentation\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"http://localhost:8000/docs\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/login\",\n                                    className: \"p-3 border rounded-lg hover:bg-muted transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: \"Login Page\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Test authentication\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                lineNumber: 187,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n        lineNumber: 106,\n        columnNumber: 9\n    }, this);\n}\n_s(TestConnectionPage, \"aYaOVuukEL0myMCT6O2lU66lGF8=\", false, function() {\n    return [\n        _hooks_use_client_side__WEBPACK_IMPORTED_MODULE_7__.useCurrentOrigin\n    ];\n});\n_c = TestConnectionPage;\nvar _c;\n$RefreshReg$(_c, \"TestConnectionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/test-connection/page.tsx\n"));

/***/ })

});