'use client';

import React, { useState, useEffect } from 'react';
import { ConnectionDiagnostic } from '@/components/connection-diagnostic';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { apiClientInstance } from '@/lib/api-client';

export default function TestConnectionPage() {
    const [testResults, setTestResults] = useState<any[]>([]);
    const [isLoading, setIsLoading] = useState(false);

    const runManualTests = async () => {
        setIsLoading(true);
        setTestResults([]);
        
        const tests = [
            {
                name: 'Direct Health Check',
                test: async () => {
                    const response = await fetch('http://localhost:8000/health');
                    const data = await response.json();
                    return { success: true, data };
                }
            },
            {
                name: 'API Client Health Check',
                test: async () => {
                    const data = await apiClientInstance.get('/health');
                    return { success: true, data };
                }
            },
            {
                name: 'API Client Connection Test',
                test: async () => {
                    const result = await apiClientInstance.testConnection();
                    return result;
                }
            },
            {
                name: 'Auth Endpoint Test',
                test: async () => {
                    try {
                        await apiClientInstance.post('/auth/login', {
                            email: '<EMAIL>',
                            password: 'test'
                        });
                        return { success: false, message: 'Unexpected success' };
                    } catch (error: any) {
                        if (error.message.includes('Validation error') || 
                            error.message.includes('Invalid credentials') ||
                            error.message.includes('User not found')) {
                            return { success: true, message: 'Expected validation error', error: error.message };
                        } else {
                            return { success: false, message: error.message, error };
                        }
                    }
                }
            }
        ];

        const results = [];
        for (const test of tests) {
            try {
                const result = await test.test();
                results.push({
                    name: test.name,
                    ...result
                });
            } catch (error: any) {
                results.push({
                    name: test.name,
                    success: false,
                    message: error.message,
                    error
                });
            }
        }
        
        setTestResults(results);
        setIsLoading(false);
    };

    useEffect(() => {
        runManualTests();
    }, []);

    return (
        <div className="container mx-auto py-8 space-y-8">
            <div className="text-center">
                <h1 className="text-3xl font-bold">Connection Test Page</h1>
                <p className="text-muted-foreground mt-2">
                    Diagnose connection issues between frontend and backend
                </p>
            </div>

            {/* Environment Info */}
            <Card>
                <CardHeader>
                    <CardTitle>Environment Configuration</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <strong>Frontend URL:</strong> {typeof window !== 'undefined' ? window.location.origin : 'N/A'}
                        </div>
                        <div>
                            <strong>Backend URL:</strong> {process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}
                        </div>
                        <div>
                            <strong>Debug Mode:</strong> {process.env.NEXT_PUBLIC_DEBUG || 'false'}
                        </div>
                        <div>
                            <strong>Node Env:</strong> {process.env.NODE_ENV || 'development'}
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Manual Tests */}
            <Card>
                <CardHeader>
                    <CardTitle>Manual Connection Tests</CardTitle>
                    <CardDescription>
                        These tests check different aspects of the connection
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <Button onClick={runManualTests} disabled={isLoading}>
                        {isLoading ? 'Running Tests...' : 'Run Manual Tests'}
                    </Button>
                    
                    {testResults.length > 0 && (
                        <div className="space-y-3">
                            {testResults.map((result, index) => (
                                <Alert key={index} variant={result.success ? 'default' : 'destructive'}>
                                    <AlertDescription>
                                        <div className="font-medium">{result.name}</div>
                                        <div className="text-sm mt-1">
                                            {result.success ? '✅' : '❌'} {result.message}
                                        </div>
                                        {result.data && (
                                            <details className="mt-2">
                                                <summary className="cursor-pointer text-xs">Show Data</summary>
                                                <pre className="text-xs mt-1 p-2 bg-muted rounded">
                                                    {JSON.stringify(result.data, null, 2)}
                                                </pre>
                                            </details>
                                        )}
                                        {result.error && (
                                            <details className="mt-2">
                                                <summary className="cursor-pointer text-xs">Show Error</summary>
                                                <pre className="text-xs mt-1 p-2 bg-muted rounded">
                                                    {JSON.stringify(result.error, null, 2)}
                                                </pre>
                                            </details>
                                        )}
                                    </AlertDescription>
                                </Alert>
                            ))}
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Automated Diagnostic */}
            <ConnectionDiagnostic />

            {/* Quick Links */}
            <Card>
                <CardHeader>
                    <CardTitle>Quick Links</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <a 
                            href="http://localhost:8000/health" 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="p-3 border rounded-lg hover:bg-muted transition-colors"
                        >
                            <div className="font-medium">Backend Health</div>
                            <div className="text-sm text-muted-foreground">http://localhost:8000/health</div>
                        </a>
                        <a 
                            href="http://localhost:8000/docs" 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="p-3 border rounded-lg hover:bg-muted transition-colors"
                        >
                            <div className="font-medium">API Documentation</div>
                            <div className="text-sm text-muted-foreground">http://localhost:8000/docs</div>
                        </a>
                        <a 
                            href="/login" 
                            className="p-3 border rounded-lg hover:bg-muted transition-colors"
                        >
                            <div className="font-medium">Login Page</div>
                            <div className="text-sm text-muted-foreground">Test authentication</div>
                        </a>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
