import { apiClientInstance } from './api-client';
import { jwtDecode } from 'jwt-decode';

// Authentication Types
export interface LoginRequest {
    email_or_username: string;
    password: string;
}

export interface RegisterRequest {
    email: string;
    username: string;
    password: string;
    first_name?: string;
    last_name?: string;
    full_name?: string;
}

export interface TokenResponse {
    access_token: string;
    refresh_token: string;
    token_type: string;
    expires_in: number;
    session_id: string;
}

export interface UserProfile {
    id: string;
    email: string;
    username: string;
    first_name?: string;
    last_name?: string;
    full_name?: string;
    is_active: boolean;
    is_superuser: boolean;
    is_verified: boolean;
    role: string;
    created_at: string;
    last_login?: string;
    email_verified_at?: string;
}

export interface AuthResponse {
    message: string;
    success: boolean;
    data?: unknown;
}

export interface PasswordResetRequest {
    email: string;
}

export interface PasswordResetComplete {
    token: string;
    new_password: string;
}

export interface SessionInfo {
    id: string;
    device_info?: string;
    ip_address?: string;
    user_agent?: string;
    created_at: string;
    last_used_at: string;
    expires_at: string;
    is_current: boolean;
}

export interface UserSessionsResponse {
    sessions: SessionInfo[];
    total_sessions: number;
    active_sessions: number;
}

// JWT Token interface
export interface JWTPayload {
    sub: string; // user_id
    exp: number; // expiration
    iat: number; // issued at
    role: string;
    username: string;
    oauth_provider?: string; // OAuth provider if user logged in via OAuth
}

// Authentication Service Class
export class AuthService {
    private apiClient = apiClientInstance;

    // Login user
    async login(credentials: LoginRequest): Promise<TokenResponse> {
        const response = await this.apiClient.request<TokenResponse>({
            method: 'POST',
            url: '/auth/login',
            data: credentials,
        });

        // Store tokens
        this.apiClient.setTokens(response.access_token, response.refresh_token);

        return response;
    }

    // Register user
    async register(userData: RegisterRequest): Promise<AuthResponse> {
        const response = await this.apiClient.request<AuthResponse>({
            method: 'POST',
            url: '/auth/register',
            data: userData,
        });

        return response;
    }

    // Logout user
    async logout(sessionId?: string, logoutAll: boolean = false): Promise<AuthResponse> {
        const response = await this.apiClient.request<AuthResponse>({
            method: 'POST',
            url: '/auth/logout',
            data: {
                session_id: sessionId,
                logout_all: logoutAll,
            },
        });

        // Clear tokens
        this.apiClient.clearTokens();

        return response;
    }

    // Refresh access token
    async refreshToken(): Promise<TokenResponse> {
        const refreshToken = this.apiClient.getRefreshToken();

        if (!refreshToken) {
            throw new Error('No refresh token available');
        }

        const response = await this.apiClient.request<TokenResponse>({
            method: 'POST',
            url: '/auth/refresh',
            data: {
                refresh_token: refreshToken,
            },
        });

        // Update stored tokens
        this.apiClient.setTokens(response.access_token, response.refresh_token);

        return response;
    }

    // Get current user profile
    async getCurrentUser(): Promise<UserProfile> {
        const response = await this.apiClient.request<UserProfile>({
            method: 'GET',
            url: '/auth/me',
        });

        return response;
    }

    // Get user sessions
    async getUserSessions(): Promise<UserSessionsResponse> {
        const response = await this.apiClient.request<UserSessionsResponse>({
            method: 'GET',
            url: '/auth/sessions',
        });

        return response;
    }

    // Revoke specific session
    async revokeSession(sessionId: string): Promise<AuthResponse> {
        const response = await this.apiClient.request<AuthResponse>({
            method: 'DELETE',
            url: `/auth/sessions/${sessionId}`,
        });

        return response;
    }

    // Request password reset
    async requestPasswordReset(email: string): Promise<AuthResponse> {
        const response = await this.apiClient.request<AuthResponse>({
            method: 'POST',
            url: '/auth/forgot-password',
            data: { email },
        });

        return response;
    }

    // Complete password reset
    async completePasswordReset(token: string, newPassword: string): Promise<AuthResponse> {
        const response = await this.apiClient.request<AuthResponse>({
            method: 'POST',
            url: '/auth/reset-password',
            data: {
                token,
                new_password: newPassword,
            },
        });

        return response;
    }

    // Check if user is authenticated
    isAuthenticated(): boolean {
        const token = this.apiClient.getAccessToken();
        if (!token) return false;

        try {
            const decoded = jwtDecode<JWTPayload>(token);
            const currentTime = Math.floor(Date.now() / 1000);

            return decoded.exp > currentTime;
        } catch {
            return false;
        }
    }

    // Get current user from token
    getCurrentUserFromToken(): JWTPayload | null {
        const token = this.apiClient.getAccessToken();
        if (!token) return null;

        try {
            return jwtDecode<JWTPayload>(token);
        } catch {
            return null;
        }
    }

    // Check if token is expired
    isTokenExpired(): boolean {
        const token = this.apiClient.getAccessToken();
        if (!token) return true;

        try {
            const decoded = jwtDecode<JWTPayload>(token);
            const currentTime = Math.floor(Date.now() / 1000);

            return decoded.exp <= currentTime;
        } catch {
            return true;
        }
    }

    // Get token expiration time
    getTokenExpiration(): Date | null {
        const token = this.apiClient.getAccessToken();
        if (!token) return null;

        try {
            const decoded = jwtDecode<JWTPayload>(token);
            return new Date(decoded.exp * 1000);
        } catch {
            return null;
        }
    }

    // Clear all authentication data
    clearAuth(): void {
        this.apiClient.clearTokens();
    }

    // Verify email
    async verifyEmail(token: string): Promise<AuthResponse> {
        const response = await this.apiClient.request<AuthResponse>({
            method: 'POST',
            url: '/auth/verify-email',
            data: { token },
        });

        return response;
    }

    // Resend verification email
    async resendVerification(email: string): Promise<AuthResponse> {
        const response = await this.apiClient.request<AuthResponse>({
            method: 'POST',
            url: '/auth/resend-verification',
            data: { email },
        });

        return response;
    }

    // OAuth-related methods
    async handleOAuthCallback(provider: string, code: string, state: string): Promise<TokenResponse> {
        const response = await this.apiClient.request<TokenResponse>({
            method: 'GET',
            url: `/oauth/${provider}/callback`,
            params: { code, state },
        });

        return response;
    }

    // Get OAuth providers list
    async getOAuthProviders(): Promise<Record<string, string>> {
        const response = await this.apiClient.request<Record<string, string>>({
            method: 'GET',
            url: '/oauth/providers',
        });

        return response;
    }

    // Check if user has OAuth provider linked
    hasOAuthProvider(provider: string): boolean {
        const user = this.getCurrentUserFromToken();
        return user?.oauth_provider === provider;
    }

    // Test backend connection
    async testConnection() {
        return await this.apiClient.testConnection();
    }
}

// Export singleton instance
export const authService = new AuthService();
export default authService; 