"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/lib/api-client.ts":
/*!*******************************!*\
  !*** ./src/lib/api-client.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   apiClientInstance: () => (/* binding */ apiClientInstance),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\n// API Configuration\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\nconst API_VERSION = '/api/v1';\n// Cache configuration\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds\nconst CACHE_PREFIX = 'api_cache_';\n// Cache utility functions\nclass CacheManager {\n    static getInstance() {\n        if (!CacheManager.instance) {\n            CacheManager.instance = new CacheManager();\n        }\n        return CacheManager.instance;\n    }\n    set(key, data) {\n        let duration = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : CACHE_DURATION;\n        const expiresAt = Date.now() + duration;\n        this.cache.set(key, {\n            data,\n            timestamp: Date.now(),\n            expiresAt\n        });\n        // Also store in localStorage for persistence across page reloads\n        if (true) {\n            try {\n                localStorage.setItem(\"\".concat(CACHE_PREFIX).concat(key), JSON.stringify({\n                    data,\n                    timestamp: Date.now(),\n                    expiresAt\n                }));\n            } catch (error) {\n                console.warn('Failed to store cache in localStorage:', error);\n            }\n        }\n    }\n    get(key) {\n        // Check memory cache first\n        const memoryEntry = this.cache.get(key);\n        if (memoryEntry && memoryEntry.expiresAt > Date.now()) {\n            return memoryEntry.data;\n        }\n        // Check localStorage\n        if (true) {\n            try {\n                const stored = localStorage.getItem(\"\".concat(CACHE_PREFIX).concat(key));\n                if (stored) {\n                    const entry = JSON.parse(stored);\n                    if (entry.expiresAt > Date.now()) {\n                        // Update memory cache\n                        this.cache.set(key, entry);\n                        return entry.data;\n                    } else {\n                        // Remove expired entry\n                        localStorage.removeItem(\"\".concat(CACHE_PREFIX).concat(key));\n                    }\n                }\n            } catch (error) {\n                console.warn('Failed to retrieve cache from localStorage:', error);\n            }\n        }\n        return null;\n    }\n    delete(key) {\n        this.cache.delete(key);\n        if (true) {\n            localStorage.removeItem(\"\".concat(CACHE_PREFIX).concat(key));\n        }\n    }\n    clear(pattern) {\n        if (pattern) {\n            // Clear specific pattern\n            for (const key of this.cache.keys()){\n                if (key.includes(pattern)) {\n                    this.cache.delete(key);\n                }\n            }\n            if (true) {\n                for(let i = localStorage.length - 1; i >= 0; i--){\n                    const key = localStorage.key(i);\n                    if (key && key.startsWith(CACHE_PREFIX) && key.includes(pattern)) {\n                        localStorage.removeItem(key);\n                    }\n                }\n            }\n        } else {\n            // Clear all cache\n            this.cache.clear();\n            if (true) {\n                for(let i = localStorage.length - 1; i >= 0; i--){\n                    const key = localStorage.key(i);\n                    if (key && key.startsWith(CACHE_PREFIX)) {\n                        localStorage.removeItem(key);\n                    }\n                }\n            }\n        }\n    }\n    generateKey(config) {\n        const { method, url, params, data } = config;\n        return \"\".concat(method === null || method === void 0 ? void 0 : method.toUpperCase(), \"_\").concat(url, \"_\").concat(JSON.stringify(params), \"_\").concat(JSON.stringify(data));\n    }\n    constructor(){\n        this.cache = new Map();\n    }\n}\n// Create axios instance\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: \"\".concat(API_BASE_URL).concat(API_VERSION),\n    timeout: 10000,\n    withCredentials: true,\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// Request interceptor to add auth token\napiClient.interceptors.request.use((config)=>{\n    // Get token from localStorage or cookies\n    const token =  true ? localStorage.getItem('access_token') : 0;\n    if (token) {\n        config.headers.Authorization = \"Bearer \".concat(token);\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor for token refresh and error handling\napiClient.interceptors.response.use((response)=>{\n    return response;\n}, async (error)=>{\n    var _error_response;\n    const originalRequest = error.config;\n    // Handle 401 Unauthorized - try to refresh token\n    if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        try {\n            const refreshToken =  true ? localStorage.getItem('refresh_token') : 0;\n            if (refreshToken) {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(API_BASE_URL).concat(API_VERSION, \"/auth/refresh\"), {\n                    refresh_token: refreshToken\n                });\n                const { access_token, refresh_token } = response.data;\n                // Store new tokens\n                if (true) {\n                    localStorage.setItem('access_token', access_token);\n                    localStorage.setItem('refresh_token', refresh_token);\n                }\n                // Retry original request with new token\n                originalRequest.headers.Authorization = \"Bearer \".concat(access_token);\n                return apiClient(originalRequest);\n            }\n        } catch (e) {\n            // Refresh failed - clear tokens and redirect to login\n            if (true) {\n                localStorage.removeItem('access_token');\n                localStorage.removeItem('refresh_token');\n                window.location.href = '/login';\n            }\n        }\n    }\n    return Promise.reject(error);\n});\n// API Client class with typed methods and caching\nclass ApiClient {\n    // Generic request method with caching\n    async request(config) {\n        let useCache = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false, cacheDuration = arguments.length > 2 ? arguments[2] : void 0;\n        try {\n            var _config_method, _config_method1;\n            // Check cache for GET requests\n            if (useCache && ((_config_method = config.method) === null || _config_method === void 0 ? void 0 : _config_method.toLowerCase()) === 'get') {\n                const cacheKey = this.cache.generateKey(config);\n                const cachedData = this.cache.get(cacheKey);\n                if (cachedData) {\n                    return cachedData;\n                }\n            }\n            const response = await this.client.request(config);\n            // Cache successful GET responses\n            if (useCache && ((_config_method1 = config.method) === null || _config_method1 === void 0 ? void 0 : _config_method1.toLowerCase()) === 'get' && response.status === 200) {\n                const cacheKey = this.cache.generateKey(config);\n                this.cache.set(cacheKey, response.data, cacheDuration);\n            }\n            return response.data;\n        } catch (error) {\n            throw this.handleError(error);\n        }\n    }\n    // Cached GET request\n    async get(url, params) {\n        let useCache = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true, cacheDuration = arguments.length > 3 ? arguments[3] : void 0;\n        return this.request({\n            method: 'GET',\n            url,\n            params\n        }, useCache, cacheDuration);\n    }\n    // POST request (no caching)\n    async post(url, data) {\n        return this.request({\n            method: 'POST',\n            url,\n            data\n        }, false);\n    }\n    // PUT request (no caching)\n    async put(url, data) {\n        return this.request({\n            method: 'PUT',\n            url,\n            data\n        }, false);\n    }\n    // DELETE request (no caching)\n    async delete(url) {\n        return this.request({\n            method: 'DELETE',\n            url\n        }, false);\n    }\n    // Clear cache\n    clearCache(pattern) {\n        this.cache.clear(pattern);\n    }\n    // Handle API errors with enhanced diagnostics\n    handleError(error) {\n        console.error('API Client Error:', error);\n        if (error && typeof error === 'object' && 'response' in error) {\n            // Server responded with error status\n            const { status, data } = error.response;\n            // Prefer server-provided detail when available so the UI can show precise feedback\n            const serverMessage = data === null || data === void 0 ? void 0 : data.detail;\n            switch(status){\n                case 400:\n                    return new Error(serverMessage || 'Bad request');\n                case 401:\n                    // Pass through messages such as \"Account is locked\" when supplied\n                    return new Error(serverMessage || 'Unauthorized - please login again');\n                case 403:\n                    return new Error(serverMessage || 'Access denied - insufficient permissions');\n                case 404:\n                    return new Error(serverMessage || 'Resource not found');\n                case 422:\n                    return new Error(serverMessage || 'Validation error');\n                case 429:\n                    return new Error(serverMessage || 'Too many requests - please try again later');\n                case 500:\n                    return new Error(serverMessage || 'Internal server error');\n                default:\n                    return new Error(serverMessage || 'An error occurred');\n            }\n        } else if (error && typeof error === 'object' && 'request' in error) {\n            var _axiosError_config, _axiosError_config1, _axiosError_config2, _axiosError_config3;\n            // Network error - provide more detailed diagnostics\n            const axiosError = error;\n            console.error('Network Error Details:', {\n                code: axiosError.code,\n                message: axiosError.message,\n                config: {\n                    url: (_axiosError_config = axiosError.config) === null || _axiosError_config === void 0 ? void 0 : _axiosError_config.url,\n                    method: (_axiosError_config1 = axiosError.config) === null || _axiosError_config1 === void 0 ? void 0 : _axiosError_config1.method,\n                    baseURL: (_axiosError_config2 = axiosError.config) === null || _axiosError_config2 === void 0 ? void 0 : _axiosError_config2.baseURL,\n                    timeout: (_axiosError_config3 = axiosError.config) === null || _axiosError_config3 === void 0 ? void 0 : _axiosError_config3.timeout\n                }\n            });\n            // Provide specific error messages based on error code\n            if (axiosError.code === 'ECONNREFUSED') {\n                return new Error('Connection refused - Backend server may not be running. Please check if the server is started on http://localhost:8000');\n            } else if (axiosError.code === 'ENOTFOUND') {\n                return new Error('Server not found - Please check the API URL configuration');\n            } else if (axiosError.code === 'ETIMEDOUT') {\n                return new Error('Request timeout - Server is taking too long to respond');\n            } else if (axiosError.code === 'ECONNABORTED') {\n                return new Error('Request aborted - Connection was terminated');\n            } else {\n                return new Error(\"Network error (\".concat(axiosError.code || 'UNKNOWN', \") - Please check your connection and ensure the backend server is running\"));\n            }\n        } else {\n            // Other error\n            const message = error && typeof error === 'object' && 'message' in error ? String(error.message) : 'An unexpected error occurred';\n            return new Error(message);\n        }\n    }\n    // Token management\n    setTokens(accessToken, refreshToken) {\n        if (true) {\n            localStorage.setItem('access_token', accessToken);\n            localStorage.setItem('refresh_token', refreshToken);\n        }\n    }\n    clearTokens() {\n        if (true) {\n            localStorage.removeItem('access_token');\n            localStorage.removeItem('refresh_token');\n        }\n    }\n    getAccessToken() {\n        if (true) {\n            return localStorage.getItem('access_token');\n        }\n        return null;\n    }\n    getRefreshToken() {\n        if (true) {\n            return localStorage.getItem('refresh_token');\n        }\n        return null;\n    }\n    constructor(){\n        this.client = apiClient;\n        this.cache = CacheManager.getInstance();\n    }\n}\n// Export singleton instance\nconst apiClientInstance = new ApiClient();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClientInstance);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvYXBpLWNsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWdGO0FBRWhGLG9CQUFvQjtBQUNwQixNQUFNQyxlQUFlQyx1QkFBK0IsSUFBSSxDQUF1QjtBQUMvRSxNQUFNRyxjQUFjO0FBRXBCLHNCQUFzQjtBQUN0QixNQUFNQyxpQkFBaUIsSUFBSSxLQUFLLE1BQU0sNEJBQTRCO0FBQ2xFLE1BQU1DLGVBQWU7QUFTckIsMEJBQTBCO0FBQzFCLE1BQU1DO0lBSUYsT0FBT0MsY0FBNEI7UUFDL0IsSUFBSSxDQUFDRCxhQUFhRSxRQUFRLEVBQUU7WUFDeEJGLGFBQWFFLFFBQVEsR0FBRyxJQUFJRjtRQUNoQztRQUNBLE9BQU9BLGFBQWFFLFFBQVE7SUFDaEM7SUFFQUMsSUFBSUMsR0FBVyxFQUFFQyxJQUFTLEVBQTJDO1lBQXpDQyxXQUFBQSxpRUFBbUJSO1FBQzNDLE1BQU1TLFlBQVlDLEtBQUtDLEdBQUcsS0FBS0g7UUFDL0IsSUFBSSxDQUFDSSxLQUFLLENBQUNQLEdBQUcsQ0FBQ0MsS0FBSztZQUNoQkM7WUFDQU0sV0FBV0gsS0FBS0MsR0FBRztZQUNuQkY7UUFDSjtRQUVBLGlFQUFpRTtRQUNqRSxJQUFJLElBQTZCLEVBQUU7WUFDL0IsSUFBSTtnQkFDQUssYUFBYUMsT0FBTyxDQUFDLEdBQWtCVCxPQUFmTCxjQUFtQixPQUFKSyxNQUFPVSxLQUFLQyxTQUFTLENBQUM7b0JBQ3pEVjtvQkFDQU0sV0FBV0gsS0FBS0MsR0FBRztvQkFDbkJGO2dCQUNKO1lBQ0osRUFBRSxPQUFPUyxPQUFPO2dCQUNaQyxRQUFRQyxJQUFJLENBQUMsMENBQTBDRjtZQUMzRDtRQUNKO0lBQ0o7SUFFQUcsSUFBSWYsR0FBVyxFQUFjO1FBQ3pCLDJCQUEyQjtRQUMzQixNQUFNZ0IsY0FBYyxJQUFJLENBQUNWLEtBQUssQ0FBQ1MsR0FBRyxDQUFDZjtRQUNuQyxJQUFJZ0IsZUFBZUEsWUFBWWIsU0FBUyxHQUFHQyxLQUFLQyxHQUFHLElBQUk7WUFDbkQsT0FBT1csWUFBWWYsSUFBSTtRQUMzQjtRQUVBLHFCQUFxQjtRQUNyQixJQUFJLElBQTZCLEVBQUU7WUFDL0IsSUFBSTtnQkFDQSxNQUFNZ0IsU0FBU1QsYUFBYVUsT0FBTyxDQUFDLEdBQWtCbEIsT0FBZkwsY0FBbUIsT0FBSks7Z0JBQ3RELElBQUlpQixRQUFRO29CQUNSLE1BQU1FLFFBQW9CVCxLQUFLVSxLQUFLLENBQUNIO29CQUNyQyxJQUFJRSxNQUFNaEIsU0FBUyxHQUFHQyxLQUFLQyxHQUFHLElBQUk7d0JBQzlCLHNCQUFzQjt3QkFDdEIsSUFBSSxDQUFDQyxLQUFLLENBQUNQLEdBQUcsQ0FBQ0MsS0FBS21CO3dCQUNwQixPQUFPQSxNQUFNbEIsSUFBSTtvQkFDckIsT0FBTzt3QkFDSCx1QkFBdUI7d0JBQ3ZCTyxhQUFhYSxVQUFVLENBQUMsR0FBa0JyQixPQUFmTCxjQUFtQixPQUFKSztvQkFDOUM7Z0JBQ0o7WUFDSixFQUFFLE9BQU9ZLE9BQU87Z0JBQ1pDLFFBQVFDLElBQUksQ0FBQywrQ0FBK0NGO1lBQ2hFO1FBQ0o7UUFFQSxPQUFPO0lBQ1g7SUFFQVUsT0FBT3RCLEdBQVcsRUFBUTtRQUN0QixJQUFJLENBQUNNLEtBQUssQ0FBQ2dCLE1BQU0sQ0FBQ3RCO1FBQ2xCLElBQUksSUFBNkIsRUFBRTtZQUMvQlEsYUFBYWEsVUFBVSxDQUFDLEdBQWtCckIsT0FBZkwsY0FBbUIsT0FBSks7UUFDOUM7SUFDSjtJQUVBdUIsTUFBTUMsT0FBZ0IsRUFBUTtRQUMxQixJQUFJQSxTQUFTO1lBQ1QseUJBQXlCO1lBQ3pCLEtBQUssTUFBTXhCLE9BQU8sSUFBSSxDQUFDTSxLQUFLLENBQUNtQixJQUFJLEdBQUk7Z0JBQ2pDLElBQUl6QixJQUFJMEIsUUFBUSxDQUFDRixVQUFVO29CQUN2QixJQUFJLENBQUNsQixLQUFLLENBQUNnQixNQUFNLENBQUN0QjtnQkFDdEI7WUFDSjtZQUNBLElBQUksSUFBNkIsRUFBRTtnQkFDL0IsSUFBSyxJQUFJMkIsSUFBSW5CLGFBQWFvQixNQUFNLEdBQUcsR0FBR0QsS0FBSyxHQUFHQSxJQUFLO29CQUMvQyxNQUFNM0IsTUFBTVEsYUFBYVIsR0FBRyxDQUFDMkI7b0JBQzdCLElBQUkzQixPQUFPQSxJQUFJNkIsVUFBVSxDQUFDbEMsaUJBQWlCSyxJQUFJMEIsUUFBUSxDQUFDRixVQUFVO3dCQUM5RGhCLGFBQWFhLFVBQVUsQ0FBQ3JCO29CQUM1QjtnQkFDSjtZQUNKO1FBQ0osT0FBTztZQUNILGtCQUFrQjtZQUNsQixJQUFJLENBQUNNLEtBQUssQ0FBQ2lCLEtBQUs7WUFDaEIsSUFBSSxJQUE2QixFQUFFO2dCQUMvQixJQUFLLElBQUlJLElBQUluQixhQUFhb0IsTUFBTSxHQUFHLEdBQUdELEtBQUssR0FBR0EsSUFBSztvQkFDL0MsTUFBTTNCLE1BQU1RLGFBQWFSLEdBQUcsQ0FBQzJCO29CQUM3QixJQUFJM0IsT0FBT0EsSUFBSTZCLFVBQVUsQ0FBQ2xDLGVBQWU7d0JBQ3JDYSxhQUFhYSxVQUFVLENBQUNyQjtvQkFDNUI7Z0JBQ0o7WUFDSjtRQUNKO0lBQ0o7SUFFQThCLFlBQVlDLE1BQTBCLEVBQVU7UUFDNUMsTUFBTSxFQUFFQyxNQUFNLEVBQUVDLEdBQUcsRUFBRUMsTUFBTSxFQUFFakMsSUFBSSxFQUFFLEdBQUc4QjtRQUN0QyxPQUFPLEdBQTRCRSxPQUF6QkQsbUJBQUFBLDZCQUFBQSxPQUFRRyxXQUFXLElBQUcsS0FBVXpCLE9BQVB1QixLQUFJLEtBQTZCdkIsT0FBMUJBLEtBQUtDLFNBQVMsQ0FBQ3VCLFNBQVEsS0FBd0IsT0FBckJ4QixLQUFLQyxTQUFTLENBQUNWO0lBQ3ZGOzthQXJHUUssUUFBaUMsSUFBSThCOztBQXNHakQ7QUFFQSx3QkFBd0I7QUFDeEIsTUFBTUMsWUFBMkJqRCw2Q0FBS0EsQ0FBQ2tELE1BQU0sQ0FBQztJQUMxQ0MsU0FBUyxHQUFrQjlDLE9BQWZKLGNBQTJCLE9BQVpJO0lBQzNCK0MsU0FBUztJQUNUQyxpQkFBaUI7SUFDakJDLFNBQVM7UUFDTCxnQkFBZ0I7SUFDcEI7QUFDSjtBQUVBLHdDQUF3QztBQUN4Q0wsVUFBVU0sWUFBWSxDQUFDQyxPQUFPLENBQUNDLEdBQUcsQ0FDOUIsQ0FBQ2Q7SUFDRyx5Q0FBeUM7SUFDekMsTUFBTWUsUUFBUSxLQUE2QixHQUFHdEMsYUFBYVUsT0FBTyxDQUFDLGtCQUFrQixDQUFJO0lBRXpGLElBQUk0QixPQUFPO1FBQ1BmLE9BQU9XLE9BQU8sQ0FBQ0ssYUFBYSxHQUFHLFVBQWdCLE9BQU5EO0lBQzdDO0lBRUEsT0FBT2Y7QUFDWCxHQUNBLENBQUNuQjtJQUNHLE9BQU9vQyxRQUFRQyxNQUFNLENBQUNyQztBQUMxQjtBQUdKLDREQUE0RDtBQUM1RHlCLFVBQVVNLFlBQVksQ0FBQ08sUUFBUSxDQUFDTCxHQUFHLENBQy9CLENBQUNLO0lBQ0csT0FBT0E7QUFDWCxHQUNBLE9BQU90QztRQUlDQTtJQUhKLE1BQU11QyxrQkFBa0J2QyxNQUFNbUIsTUFBTTtJQUVwQyxpREFBaUQ7SUFDakQsSUFBSW5CLEVBQUFBLGtCQUFBQSxNQUFNc0MsUUFBUSxjQUFkdEMsc0NBQUFBLGdCQUFnQndDLE1BQU0sTUFBSyxPQUFPLENBQUNELGdCQUFnQkUsTUFBTSxFQUFFO1FBQzNERixnQkFBZ0JFLE1BQU0sR0FBRztRQUV6QixJQUFJO1lBQ0EsTUFBTUMsZUFBZSxLQUE2QixHQUFHOUMsYUFBYVUsT0FBTyxDQUFDLG1CQUFtQixDQUFJO1lBRWpHLElBQUlvQyxjQUFjO2dCQUNkLE1BQU1KLFdBQVcsTUFBTTlELDZDQUFLQSxDQUFDbUUsSUFBSSxDQUFDLEdBQWtCOUQsT0FBZkosY0FBMkIsT0FBWkksYUFBWSxrQkFBZ0I7b0JBQzVFK0QsZUFBZUY7Z0JBQ25CO2dCQUVBLE1BQU0sRUFBRUcsWUFBWSxFQUFFRCxhQUFhLEVBQUUsR0FBR04sU0FBU2pELElBQUk7Z0JBRXJELG1CQUFtQjtnQkFDbkIsSUFBSSxJQUE2QixFQUFFO29CQUMvQk8sYUFBYUMsT0FBTyxDQUFDLGdCQUFnQmdEO29CQUNyQ2pELGFBQWFDLE9BQU8sQ0FBQyxpQkFBaUIrQztnQkFDMUM7Z0JBRUEsd0NBQXdDO2dCQUN4Q0wsZ0JBQWdCVCxPQUFPLENBQUNLLGFBQWEsR0FBRyxVQUF1QixPQUFiVTtnQkFDbEQsT0FBT3BCLFVBQVVjO1lBQ3JCO1FBQ0osRUFBRSxVQUFNO1lBQ0osc0RBQXNEO1lBQ3RELElBQUksSUFBNkIsRUFBRTtnQkFDL0IzQyxhQUFhYSxVQUFVLENBQUM7Z0JBQ3hCYixhQUFhYSxVQUFVLENBQUM7Z0JBQ3hCcUMsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7WUFDM0I7UUFDSjtJQUNKO0lBRUEsT0FBT1osUUFBUUMsTUFBTSxDQUFDckM7QUFDMUI7QUFrQ0osa0RBQWtEO0FBQzNDLE1BQU1pRDtJQVNULHNDQUFzQztJQUN0QyxNQUFNakIsUUFBV2IsTUFBMEIsRUFBaUU7WUFBL0QrQixXQUFBQSxpRUFBb0IsT0FBT0M7UUFDcEUsSUFBSTtnQkFFZ0JoQyxnQkFZQUE7WUFiaEIsK0JBQStCO1lBQy9CLElBQUkrQixZQUFZL0IsRUFBQUEsaUJBQUFBLE9BQU9DLE1BQU0sY0FBYkQscUNBQUFBLGVBQWVpQyxXQUFXLFFBQU8sT0FBTztnQkFDcEQsTUFBTUMsV0FBVyxJQUFJLENBQUMzRCxLQUFLLENBQUN3QixXQUFXLENBQUNDO2dCQUN4QyxNQUFNbUMsYUFBYSxJQUFJLENBQUM1RCxLQUFLLENBQUNTLEdBQUcsQ0FBQ2tEO2dCQUVsQyxJQUFJQyxZQUFZO29CQUNaLE9BQU9BO2dCQUNYO1lBQ0o7WUFFQSxNQUFNaEIsV0FBVyxNQUFNLElBQUksQ0FBQ2lCLE1BQU0sQ0FBQ3ZCLE9BQU8sQ0FBSWI7WUFFOUMsaUNBQWlDO1lBQ2pDLElBQUkrQixZQUFZL0IsRUFBQUEsa0JBQUFBLE9BQU9DLE1BQU0sY0FBYkQsc0NBQUFBLGdCQUFlaUMsV0FBVyxRQUFPLFNBQVNkLFNBQVNFLE1BQU0sS0FBSyxLQUFLO2dCQUMvRSxNQUFNYSxXQUFXLElBQUksQ0FBQzNELEtBQUssQ0FBQ3dCLFdBQVcsQ0FBQ0M7Z0JBQ3hDLElBQUksQ0FBQ3pCLEtBQUssQ0FBQ1AsR0FBRyxDQUFDa0UsVUFBVWYsU0FBU2pELElBQUksRUFBRThEO1lBQzVDO1lBRUEsT0FBT2IsU0FBU2pELElBQUk7UUFDeEIsRUFBRSxPQUFPVyxPQUFnQjtZQUNyQixNQUFNLElBQUksQ0FBQ3dELFdBQVcsQ0FBQ3hEO1FBQzNCO0lBQ0o7SUFFQSxxQkFBcUI7SUFDckIsTUFBTUcsSUFBT2tCLEdBQVcsRUFBRUMsTUFBWSxFQUFnRTtZQUE5RDRCLFdBQUFBLGlFQUFvQixNQUFNQztRQUM5RCxPQUFPLElBQUksQ0FBQ25CLE9BQU8sQ0FBSTtZQUNuQlosUUFBUTtZQUNSQztZQUNBQztRQUNKLEdBQUc0QixVQUFVQztJQUNqQjtJQUVBLDRCQUE0QjtJQUM1QixNQUFNUixLQUFRdEIsR0FBVyxFQUFFaEMsSUFBVSxFQUFjO1FBQy9DLE9BQU8sSUFBSSxDQUFDMkMsT0FBTyxDQUFJO1lBQ25CWixRQUFRO1lBQ1JDO1lBQ0FoQztRQUNKLEdBQUc7SUFDUDtJQUVBLDJCQUEyQjtJQUMzQixNQUFNb0UsSUFBT3BDLEdBQVcsRUFBRWhDLElBQVUsRUFBYztRQUM5QyxPQUFPLElBQUksQ0FBQzJDLE9BQU8sQ0FBSTtZQUNuQlosUUFBUTtZQUNSQztZQUNBaEM7UUFDSixHQUFHO0lBQ1A7SUFFQSw4QkFBOEI7SUFDOUIsTUFBTXFCLE9BQVVXLEdBQVcsRUFBYztRQUNyQyxPQUFPLElBQUksQ0FBQ1csT0FBTyxDQUFJO1lBQ25CWixRQUFRO1lBQ1JDO1FBQ0osR0FBRztJQUNQO0lBRUEsY0FBYztJQUNkcUMsV0FBVzlDLE9BQWdCLEVBQVE7UUFDL0IsSUFBSSxDQUFDbEIsS0FBSyxDQUFDaUIsS0FBSyxDQUFDQztJQUNyQjtJQUVBLDhDQUE4QztJQUN0QzRDLFlBQVl4RCxLQUFjLEVBQVM7UUFDdkNDLFFBQVFELEtBQUssQ0FBQyxxQkFBcUJBO1FBRW5DLElBQUlBLFNBQVMsT0FBT0EsVUFBVSxZQUFZLGNBQWNBLE9BQU87WUFDM0QscUNBQXFDO1lBQ3JDLE1BQU0sRUFBRXdDLE1BQU0sRUFBRW5ELElBQUksRUFBRSxHQUFHLE1BQTZCaUQsUUFBUTtZQUU5RCxtRkFBbUY7WUFDbkYsTUFBTXFCLGdCQUFnQnRFLGlCQUFBQSwyQkFBQUEsS0FBTXVFLE1BQU07WUFFbEMsT0FBUXBCO2dCQUNKLEtBQUs7b0JBQ0QsT0FBTyxJQUFJcUIsTUFBTUYsaUJBQWlCO2dCQUN0QyxLQUFLO29CQUNELGtFQUFrRTtvQkFDbEUsT0FBTyxJQUFJRSxNQUFNRixpQkFBaUI7Z0JBQ3RDLEtBQUs7b0JBQ0QsT0FBTyxJQUFJRSxNQUFNRixpQkFBaUI7Z0JBQ3RDLEtBQUs7b0JBQ0QsT0FBTyxJQUFJRSxNQUFNRixpQkFBaUI7Z0JBQ3RDLEtBQUs7b0JBQ0QsT0FBTyxJQUFJRSxNQUFNRixpQkFBaUI7Z0JBQ3RDLEtBQUs7b0JBQ0QsT0FBTyxJQUFJRSxNQUFNRixpQkFBaUI7Z0JBQ3RDLEtBQUs7b0JBQ0QsT0FBTyxJQUFJRSxNQUFNRixpQkFBaUI7Z0JBQ3RDO29CQUNJLE9BQU8sSUFBSUUsTUFBTUYsaUJBQWlCO1lBQzFDO1FBQ0osT0FBTyxJQUFJM0QsU0FBUyxPQUFPQSxVQUFVLFlBQVksYUFBYUEsT0FBTztnQkFPcEQ4RCxvQkFDR0EscUJBQ0NBLHFCQUNBQTtZQVRqQixvREFBb0Q7WUFDcEQsTUFBTUEsYUFBYTlEO1lBQ25CQyxRQUFRRCxLQUFLLENBQUMsMEJBQTBCO2dCQUNwQytELE1BQU1ELFdBQVdDLElBQUk7Z0JBQ3JCQyxTQUFTRixXQUFXRSxPQUFPO2dCQUMzQjdDLFFBQVE7b0JBQ0pFLEdBQUcsR0FBRXlDLHFCQUFBQSxXQUFXM0MsTUFBTSxjQUFqQjJDLHlDQUFBQSxtQkFBbUJ6QyxHQUFHO29CQUMzQkQsTUFBTSxHQUFFMEMsc0JBQUFBLFdBQVczQyxNQUFNLGNBQWpCMkMsMENBQUFBLG9CQUFtQjFDLE1BQU07b0JBQ2pDTyxPQUFPLEdBQUVtQyxzQkFBQUEsV0FBVzNDLE1BQU0sY0FBakIyQywwQ0FBQUEsb0JBQW1CbkMsT0FBTztvQkFDbkNDLE9BQU8sR0FBRWtDLHNCQUFBQSxXQUFXM0MsTUFBTSxjQUFqQjJDLDBDQUFBQSxvQkFBbUJsQyxPQUFPO2dCQUN2QztZQUNKO1lBRUEsc0RBQXNEO1lBQ3RELElBQUlrQyxXQUFXQyxJQUFJLEtBQUssZ0JBQWdCO2dCQUNwQyxPQUFPLElBQUlGLE1BQU07WUFDckIsT0FBTyxJQUFJQyxXQUFXQyxJQUFJLEtBQUssYUFBYTtnQkFDeEMsT0FBTyxJQUFJRixNQUFNO1lBQ3JCLE9BQU8sSUFBSUMsV0FBV0MsSUFBSSxLQUFLLGFBQWE7Z0JBQ3hDLE9BQU8sSUFBSUYsTUFBTTtZQUNyQixPQUFPLElBQUlDLFdBQVdDLElBQUksS0FBSyxnQkFBZ0I7Z0JBQzNDLE9BQU8sSUFBSUYsTUFBTTtZQUNyQixPQUFPO2dCQUNILE9BQU8sSUFBSUEsTUFBTSxrQkFBK0MsT0FBN0JDLFdBQVdDLElBQUksSUFBSSxXQUFVO1lBQ3BFO1FBQ0osT0FBTztZQUNILGNBQWM7WUFDZCxNQUFNQyxVQUFVaEUsU0FBUyxPQUFPQSxVQUFVLFlBQVksYUFBYUEsUUFDN0RpRSxPQUFPLE1BQTRCRCxPQUFPLElBQzFDO1lBQ04sT0FBTyxJQUFJSCxNQUFNRztRQUNyQjtJQUNKO0lBRUEsbUJBQW1CO0lBQ25CRSxVQUFVQyxXQUFtQixFQUFFekIsWUFBb0IsRUFBRTtRQUNqRCxJQUFJLElBQTZCLEVBQUU7WUFDL0I5QyxhQUFhQyxPQUFPLENBQUMsZ0JBQWdCc0U7WUFDckN2RSxhQUFhQyxPQUFPLENBQUMsaUJBQWlCNkM7UUFDMUM7SUFDSjtJQUVBMEIsY0FBYztRQUNWLElBQUksSUFBNkIsRUFBRTtZQUMvQnhFLGFBQWFhLFVBQVUsQ0FBQztZQUN4QmIsYUFBYWEsVUFBVSxDQUFDO1FBQzVCO0lBQ0o7SUFFQTRELGlCQUFnQztRQUM1QixJQUFJLElBQTZCLEVBQUU7WUFDL0IsT0FBT3pFLGFBQWFVLE9BQU8sQ0FBQztRQUNoQztRQUNBLE9BQU87SUFDWDtJQUVBZ0Usa0JBQWlDO1FBQzdCLElBQUksSUFBNkIsRUFBRTtZQUMvQixPQUFPMUUsYUFBYVUsT0FBTyxDQUFDO1FBQ2hDO1FBQ0EsT0FBTztJQUNYO0lBcEtBaUUsYUFBYztRQUNWLElBQUksQ0FBQ2hCLE1BQU0sR0FBRzlCO1FBQ2QsSUFBSSxDQUFDL0IsS0FBSyxHQUFHVixhQUFhQyxXQUFXO0lBQ3pDO0FBa0tKO0FBRUEsNEJBQTRCO0FBQ3JCLE1BQU11RixvQkFBb0IsSUFBSXZCLFlBQVk7QUFDakQsaUVBQWV1QixpQkFBaUJBLEVBQUMiLCJzb3VyY2VzIjpbIkQ6XFxuZXh0anNcXHdlYmFwcFxcZnJvbnRlbmRcXHNyY1xcbGliXFxhcGktY2xpZW50LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBheGlvcywgeyBBeGlvc0luc3RhbmNlLCBBeGlvc1JlcXVlc3RDb25maWcsIEF4aW9zUmVzcG9uc2UgfSBmcm9tICdheGlvcyc7XHJcblxyXG4vLyBBUEkgQ29uZmlndXJhdGlvblxyXG5jb25zdCBBUElfQkFTRV9VUkwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjgwMDAnO1xyXG5jb25zdCBBUElfVkVSU0lPTiA9ICcvYXBpL3YxJztcclxuXHJcbi8vIENhY2hlIGNvbmZpZ3VyYXRpb25cclxuY29uc3QgQ0FDSEVfRFVSQVRJT04gPSA1ICogNjAgKiAxMDAwOyAvLyA1IG1pbnV0ZXMgaW4gbWlsbGlzZWNvbmRzXHJcbmNvbnN0IENBQ0hFX1BSRUZJWCA9ICdhcGlfY2FjaGVfJztcclxuXHJcbi8vIENhY2hlIGludGVyZmFjZVxyXG5pbnRlcmZhY2UgQ2FjaGVFbnRyeSB7XHJcbiAgICBkYXRhOiBhbnk7XHJcbiAgICB0aW1lc3RhbXA6IG51bWJlcjtcclxuICAgIGV4cGlyZXNBdDogbnVtYmVyO1xyXG59XHJcblxyXG4vLyBDYWNoZSB1dGlsaXR5IGZ1bmN0aW9uc1xyXG5jbGFzcyBDYWNoZU1hbmFnZXIge1xyXG4gICAgcHJpdmF0ZSBzdGF0aWMgaW5zdGFuY2U6IENhY2hlTWFuYWdlcjtcclxuICAgIHByaXZhdGUgY2FjaGU6IE1hcDxzdHJpbmcsIENhY2hlRW50cnk+ID0gbmV3IE1hcCgpO1xyXG5cclxuICAgIHN0YXRpYyBnZXRJbnN0YW5jZSgpOiBDYWNoZU1hbmFnZXIge1xyXG4gICAgICAgIGlmICghQ2FjaGVNYW5hZ2VyLmluc3RhbmNlKSB7XHJcbiAgICAgICAgICAgIENhY2hlTWFuYWdlci5pbnN0YW5jZSA9IG5ldyBDYWNoZU1hbmFnZXIoKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIENhY2hlTWFuYWdlci5pbnN0YW5jZTtcclxuICAgIH1cclxuXHJcbiAgICBzZXQoa2V5OiBzdHJpbmcsIGRhdGE6IGFueSwgZHVyYXRpb246IG51bWJlciA9IENBQ0hFX0RVUkFUSU9OKTogdm9pZCB7XHJcbiAgICAgICAgY29uc3QgZXhwaXJlc0F0ID0gRGF0ZS5ub3coKSArIGR1cmF0aW9uO1xyXG4gICAgICAgIHRoaXMuY2FjaGUuc2V0KGtleSwge1xyXG4gICAgICAgICAgICBkYXRhLFxyXG4gICAgICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXHJcbiAgICAgICAgICAgIGV4cGlyZXNBdFxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICAvLyBBbHNvIHN0b3JlIGluIGxvY2FsU3RvcmFnZSBmb3IgcGVyc2lzdGVuY2UgYWNyb3NzIHBhZ2UgcmVsb2Fkc1xyXG4gICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oYCR7Q0FDSEVfUFJFRklYfSR7a2V5fWAsIEpTT04uc3RyaW5naWZ5KHtcclxuICAgICAgICAgICAgICAgICAgICBkYXRhLFxyXG4gICAgICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcclxuICAgICAgICAgICAgICAgICAgICBleHBpcmVzQXRcclxuICAgICAgICAgICAgICAgIH0pKTtcclxuICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybignRmFpbGVkIHRvIHN0b3JlIGNhY2hlIGluIGxvY2FsU3RvcmFnZTonLCBlcnJvcik7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgZ2V0KGtleTogc3RyaW5nKTogYW55IHwgbnVsbCB7XHJcbiAgICAgICAgLy8gQ2hlY2sgbWVtb3J5IGNhY2hlIGZpcnN0XHJcbiAgICAgICAgY29uc3QgbWVtb3J5RW50cnkgPSB0aGlzLmNhY2hlLmdldChrZXkpO1xyXG4gICAgICAgIGlmIChtZW1vcnlFbnRyeSAmJiBtZW1vcnlFbnRyeS5leHBpcmVzQXQgPiBEYXRlLm5vdygpKSB7XHJcbiAgICAgICAgICAgIHJldHVybiBtZW1vcnlFbnRyeS5kYXRhO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gQ2hlY2sgbG9jYWxTdG9yYWdlXHJcbiAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBzdG9yZWQgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShgJHtDQUNIRV9QUkVGSVh9JHtrZXl9YCk7XHJcbiAgICAgICAgICAgICAgICBpZiAoc3RvcmVkKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZW50cnk6IENhY2hlRW50cnkgPSBKU09OLnBhcnNlKHN0b3JlZCk7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKGVudHJ5LmV4cGlyZXNBdCA+IERhdGUubm93KCkpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gVXBkYXRlIG1lbW9yeSBjYWNoZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmNhY2hlLnNldChrZXksIGVudHJ5KTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGVudHJ5LmRhdGE7XHJcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gUmVtb3ZlIGV4cGlyZWQgZW50cnlcclxuICAgICAgICAgICAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oYCR7Q0FDSEVfUFJFRklYfSR7a2V5fWApO1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybignRmFpbGVkIHRvIHJldHJpZXZlIGNhY2hlIGZyb20gbG9jYWxTdG9yYWdlOicsIGVycm9yKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICB9XHJcblxyXG4gICAgZGVsZXRlKGtleTogc3RyaW5nKTogdm9pZCB7XHJcbiAgICAgICAgdGhpcy5jYWNoZS5kZWxldGUoa2V5KTtcclxuICAgICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcclxuICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oYCR7Q0FDSEVfUFJFRklYfSR7a2V5fWApO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBjbGVhcihwYXR0ZXJuPzogc3RyaW5nKTogdm9pZCB7XHJcbiAgICAgICAgaWYgKHBhdHRlcm4pIHtcclxuICAgICAgICAgICAgLy8gQ2xlYXIgc3BlY2lmaWMgcGF0dGVyblxyXG4gICAgICAgICAgICBmb3IgKGNvbnN0IGtleSBvZiB0aGlzLmNhY2hlLmtleXMoKSkge1xyXG4gICAgICAgICAgICAgICAgaWYgKGtleS5pbmNsdWRlcyhwYXR0ZXJuKSkge1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY2FjaGUuZGVsZXRlKGtleSk7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgICAgICAgICAgICBmb3IgKGxldCBpID0gbG9jYWxTdG9yYWdlLmxlbmd0aCAtIDE7IGkgPj0gMDsgaS0tKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qga2V5ID0gbG9jYWxTdG9yYWdlLmtleShpKTtcclxuICAgICAgICAgICAgICAgICAgICBpZiAoa2V5ICYmIGtleS5zdGFydHNXaXRoKENBQ0hFX1BSRUZJWCkgJiYga2V5LmluY2x1ZGVzKHBhdHRlcm4pKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKGtleSk7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgLy8gQ2xlYXIgYWxsIGNhY2hlXHJcbiAgICAgICAgICAgIHRoaXMuY2FjaGUuY2xlYXIoKTtcclxuICAgICAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgICAgICAgICAgICBmb3IgKGxldCBpID0gbG9jYWxTdG9yYWdlLmxlbmd0aCAtIDE7IGkgPj0gMDsgaS0tKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qga2V5ID0gbG9jYWxTdG9yYWdlLmtleShpKTtcclxuICAgICAgICAgICAgICAgICAgICBpZiAoa2V5ICYmIGtleS5zdGFydHNXaXRoKENBQ0hFX1BSRUZJWCkpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oa2V5KTtcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgZ2VuZXJhdGVLZXkoY29uZmlnOiBBeGlvc1JlcXVlc3RDb25maWcpOiBzdHJpbmcge1xyXG4gICAgICAgIGNvbnN0IHsgbWV0aG9kLCB1cmwsIHBhcmFtcywgZGF0YSB9ID0gY29uZmlnO1xyXG4gICAgICAgIHJldHVybiBgJHttZXRob2Q/LnRvVXBwZXJDYXNlKCl9XyR7dXJsfV8ke0pTT04uc3RyaW5naWZ5KHBhcmFtcyl9XyR7SlNPTi5zdHJpbmdpZnkoZGF0YSl9YDtcclxuICAgIH1cclxufVxyXG5cclxuLy8gQ3JlYXRlIGF4aW9zIGluc3RhbmNlXHJcbmNvbnN0IGFwaUNsaWVudDogQXhpb3NJbnN0YW5jZSA9IGF4aW9zLmNyZWF0ZSh7XHJcbiAgICBiYXNlVVJMOiBgJHtBUElfQkFTRV9VUkx9JHtBUElfVkVSU0lPTn1gLFxyXG4gICAgdGltZW91dDogMTAwMDAsXHJcbiAgICB3aXRoQ3JlZGVudGlhbHM6IHRydWUsIC8vIEltcG9ydGFudCBmb3IgSldUIGNvb2tpZXNcclxuICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgfSxcclxufSk7XHJcblxyXG4vLyBSZXF1ZXN0IGludGVyY2VwdG9yIHRvIGFkZCBhdXRoIHRva2VuXHJcbmFwaUNsaWVudC5pbnRlcmNlcHRvcnMucmVxdWVzdC51c2UoXHJcbiAgICAoY29uZmlnKSA9PiB7XHJcbiAgICAgICAgLy8gR2V0IHRva2VuIGZyb20gbG9jYWxTdG9yYWdlIG9yIGNvb2tpZXNcclxuICAgICAgICBjb25zdCB0b2tlbiA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnID8gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2FjY2Vzc190b2tlbicpIDogbnVsbDtcclxuXHJcbiAgICAgICAgaWYgKHRva2VuKSB7XHJcbiAgICAgICAgICAgIGNvbmZpZy5oZWFkZXJzLkF1dGhvcml6YXRpb24gPSBgQmVhcmVyICR7dG9rZW59YDtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHJldHVybiBjb25maWc7XHJcbiAgICB9LFxyXG4gICAgKGVycm9yKSA9PiB7XHJcbiAgICAgICAgcmV0dXJuIFByb21pc2UucmVqZWN0KGVycm9yKTtcclxuICAgIH1cclxuKTtcclxuXHJcbi8vIFJlc3BvbnNlIGludGVyY2VwdG9yIGZvciB0b2tlbiByZWZyZXNoIGFuZCBlcnJvciBoYW5kbGluZ1xyXG5hcGlDbGllbnQuaW50ZXJjZXB0b3JzLnJlc3BvbnNlLnVzZShcclxuICAgIChyZXNwb25zZTogQXhpb3NSZXNwb25zZSkgPT4ge1xyXG4gICAgICAgIHJldHVybiByZXNwb25zZTtcclxuICAgIH0sXHJcbiAgICBhc3luYyAoZXJyb3IpID0+IHtcclxuICAgICAgICBjb25zdCBvcmlnaW5hbFJlcXVlc3QgPSBlcnJvci5jb25maWc7XHJcblxyXG4gICAgICAgIC8vIEhhbmRsZSA0MDEgVW5hdXRob3JpemVkIC0gdHJ5IHRvIHJlZnJlc2ggdG9rZW5cclxuICAgICAgICBpZiAoZXJyb3IucmVzcG9uc2U/LnN0YXR1cyA9PT0gNDAxICYmICFvcmlnaW5hbFJlcXVlc3QuX3JldHJ5KSB7XHJcbiAgICAgICAgICAgIG9yaWdpbmFsUmVxdWVzdC5fcmV0cnkgPSB0cnVlO1xyXG5cclxuICAgICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHJlZnJlc2hUb2tlbiA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnID8gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3JlZnJlc2hfdG9rZW4nKSA6IG51bGw7XHJcblxyXG4gICAgICAgICAgICAgICAgaWYgKHJlZnJlc2hUb2tlbikge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MucG9zdChgJHtBUElfQkFTRV9VUkx9JHtBUElfVkVSU0lPTn0vYXV0aC9yZWZyZXNoYCwge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICByZWZyZXNoX3Rva2VuOiByZWZyZXNoVG9rZW4sXHJcbiAgICAgICAgICAgICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHsgYWNjZXNzX3Rva2VuLCByZWZyZXNoX3Rva2VuIH0gPSByZXNwb25zZS5kYXRhO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAvLyBTdG9yZSBuZXcgdG9rZW5zXHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdhY2Nlc3NfdG9rZW4nLCBhY2Nlc3NfdG9rZW4pO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgncmVmcmVzaF90b2tlbicsIHJlZnJlc2hfdG9rZW4pO1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gUmV0cnkgb3JpZ2luYWwgcmVxdWVzdCB3aXRoIG5ldyB0b2tlblxyXG4gICAgICAgICAgICAgICAgICAgIG9yaWdpbmFsUmVxdWVzdC5oZWFkZXJzLkF1dGhvcml6YXRpb24gPSBgQmVhcmVyICR7YWNjZXNzX3Rva2VufWA7XHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGFwaUNsaWVudChvcmlnaW5hbFJlcXVlc3QpO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9IGNhdGNoIHtcclxuICAgICAgICAgICAgICAgIC8vIFJlZnJlc2ggZmFpbGVkIC0gY2xlYXIgdG9rZW5zIGFuZCByZWRpcmVjdCB0byBsb2dpblxyXG4gICAgICAgICAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2FjY2Vzc190b2tlbicpO1xyXG4gICAgICAgICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdyZWZyZXNoX3Rva2VuJyk7XHJcbiAgICAgICAgICAgICAgICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnL2xvZ2luJztcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgcmV0dXJuIFByb21pc2UucmVqZWN0KGVycm9yKTtcclxuICAgIH1cclxuKTtcclxuXHJcbi8vIEFQSSBSZXNwb25zZSB0eXBlc1xyXG5leHBvcnQgaW50ZXJmYWNlIEFwaVJlc3BvbnNlPFQgPSB1bmtub3duPiB7XHJcbiAgICBzdWNjZXNzOiBib29sZWFuO1xyXG4gICAgbWVzc2FnZTogc3RyaW5nO1xyXG4gICAgZGF0YT86IFQ7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgRXJyb3JSZXNwb25zZSB7XHJcbiAgICBkZXRhaWw6IHN0cmluZztcclxuICAgIGVycm9yX2NvZGU/OiBzdHJpbmc7XHJcbiAgICBzdWNjZXNzOiBmYWxzZTtcclxufVxyXG5cclxuLy8gRXJyb3IgdHlwZXMgZm9yIGhhbmRsaW5nXHJcbmludGVyZmFjZSBBeGlvc0Vycm9yUmVzcG9uc2Uge1xyXG4gICAgc3RhdHVzOiBudW1iZXI7XHJcbiAgICBkYXRhOiBFcnJvclJlc3BvbnNlO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgRXJyb3JXaXRoUmVzcG9uc2Uge1xyXG4gICAgcmVzcG9uc2U6IEF4aW9zRXJyb3JSZXNwb25zZTtcclxufVxyXG5cclxuaW50ZXJmYWNlIEVycm9yV2l0aFJlcXVlc3Qge1xyXG4gICAgcmVxdWVzdDogdW5rbm93bjtcclxufVxyXG5cclxuaW50ZXJmYWNlIEVycm9yV2l0aE1lc3NhZ2Uge1xyXG4gICAgbWVzc2FnZTogc3RyaW5nO1xyXG59XHJcblxyXG4vLyBBUEkgQ2xpZW50IGNsYXNzIHdpdGggdHlwZWQgbWV0aG9kcyBhbmQgY2FjaGluZ1xyXG5leHBvcnQgY2xhc3MgQXBpQ2xpZW50IHtcclxuICAgIHByaXZhdGUgY2xpZW50OiBBeGlvc0luc3RhbmNlO1xyXG4gICAgcHJpdmF0ZSBjYWNoZTogQ2FjaGVNYW5hZ2VyO1xyXG5cclxuICAgIGNvbnN0cnVjdG9yKCkge1xyXG4gICAgICAgIHRoaXMuY2xpZW50ID0gYXBpQ2xpZW50O1xyXG4gICAgICAgIHRoaXMuY2FjaGUgPSBDYWNoZU1hbmFnZXIuZ2V0SW5zdGFuY2UoKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBHZW5lcmljIHJlcXVlc3QgbWV0aG9kIHdpdGggY2FjaGluZ1xyXG4gICAgYXN5bmMgcmVxdWVzdDxUPihjb25maWc6IEF4aW9zUmVxdWVzdENvbmZpZywgdXNlQ2FjaGU6IGJvb2xlYW4gPSBmYWxzZSwgY2FjaGVEdXJhdGlvbj86IG51bWJlcik6IFByb21pc2U8VD4ge1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIC8vIENoZWNrIGNhY2hlIGZvciBHRVQgcmVxdWVzdHNcclxuICAgICAgICAgICAgaWYgKHVzZUNhY2hlICYmIGNvbmZpZy5tZXRob2Q/LnRvTG93ZXJDYXNlKCkgPT09ICdnZXQnKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBjYWNoZUtleSA9IHRoaXMuY2FjaGUuZ2VuZXJhdGVLZXkoY29uZmlnKTtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGNhY2hlZERhdGEgPSB0aGlzLmNhY2hlLmdldChjYWNoZUtleSk7XHJcblxyXG4gICAgICAgICAgICAgICAgaWYgKGNhY2hlZERhdGEpIHtcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gY2FjaGVkRGF0YTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmNsaWVudC5yZXF1ZXN0PFQ+KGNvbmZpZyk7XHJcblxyXG4gICAgICAgICAgICAvLyBDYWNoZSBzdWNjZXNzZnVsIEdFVCByZXNwb25zZXNcclxuICAgICAgICAgICAgaWYgKHVzZUNhY2hlICYmIGNvbmZpZy5tZXRob2Q/LnRvTG93ZXJDYXNlKCkgPT09ICdnZXQnICYmIHJlc3BvbnNlLnN0YXR1cyA9PT0gMjAwKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBjYWNoZUtleSA9IHRoaXMuY2FjaGUuZ2VuZXJhdGVLZXkoY29uZmlnKTtcclxuICAgICAgICAgICAgICAgIHRoaXMuY2FjaGUuc2V0KGNhY2hlS2V5LCByZXNwb25zZS5kYXRhLCBjYWNoZUR1cmF0aW9uKTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3I6IHVua25vd24pIHtcclxuICAgICAgICAgICAgdGhyb3cgdGhpcy5oYW5kbGVFcnJvcihlcnJvcik7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIENhY2hlZCBHRVQgcmVxdWVzdFxyXG4gICAgYXN5bmMgZ2V0PFQ+KHVybDogc3RyaW5nLCBwYXJhbXM/OiBhbnksIHVzZUNhY2hlOiBib29sZWFuID0gdHJ1ZSwgY2FjaGVEdXJhdGlvbj86IG51bWJlcik6IFByb21pc2U8VD4ge1xyXG4gICAgICAgIHJldHVybiB0aGlzLnJlcXVlc3Q8VD4oe1xyXG4gICAgICAgICAgICBtZXRob2Q6ICdHRVQnLFxyXG4gICAgICAgICAgICB1cmwsXHJcbiAgICAgICAgICAgIHBhcmFtc1xyXG4gICAgICAgIH0sIHVzZUNhY2hlLCBjYWNoZUR1cmF0aW9uKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBQT1NUIHJlcXVlc3QgKG5vIGNhY2hpbmcpXHJcbiAgICBhc3luYyBwb3N0PFQ+KHVybDogc3RyaW5nLCBkYXRhPzogYW55KTogUHJvbWlzZTxUPiB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMucmVxdWVzdDxUPih7XHJcbiAgICAgICAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICAgICAgICB1cmwsXHJcbiAgICAgICAgICAgIGRhdGFcclxuICAgICAgICB9LCBmYWxzZSk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gUFVUIHJlcXVlc3QgKG5vIGNhY2hpbmcpXHJcbiAgICBhc3luYyBwdXQ8VD4odXJsOiBzdHJpbmcsIGRhdGE/OiBhbnkpOiBQcm9taXNlPFQ+IHtcclxuICAgICAgICByZXR1cm4gdGhpcy5yZXF1ZXN0PFQ+KHtcclxuICAgICAgICAgICAgbWV0aG9kOiAnUFVUJyxcclxuICAgICAgICAgICAgdXJsLFxyXG4gICAgICAgICAgICBkYXRhXHJcbiAgICAgICAgfSwgZmFsc2UpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIERFTEVURSByZXF1ZXN0IChubyBjYWNoaW5nKVxyXG4gICAgYXN5bmMgZGVsZXRlPFQ+KHVybDogc3RyaW5nKTogUHJvbWlzZTxUPiB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMucmVxdWVzdDxUPih7XHJcbiAgICAgICAgICAgIG1ldGhvZDogJ0RFTEVURScsXHJcbiAgICAgICAgICAgIHVybFxyXG4gICAgICAgIH0sIGZhbHNlKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBDbGVhciBjYWNoZVxyXG4gICAgY2xlYXJDYWNoZShwYXR0ZXJuPzogc3RyaW5nKTogdm9pZCB7XHJcbiAgICAgICAgdGhpcy5jYWNoZS5jbGVhcihwYXR0ZXJuKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBIYW5kbGUgQVBJIGVycm9ycyB3aXRoIGVuaGFuY2VkIGRpYWdub3N0aWNzXHJcbiAgICBwcml2YXRlIGhhbmRsZUVycm9yKGVycm9yOiB1bmtub3duKTogRXJyb3Ige1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0FQSSBDbGllbnQgRXJyb3I6JywgZXJyb3IpO1xyXG5cclxuICAgICAgICBpZiAoZXJyb3IgJiYgdHlwZW9mIGVycm9yID09PSAnb2JqZWN0JyAmJiAncmVzcG9uc2UnIGluIGVycm9yKSB7XHJcbiAgICAgICAgICAgIC8vIFNlcnZlciByZXNwb25kZWQgd2l0aCBlcnJvciBzdGF0dXNcclxuICAgICAgICAgICAgY29uc3QgeyBzdGF0dXMsIGRhdGEgfSA9IChlcnJvciBhcyBFcnJvcldpdGhSZXNwb25zZSkucmVzcG9uc2U7XHJcblxyXG4gICAgICAgICAgICAvLyBQcmVmZXIgc2VydmVyLXByb3ZpZGVkIGRldGFpbCB3aGVuIGF2YWlsYWJsZSBzbyB0aGUgVUkgY2FuIHNob3cgcHJlY2lzZSBmZWVkYmFja1xyXG4gICAgICAgICAgICBjb25zdCBzZXJ2ZXJNZXNzYWdlID0gZGF0YT8uZGV0YWlsO1xyXG5cclxuICAgICAgICAgICAgc3dpdGNoIChzdGF0dXMpIHtcclxuICAgICAgICAgICAgICAgIGNhc2UgNDAwOlxyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBuZXcgRXJyb3Ioc2VydmVyTWVzc2FnZSB8fCAnQmFkIHJlcXVlc3QnKTtcclxuICAgICAgICAgICAgICAgIGNhc2UgNDAxOlxyXG4gICAgICAgICAgICAgICAgICAgIC8vIFBhc3MgdGhyb3VnaCBtZXNzYWdlcyBzdWNoIGFzIFwiQWNjb3VudCBpcyBsb2NrZWRcIiB3aGVuIHN1cHBsaWVkXHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG5ldyBFcnJvcihzZXJ2ZXJNZXNzYWdlIHx8ICdVbmF1dGhvcml6ZWQgLSBwbGVhc2UgbG9naW4gYWdhaW4nKTtcclxuICAgICAgICAgICAgICAgIGNhc2UgNDAzOlxyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBuZXcgRXJyb3Ioc2VydmVyTWVzc2FnZSB8fCAnQWNjZXNzIGRlbmllZCAtIGluc3VmZmljaWVudCBwZXJtaXNzaW9ucycpO1xyXG4gICAgICAgICAgICAgICAgY2FzZSA0MDQ6XHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG5ldyBFcnJvcihzZXJ2ZXJNZXNzYWdlIHx8ICdSZXNvdXJjZSBub3QgZm91bmQnKTtcclxuICAgICAgICAgICAgICAgIGNhc2UgNDIyOlxyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBuZXcgRXJyb3Ioc2VydmVyTWVzc2FnZSB8fCAnVmFsaWRhdGlvbiBlcnJvcicpO1xyXG4gICAgICAgICAgICAgICAgY2FzZSA0Mjk6XHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG5ldyBFcnJvcihzZXJ2ZXJNZXNzYWdlIHx8ICdUb28gbWFueSByZXF1ZXN0cyAtIHBsZWFzZSB0cnkgYWdhaW4gbGF0ZXInKTtcclxuICAgICAgICAgICAgICAgIGNhc2UgNTAwOlxyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBuZXcgRXJyb3Ioc2VydmVyTWVzc2FnZSB8fCAnSW50ZXJuYWwgc2VydmVyIGVycm9yJyk7XHJcbiAgICAgICAgICAgICAgICBkZWZhdWx0OlxyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBuZXcgRXJyb3Ioc2VydmVyTWVzc2FnZSB8fCAnQW4gZXJyb3Igb2NjdXJyZWQnKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0gZWxzZSBpZiAoZXJyb3IgJiYgdHlwZW9mIGVycm9yID09PSAnb2JqZWN0JyAmJiAncmVxdWVzdCcgaW4gZXJyb3IpIHtcclxuICAgICAgICAgICAgLy8gTmV0d29yayBlcnJvciAtIHByb3ZpZGUgbW9yZSBkZXRhaWxlZCBkaWFnbm9zdGljc1xyXG4gICAgICAgICAgICBjb25zdCBheGlvc0Vycm9yID0gZXJyb3IgYXMgYW55O1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdOZXR3b3JrIEVycm9yIERldGFpbHM6Jywge1xyXG4gICAgICAgICAgICAgICAgY29kZTogYXhpb3NFcnJvci5jb2RlLFxyXG4gICAgICAgICAgICAgICAgbWVzc2FnZTogYXhpb3NFcnJvci5tZXNzYWdlLFxyXG4gICAgICAgICAgICAgICAgY29uZmlnOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgdXJsOiBheGlvc0Vycm9yLmNvbmZpZz8udXJsLFxyXG4gICAgICAgICAgICAgICAgICAgIG1ldGhvZDogYXhpb3NFcnJvci5jb25maWc/Lm1ldGhvZCxcclxuICAgICAgICAgICAgICAgICAgICBiYXNlVVJMOiBheGlvc0Vycm9yLmNvbmZpZz8uYmFzZVVSTCxcclxuICAgICAgICAgICAgICAgICAgICB0aW1lb3V0OiBheGlvc0Vycm9yLmNvbmZpZz8udGltZW91dFxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgICAgIC8vIFByb3ZpZGUgc3BlY2lmaWMgZXJyb3IgbWVzc2FnZXMgYmFzZWQgb24gZXJyb3IgY29kZVxyXG4gICAgICAgICAgICBpZiAoYXhpb3NFcnJvci5jb2RlID09PSAnRUNPTk5SRUZVU0VEJykge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIG5ldyBFcnJvcignQ29ubmVjdGlvbiByZWZ1c2VkIC0gQmFja2VuZCBzZXJ2ZXIgbWF5IG5vdCBiZSBydW5uaW5nLiBQbGVhc2UgY2hlY2sgaWYgdGhlIHNlcnZlciBpcyBzdGFydGVkIG9uIGh0dHA6Ly9sb2NhbGhvc3Q6ODAwMCcpO1xyXG4gICAgICAgICAgICB9IGVsc2UgaWYgKGF4aW9zRXJyb3IuY29kZSA9PT0gJ0VOT1RGT1VORCcpIHtcclxuICAgICAgICAgICAgICAgIHJldHVybiBuZXcgRXJyb3IoJ1NlcnZlciBub3QgZm91bmQgLSBQbGVhc2UgY2hlY2sgdGhlIEFQSSBVUkwgY29uZmlndXJhdGlvbicpO1xyXG4gICAgICAgICAgICB9IGVsc2UgaWYgKGF4aW9zRXJyb3IuY29kZSA9PT0gJ0VUSU1FRE9VVCcpIHtcclxuICAgICAgICAgICAgICAgIHJldHVybiBuZXcgRXJyb3IoJ1JlcXVlc3QgdGltZW91dCAtIFNlcnZlciBpcyB0YWtpbmcgdG9vIGxvbmcgdG8gcmVzcG9uZCcpO1xyXG4gICAgICAgICAgICB9IGVsc2UgaWYgKGF4aW9zRXJyb3IuY29kZSA9PT0gJ0VDT05OQUJPUlRFRCcpIHtcclxuICAgICAgICAgICAgICAgIHJldHVybiBuZXcgRXJyb3IoJ1JlcXVlc3QgYWJvcnRlZCAtIENvbm5lY3Rpb24gd2FzIHRlcm1pbmF0ZWQnKTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIHJldHVybiBuZXcgRXJyb3IoYE5ldHdvcmsgZXJyb3IgKCR7YXhpb3NFcnJvci5jb2RlIHx8ICdVTktOT1dOJ30pIC0gUGxlYXNlIGNoZWNrIHlvdXIgY29ubmVjdGlvbiBhbmQgZW5zdXJlIHRoZSBiYWNrZW5kIHNlcnZlciBpcyBydW5uaW5nYCk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAvLyBPdGhlciBlcnJvclxyXG4gICAgICAgICAgICBjb25zdCBtZXNzYWdlID0gZXJyb3IgJiYgdHlwZW9mIGVycm9yID09PSAnb2JqZWN0JyAmJiAnbWVzc2FnZScgaW4gZXJyb3JcclxuICAgICAgICAgICAgICAgID8gU3RyaW5nKChlcnJvciBhcyBFcnJvcldpdGhNZXNzYWdlKS5tZXNzYWdlKVxyXG4gICAgICAgICAgICAgICAgOiAnQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZCc7XHJcbiAgICAgICAgICAgIHJldHVybiBuZXcgRXJyb3IobWVzc2FnZSk7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIFRva2VuIG1hbmFnZW1lbnRcclxuICAgIHNldFRva2VucyhhY2Nlc3NUb2tlbjogc3RyaW5nLCByZWZyZXNoVG9rZW46IHN0cmluZykge1xyXG4gICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnYWNjZXNzX3Rva2VuJywgYWNjZXNzVG9rZW4pO1xyXG4gICAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgncmVmcmVzaF90b2tlbicsIHJlZnJlc2hUb2tlbik7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGNsZWFyVG9rZW5zKCkge1xyXG4gICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnYWNjZXNzX3Rva2VuJyk7XHJcbiAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdyZWZyZXNoX3Rva2VuJyk7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGdldEFjY2Vzc1Rva2VuKCk6IHN0cmluZyB8IG51bGwge1xyXG4gICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICAgICAgICByZXR1cm4gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2FjY2Vzc190b2tlbicpO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgIH1cclxuXHJcbiAgICBnZXRSZWZyZXNoVG9rZW4oKTogc3RyaW5nIHwgbnVsbCB7XHJcbiAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgICAgICAgIHJldHVybiBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgncmVmcmVzaF90b2tlbicpO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgIH1cclxufVxyXG5cclxuLy8gRXhwb3J0IHNpbmdsZXRvbiBpbnN0YW5jZVxyXG5leHBvcnQgY29uc3QgYXBpQ2xpZW50SW5zdGFuY2UgPSBuZXcgQXBpQ2xpZW50KCk7XHJcbmV4cG9ydCBkZWZhdWx0IGFwaUNsaWVudEluc3RhbmNlOyAiXSwibmFtZXMiOlsiYXhpb3MiLCJBUElfQkFTRV9VUkwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQVBJX1VSTCIsIkFQSV9WRVJTSU9OIiwiQ0FDSEVfRFVSQVRJT04iLCJDQUNIRV9QUkVGSVgiLCJDYWNoZU1hbmFnZXIiLCJnZXRJbnN0YW5jZSIsImluc3RhbmNlIiwic2V0Iiwia2V5IiwiZGF0YSIsImR1cmF0aW9uIiwiZXhwaXJlc0F0IiwiRGF0ZSIsIm5vdyIsImNhY2hlIiwidGltZXN0YW1wIiwibG9jYWxTdG9yYWdlIiwic2V0SXRlbSIsIkpTT04iLCJzdHJpbmdpZnkiLCJlcnJvciIsImNvbnNvbGUiLCJ3YXJuIiwiZ2V0IiwibWVtb3J5RW50cnkiLCJzdG9yZWQiLCJnZXRJdGVtIiwiZW50cnkiLCJwYXJzZSIsInJlbW92ZUl0ZW0iLCJkZWxldGUiLCJjbGVhciIsInBhdHRlcm4iLCJrZXlzIiwiaW5jbHVkZXMiLCJpIiwibGVuZ3RoIiwic3RhcnRzV2l0aCIsImdlbmVyYXRlS2V5IiwiY29uZmlnIiwibWV0aG9kIiwidXJsIiwicGFyYW1zIiwidG9VcHBlckNhc2UiLCJNYXAiLCJhcGlDbGllbnQiLCJjcmVhdGUiLCJiYXNlVVJMIiwidGltZW91dCIsIndpdGhDcmVkZW50aWFscyIsImhlYWRlcnMiLCJpbnRlcmNlcHRvcnMiLCJyZXF1ZXN0IiwidXNlIiwidG9rZW4iLCJBdXRob3JpemF0aW9uIiwiUHJvbWlzZSIsInJlamVjdCIsInJlc3BvbnNlIiwib3JpZ2luYWxSZXF1ZXN0Iiwic3RhdHVzIiwiX3JldHJ5IiwicmVmcmVzaFRva2VuIiwicG9zdCIsInJlZnJlc2hfdG9rZW4iLCJhY2Nlc3NfdG9rZW4iLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhyZWYiLCJBcGlDbGllbnQiLCJ1c2VDYWNoZSIsImNhY2hlRHVyYXRpb24iLCJ0b0xvd2VyQ2FzZSIsImNhY2hlS2V5IiwiY2FjaGVkRGF0YSIsImNsaWVudCIsImhhbmRsZUVycm9yIiwicHV0IiwiY2xlYXJDYWNoZSIsInNlcnZlck1lc3NhZ2UiLCJkZXRhaWwiLCJFcnJvciIsImF4aW9zRXJyb3IiLCJjb2RlIiwibWVzc2FnZSIsIlN0cmluZyIsInNldFRva2VucyIsImFjY2Vzc1Rva2VuIiwiY2xlYXJUb2tlbnMiLCJnZXRBY2Nlc3NUb2tlbiIsImdldFJlZnJlc2hUb2tlbiIsImNvbnN0cnVjdG9yIiwiYXBpQ2xpZW50SW5zdGFuY2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api-client.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"99ce308f1d5d\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcbmV4dGpzXFx3ZWJhcHBcXGZyb250ZW5kXFxzcmNcXHN0eWxlc1xcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5OWNlMzA4ZjFkNWRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});