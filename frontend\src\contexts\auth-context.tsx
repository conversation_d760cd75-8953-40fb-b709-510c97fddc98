"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
  useCallback,
} from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import {
  authService,
  UserProfile,
  JWTPayload,
  LoginRequest,
  RegisterRequest,
} from "@/lib/auth-service";

// Authentication Context Types
interface AuthContextType {
  // State
  user: UserProfile | null;
  userFromToken: JWTPayload | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  isInitialized: boolean;

  // Actions
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  logout: (sessionId?: string, logoutAll?: boolean) => Promise<void>;
  refreshUser: () => Promise<void>;
  clearAuth: () => void;

  // OAuth
  hasOAuthProvider: (provider: string) => boolean;
  getOAuthProviders: () => Promise<Record<string, string>>;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider props
interface AuthProviderProps {
  children: ReactNode;
}

// Idle logout timeout – configurable via env var, defaults to 30 minutes
const IDLE_LOGOUT_MINUTES = parseInt(
  process.env.NEXT_PUBLIC_IDLE_LOGOUT_MINUTES ?? "30",
  10
);
const IDLE_LOGOUT_MS = IDLE_LOGOUT_MINUTES * 60_000;

// Authentication Provider Component
export function AuthProvider({ children }: AuthProviderProps) {
  const router = useRouter();

  // State
  const [user, setUser] = useState<UserProfile | null>(null);
  const [userFromToken, setUserFromToken] = useState<JWTPayload | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);

  // Reactive authentication state
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // -------------------
  // Proactive token refresh + idle logout
  // -------------------
  const refreshTimeoutRef = React.useRef<ReturnType<typeof setTimeout> | null>(
    null
  );
  const idleTimeoutRef = React.useRef<ReturnType<typeof setTimeout> | null>(
    null
  );

  const clearTimers = () => {
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
      refreshTimeoutRef.current = null;
    }
    if (idleTimeoutRef.current) {
      clearTimeout(idleTimeoutRef.current);
      idleTimeoutRef.current = null;
    }
  };

  // Logout function (moved here so it's defined before use in other hooks)
  const logout = React.useCallback(
    async (sessionId?: string, logoutAll: boolean = false) => {
      // Ensure any scheduled tasks are cancelled BEFORE state updates
      clearTimers();

      // Clear authentication state synchronously so UI can react immediately
      setIsAuthenticated(false);
      setUser(null);
      setUserFromToken(null);

      // Persisted auth data
      authService.clearAuth();

      // Attempt server-side logout *after* local cleanup so we don't block UI
      const shouldCallApiLogout =
        authService.isAuthenticated() && !authService.isTokenExpired();

      if (shouldCallApiLogout) {
        authService.logout(sessionId, logoutAll).catch(console.warn);
      }

      // Use Next.js router navigation rather than full page reload for smoother UX
      router.replace("/login");
    },
    [router]
  );

  const scheduleTokenRefresh = React.useCallback(() => {
    if (!authService.isAuthenticated()) return;
    const expDate = authService.getTokenExpiration();
    if (!expDate) return;
    const now = new Date();
    // Refresh 60 seconds before expiry (or immediately if already <1 min)
    const msUntilRefresh = Math.max(
      expDate.getTime() - now.getTime() - 60_000,
      0
    );
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }
    refreshTimeoutRef.current = setTimeout(async () => {
      try {
        await authService.refreshToken();
        scheduleTokenRefresh(); // reschedule for next expiry
      } catch (err) {
        console.error("Token refresh failed", err);
        await logout();
      }
    }, msUntilRefresh);
  }, [logout]);

  const resetIdleTimer = React.useCallback(() => {
    if (idleTimeoutRef.current) {
      clearTimeout(idleTimeoutRef.current);
    }
    idleTimeoutRef.current = setTimeout(() => {
      logout();
    }, IDLE_LOGOUT_MS);
  }, [logout]);

  // ----------------------------------------------
  // Setup timers only when authentication *state* changes
  // ----------------------------------------------
  React.useEffect(() => {
    if (!isAuthenticated) {
      // User just logged out – ensure timers are cleared
      clearTimers();
      return;
    }

    // User is authenticated – start / resume timers **once**
    scheduleTokenRefresh();
    resetIdleTimer();

    // Attach user-activity listeners to reset the idle timer
    const events = [
      "mousemove",
      "keydown",
      "click",
      // Removing programmatic scroll events avoids accidental resets
      //'scroll',
      "touchstart",
    ] as const;

    events.forEach((e) => window.addEventListener(e, resetIdleTimer));

    // Cleanup when component unmounts or auth state changes
    return () => {
      events.forEach((e) => window.removeEventListener(e, resetIdleTimer));
      clearTimers();
    };
  }, [isAuthenticated, scheduleTokenRefresh, resetIdleTimer]);

  // Initialize authentication state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        setIsLoading(true);

        // Check if user is authenticated from token
        if (authService.isAuthenticated()) {
          const tokenUser = authService.getCurrentUserFromToken();
          setUserFromToken(tokenUser);

          // Try to fetch full user profile
          try {
            const userProfile = await authService.getCurrentUser();
            setUser(userProfile);
          } catch (error) {
            console.warn("Failed to fetch user profile during initialization:", error);
            // Don't clear auth if profile fetch fails - user can still use token-based auth
            // Only clear if it's a 401/403 error that indicates invalid token
            if (error && typeof error === "object" && "response" in error) {
              const status = (error as { response?: { status: number } }).response?.status;
              if (status === 401 || status === 403) {
                authService.clearAuth();
                setUser(null);
                setUserFromToken(null);
              }
            }
          }
        } else {
          // Clear any stale data
          authService.clearAuth();
          setUser(null);
          setUserFromToken(null);
        }
      } catch (error) {
        console.error("Auth initialization error:", error);
        authService.clearAuth();
        setUser(null);
        setUserFromToken(null);
      } finally {
        setIsLoading(false);
        setIsInitialized(true);
      }
    };

    initializeAuth();
  }, []);

  // Refresh user data
  const refreshUser = useCallback(async () => {
    try {
      // Debug statements removed for production

      // Force check auth state first
      const currentAuthState = authService.isAuthenticated();
      setIsAuthenticated(currentAuthState);

      if (!currentAuthState) {
        //
        return;
      }

      // Try to get user profile, but don't fail if it doesn't work
      try {
        const userProfile = await authService.getCurrentUser();
        setUser(userProfile);
      } catch (profileError) {
        console.warn("Failed to refresh user profile:", profileError);
        // Keep existing user data if profile fetch fails
      }

      // Update token user as well
      const tokenUser = authService.getCurrentUserFromToken();
      setUserFromToken(tokenUser);

      //
    } catch (error) {
      console.error("Failed to refresh user:", error);
      console.error("Error details:", {
        message: error instanceof Error ? error.message : "Unknown error",
        status:
          error && typeof error === "object" && "response" in error
            ? (error as { response?: { status: number } }).response?.status
            : "No status",
      });
      // If refresh fails, user might be logged out
      logout();
    }
  }, [logout]);

  // Login function
  const login = async (credentials: LoginRequest) => {
    try {
      setIsLoading(true);

      await authService.login(credentials);

      // Get user profile - handle gracefully if it fails
      try {
        const userProfile = await authService.getCurrentUser();
        setUser(userProfile);
      } catch (profileError) {
        console.warn("Failed to get user profile after login:", profileError);
        // Don't fail the login if profile fetch fails
        // The user can still access the app with token-based auth
      }

      const tokenUser = authService.getCurrentUserFromToken();
      setUserFromToken(tokenUser);

      toast.success("Login successful!");

      // Redirect to dashboard - role-based rendering will be handled by the dashboard page
      router.push("/dashboard");
    } catch (error: unknown) {
      console.error("Login error:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Login failed";
      toast.error(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Register function
  const register = async (userData: RegisterRequest) => {
    try {
      setIsLoading(true);

      const response = await authService.register(userData);

      toast.success(
        response.message ||
          "Registration successful! Please check your email to verify your account."
      );

      // Redirect to login page
      router.push("/login");
    } catch (error: unknown) {
      console.error("Registration error:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Registration failed";
      toast.error(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Clear authentication data
  const clearAuth = () => {
    setUser(null);
    setUserFromToken(null);
    authService.clearAuth();
  };

  // Check if user has OAuth provider linked
  const hasOAuthProvider = (provider: string): boolean => {
    return authService.hasOAuthProvider(provider);
  };

  // Get OAuth providers list
  const getOAuthProviders = async (): Promise<Record<string, string>> => {
    try {
      return await authService.getOAuthProviders();
    } catch (error) {
      console.error("Failed to get OAuth providers:", error);
      return {};
    }
  };

  // Update authentication state when tokens change
  useEffect(() => {
    const updateAuthState = () => {
      const authState = authService.isAuthenticated();
      //
      setIsAuthenticated(authState);
    };

    // Initial check
    updateAuthState();

    // Listen for storage changes (when tokens are added/removed)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "access_token" || e.key === "refresh_token") {
        //
        updateAuthState();
      }
    };

    window.addEventListener("storage", handleStorageChange);

    // Also check periodically for token expiration
    const interval = setInterval(updateAuthState, 5000);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
      clearInterval(interval);
    };
  }, []);

  // Context value
  const value: AuthContextType = {
    user,
    userFromToken,
    isAuthenticated,
    isLoading,
    isInitialized,
    login,
    register,
    logout,
    refreshUser,
    clearAuth,
    hasOAuthProvider,
    getOAuthProviders,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Custom hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);

  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }

  return context;
}

// Export the context for advanced usage
export { AuthContext };
