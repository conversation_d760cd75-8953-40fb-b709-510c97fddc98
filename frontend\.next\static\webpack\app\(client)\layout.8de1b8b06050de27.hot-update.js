"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(client)/layout",{

/***/ "(app-pages-browser)/./src/lib/api-client.ts":
/*!*******************************!*\
  !*** ./src/lib/api-client.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   apiClientInstance: () => (/* binding */ apiClientInstance),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\n// API Configuration\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\nconst API_VERSION = '/api/v1';\n// Cache configuration\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds\nconst CACHE_PREFIX = 'api_cache_';\n// Cache utility functions\nclass CacheManager {\n    static getInstance() {\n        if (!CacheManager.instance) {\n            CacheManager.instance = new CacheManager();\n        }\n        return CacheManager.instance;\n    }\n    set(key, data) {\n        let duration = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : CACHE_DURATION;\n        const expiresAt = Date.now() + duration;\n        this.cache.set(key, {\n            data,\n            timestamp: Date.now(),\n            expiresAt\n        });\n        // Also store in localStorage for persistence across page reloads\n        if (true) {\n            try {\n                localStorage.setItem(\"\".concat(CACHE_PREFIX).concat(key), JSON.stringify({\n                    data,\n                    timestamp: Date.now(),\n                    expiresAt\n                }));\n            } catch (error) {\n                console.warn('Failed to store cache in localStorage:', error);\n            }\n        }\n    }\n    get(key) {\n        // Check memory cache first\n        const memoryEntry = this.cache.get(key);\n        if (memoryEntry && memoryEntry.expiresAt > Date.now()) {\n            return memoryEntry.data;\n        }\n        // Check localStorage\n        if (true) {\n            try {\n                const stored = localStorage.getItem(\"\".concat(CACHE_PREFIX).concat(key));\n                if (stored) {\n                    const entry = JSON.parse(stored);\n                    if (entry.expiresAt > Date.now()) {\n                        // Update memory cache\n                        this.cache.set(key, entry);\n                        return entry.data;\n                    } else {\n                        // Remove expired entry\n                        localStorage.removeItem(\"\".concat(CACHE_PREFIX).concat(key));\n                    }\n                }\n            } catch (error) {\n                console.warn('Failed to retrieve cache from localStorage:', error);\n            }\n        }\n        return null;\n    }\n    delete(key) {\n        this.cache.delete(key);\n        if (true) {\n            localStorage.removeItem(\"\".concat(CACHE_PREFIX).concat(key));\n        }\n    }\n    clear(pattern) {\n        if (pattern) {\n            // Clear specific pattern\n            for (const key of this.cache.keys()){\n                if (key.includes(pattern)) {\n                    this.cache.delete(key);\n                }\n            }\n            if (true) {\n                for(let i = localStorage.length - 1; i >= 0; i--){\n                    const key = localStorage.key(i);\n                    if (key && key.startsWith(CACHE_PREFIX) && key.includes(pattern)) {\n                        localStorage.removeItem(key);\n                    }\n                }\n            }\n        } else {\n            // Clear all cache\n            this.cache.clear();\n            if (true) {\n                for(let i = localStorage.length - 1; i >= 0; i--){\n                    const key = localStorage.key(i);\n                    if (key && key.startsWith(CACHE_PREFIX)) {\n                        localStorage.removeItem(key);\n                    }\n                }\n            }\n        }\n    }\n    generateKey(config) {\n        const { method, url, params, data } = config;\n        return \"\".concat(method === null || method === void 0 ? void 0 : method.toUpperCase(), \"_\").concat(url, \"_\").concat(JSON.stringify(params), \"_\").concat(JSON.stringify(data));\n    }\n    constructor(){\n        this.cache = new Map();\n    }\n}\n// Create axios instance\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: \"\".concat(API_BASE_URL).concat(API_VERSION),\n    timeout: 30000,\n    withCredentials: false,\n    headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n    }\n});\n// Request interceptor to add auth token and debug logging\napiClient.interceptors.request.use((config)=>{\n    // Debug logging\n    if (true) {\n        var _config_method;\n        console.log('API Request:', {\n            method: (_config_method = config.method) === null || _config_method === void 0 ? void 0 : _config_method.toUpperCase(),\n            url: config.url,\n            baseURL: config.baseURL,\n            fullURL: \"\".concat(config.baseURL).concat(config.url),\n            headers: config.headers,\n            data: config.data\n        });\n    }\n    // Get token from localStorage or cookies\n    const token =  true ? localStorage.getItem('access_token') : 0;\n    if (token) {\n        config.headers.Authorization = \"Bearer \".concat(token);\n    }\n    return config;\n}, (error)=>{\n    console.error('API Request Error:', error);\n    return Promise.reject(error);\n});\n// Response interceptor for token refresh and error handling\napiClient.interceptors.response.use((response)=>{\n    return response;\n}, async (error)=>{\n    var _error_response;\n    const originalRequest = error.config;\n    // Handle 401 Unauthorized - try to refresh token\n    if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        try {\n            const refreshToken =  true ? localStorage.getItem('refresh_token') : 0;\n            if (refreshToken) {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(API_BASE_URL).concat(API_VERSION, \"/auth/refresh\"), {\n                    refresh_token: refreshToken\n                });\n                const { access_token, refresh_token } = response.data;\n                // Store new tokens\n                if (true) {\n                    localStorage.setItem('access_token', access_token);\n                    localStorage.setItem('refresh_token', refresh_token);\n                }\n                // Retry original request with new token\n                originalRequest.headers.Authorization = \"Bearer \".concat(access_token);\n                return apiClient(originalRequest);\n            }\n        } catch (e) {\n            // Refresh failed - clear tokens and redirect to login\n            if (true) {\n                localStorage.removeItem('access_token');\n                localStorage.removeItem('refresh_token');\n                window.location.href = '/login';\n            }\n        }\n    }\n    return Promise.reject(error);\n});\n// API Client class with typed methods and caching\nclass ApiClient {\n    // Generic request method with caching\n    async request(config) {\n        let useCache = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false, cacheDuration = arguments.length > 2 ? arguments[2] : void 0;\n        try {\n            var _config_method, _config_method1;\n            // Check cache for GET requests\n            if (useCache && ((_config_method = config.method) === null || _config_method === void 0 ? void 0 : _config_method.toLowerCase()) === 'get') {\n                const cacheKey = this.cache.generateKey(config);\n                const cachedData = this.cache.get(cacheKey);\n                if (cachedData) {\n                    return cachedData;\n                }\n            }\n            const response = await this.client.request(config);\n            // Cache successful GET responses\n            if (useCache && ((_config_method1 = config.method) === null || _config_method1 === void 0 ? void 0 : _config_method1.toLowerCase()) === 'get' && response.status === 200) {\n                const cacheKey = this.cache.generateKey(config);\n                this.cache.set(cacheKey, response.data, cacheDuration);\n            }\n            return response.data;\n        } catch (error) {\n            throw this.handleError(error);\n        }\n    }\n    // Cached GET request\n    async get(url, params) {\n        let useCache = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true, cacheDuration = arguments.length > 3 ? arguments[3] : void 0;\n        return this.request({\n            method: 'GET',\n            url,\n            params\n        }, useCache, cacheDuration);\n    }\n    // POST request (no caching)\n    async post(url, data) {\n        return this.request({\n            method: 'POST',\n            url,\n            data\n        }, false);\n    }\n    // PUT request (no caching)\n    async put(url, data) {\n        return this.request({\n            method: 'PUT',\n            url,\n            data\n        }, false);\n    }\n    // DELETE request (no caching)\n    async delete(url) {\n        return this.request({\n            method: 'DELETE',\n            url\n        }, false);\n    }\n    // Clear cache\n    clearCache(pattern) {\n        this.cache.clear(pattern);\n    }\n    // Test connection to backend\n    async testConnection() {\n        try {\n            console.log('Testing connection to:', \"\".concat(this.client.defaults.baseURL, \"/health\"));\n            const response = await this.client.get('/health', {\n                timeout: 10000,\n                validateStatus: ()=>true // Accept any status code\n            });\n            if (response.status === 200) {\n                return {\n                    success: true,\n                    message: 'Backend connection successful',\n                    details: response.data\n                };\n            } else {\n                return {\n                    success: false,\n                    message: \"Backend responded with status \".concat(response.status),\n                    details: response.data\n                };\n            }\n        } catch (error) {\n            console.error('Connection test failed:', error);\n            if (error.code === 'ECONNREFUSED') {\n                return {\n                    success: false,\n                    message: 'Backend server is not running. Please start the backend server on http://localhost:8000',\n                    details: {\n                        error: error.code,\n                        message: error.message\n                    }\n                };\n            } else if (error.code === 'ENOTFOUND') {\n                return {\n                    success: false,\n                    message: 'Backend server not found. Please check the API URL configuration.',\n                    details: {\n                        error: error.code,\n                        message: error.message\n                    }\n                };\n            } else {\n                return {\n                    success: false,\n                    message: \"Connection failed: \".concat(error.message),\n                    details: {\n                        error: error.code,\n                        message: error.message\n                    }\n                };\n            }\n        }\n    }\n    // Handle API errors with enhanced diagnostics\n    handleError(error) {\n        console.error('API Client Error:', error);\n        if (error && typeof error === 'object' && 'response' in error) {\n            // Server responded with error status\n            const { status, data } = error.response;\n            // Prefer server-provided detail when available so the UI can show precise feedback\n            const serverMessage = data === null || data === void 0 ? void 0 : data.detail;\n            switch(status){\n                case 400:\n                    return new Error(serverMessage || 'Bad request');\n                case 401:\n                    // Pass through messages such as \"Account is locked\" when supplied\n                    return new Error(serverMessage || 'Unauthorized - please login again');\n                case 403:\n                    return new Error(serverMessage || 'Access denied - insufficient permissions');\n                case 404:\n                    return new Error(serverMessage || 'Resource not found');\n                case 422:\n                    return new Error(serverMessage || 'Validation error');\n                case 429:\n                    return new Error(serverMessage || 'Too many requests - please try again later');\n                case 500:\n                    return new Error(serverMessage || 'Internal server error');\n                default:\n                    return new Error(serverMessage || 'An error occurred');\n            }\n        } else if (error && typeof error === 'object' && 'request' in error) {\n            var _axiosError_config, _axiosError_config1, _axiosError_config2, _axiosError_config3;\n            // Network error - provide more detailed diagnostics\n            const axiosError = error;\n            console.error('Network Error Details:', {\n                code: axiosError.code,\n                message: axiosError.message,\n                config: {\n                    url: (_axiosError_config = axiosError.config) === null || _axiosError_config === void 0 ? void 0 : _axiosError_config.url,\n                    method: (_axiosError_config1 = axiosError.config) === null || _axiosError_config1 === void 0 ? void 0 : _axiosError_config1.method,\n                    baseURL: (_axiosError_config2 = axiosError.config) === null || _axiosError_config2 === void 0 ? void 0 : _axiosError_config2.baseURL,\n                    timeout: (_axiosError_config3 = axiosError.config) === null || _axiosError_config3 === void 0 ? void 0 : _axiosError_config3.timeout\n                }\n            });\n            // Provide specific error messages based on error code\n            if (axiosError.code === 'ECONNREFUSED') {\n                return new Error('Connection refused - Backend server may not be running. Please check if the server is started on http://localhost:8000');\n            } else if (axiosError.code === 'ENOTFOUND') {\n                return new Error('Server not found - Please check the API URL configuration');\n            } else if (axiosError.code === 'ETIMEDOUT') {\n                return new Error('Request timeout - Server is taking too long to respond');\n            } else if (axiosError.code === 'ECONNABORTED') {\n                return new Error('Request aborted - Connection was terminated');\n            } else {\n                return new Error(\"Network error (\".concat(axiosError.code || 'UNKNOWN', \") - Please check your connection and ensure the backend server is running\"));\n            }\n        } else {\n            // Other error\n            const message = error && typeof error === 'object' && 'message' in error ? String(error.message) : 'An unexpected error occurred';\n            return new Error(message);\n        }\n    }\n    // Token management\n    setTokens(accessToken, refreshToken) {\n        if (true) {\n            localStorage.setItem('access_token', accessToken);\n            localStorage.setItem('refresh_token', refreshToken);\n        }\n    }\n    clearTokens() {\n        if (true) {\n            localStorage.removeItem('access_token');\n            localStorage.removeItem('refresh_token');\n        }\n    }\n    getAccessToken() {\n        if (true) {\n            return localStorage.getItem('access_token');\n        }\n        return null;\n    }\n    getRefreshToken() {\n        if (true) {\n            return localStorage.getItem('refresh_token');\n        }\n        return null;\n    }\n    constructor(){\n        this.client = apiClient;\n        this.cache = CacheManager.getInstance();\n    }\n}\n// Export singleton instance\nconst apiClientInstance = new ApiClient();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClientInstance);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api-client.ts\n"));

/***/ })

});