'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Loader2, CheckCircle, XCircle, AlertTriangle, RefreshCw } from 'lucide-react';
import { apiClientInstance } from '@/lib/api-client';
import { useCurrentOrigin } from '@/hooks/use-client-side';

interface ConnectionStatus {
    success: boolean;
    message: string;
    details?: any;
}

interface DiagnosticResult {
    test: string;
    status: 'success' | 'error' | 'warning' | 'pending';
    message: string;
    details?: any;
}

export function ConnectionDiagnostic() {
    const [isRunning, setIsRunning] = useState(false);
    const [results, setResults] = useState<DiagnosticResult[]>([]);
    const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus | null>(null);
    const currentOrigin = useCurrentOrigin();

    const diagnosticTests = [
        { name: 'Backend Connection', test: 'connection' },
        { name: 'API Health Check', test: 'health' },
        { name: 'Authentication Endpoint', test: 'auth' },
        { name: 'CORS Configuration', test: 'cors' },
    ];

    const runDiagnostics = async () => {
        setIsRunning(true);
        setResults([]);
        setConnectionStatus(null);

        // Initialize results with pending status
        const initialResults = diagnosticTests.map(test => ({
            test: test.name,
            status: 'pending' as const,
            message: 'Running...',
        }));
        setResults(initialResults);

        try {
            // Test 1: Basic connection
            await updateResult('Backend Connection', async () => {
                const result = await apiClientInstance.testConnection();
                setConnectionStatus(result);
                
                if (result.success) {
                    return { status: 'success', message: result.message, details: result.details };
                } else {
                    return { status: 'error', message: result.message, details: result.details };
                }
            });

            // Test 2: Health endpoint
            await updateResult('API Health Check', async () => {
                try {
                    const response = await apiClientInstance.get('/health');
                    return {
                        status: 'success',
                        message: 'Health endpoint accessible',
                        details: response
                    };
                } catch (error: any) {
                    return {
                        status: 'error',
                        message: `Health check failed: ${error.message}`,
                        details: error
                    };
                }
            });

            // Test 3: Auth endpoint (expect validation error)
            await updateResult('Authentication Endpoint', async () => {
                try {
                    await apiClientInstance.post('/auth/login', {
                        email_or_username: '<EMAIL>',
                        password: 'testpassword'
                    });
                    return {
                        status: 'warning',
                        message: 'Unexpected success - endpoint may have issues'
                    };
                } catch (error: any) {
                    if (error.message.includes('Validation error') || error.message.includes('Invalid credentials')) {
                        return {
                            status: 'success',
                            message: 'Auth endpoint accessible (expected validation error)',
                            details: error.message
                        };
                    } else {
                        return {
                            status: 'error',
                            message: `Auth endpoint error: ${error.message}`,
                            details: error
                        };
                    }
                }
            });

            // Test 4: CORS (simplified check)
            await updateResult('CORS Configuration', async () => {
                try {
                    // If we can make requests, CORS is likely configured correctly
                    await apiClientInstance.get('/health');
                    return {
                        status: 'success',
                        message: 'CORS appears to be configured correctly'
                    };
                } catch (error: any) {
                    if (error.message.includes('CORS')) {
                        return {
                            status: 'error',
                            message: 'CORS configuration issue detected',
                            details: error
                        };
                    } else {
                        return {
                            status: 'warning',
                            message: 'Unable to verify CORS (other connection issues present)'
                        };
                    }
                }
            });

        } catch (error) {
            console.error('Diagnostic error:', error);
        } finally {
            setIsRunning(false);
        }
    };

    const updateResult = async (testName: string, testFunction: () => Promise<any>) => {
        try {
            const result = await testFunction();
            setResults(prev => prev.map(r => 
                r.test === testName ? { ...r, ...result } : r
            ));
        } catch (error: any) {
            setResults(prev => prev.map(r => 
                r.test === testName ? {
                    ...r,
                    status: 'error',
                    message: `Test failed: ${error.message}`,
                    details: error
                } : r
            ));
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'success':
                return <CheckCircle className="h-4 w-4 text-green-500" />;
            case 'error':
                return <XCircle className="h-4 w-4 text-red-500" />;
            case 'warning':
                return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
            case 'pending':
                return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
            default:
                return null;
        }
    };

    const getStatusBadge = (status: string) => {
        const variants = {
            success: 'default',
            error: 'destructive',
            warning: 'secondary',
            pending: 'outline'
        } as const;
        
        return (
            <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
                {status.toUpperCase()}
            </Badge>
        );
    };

    // Auto-run diagnostics on component mount
    useEffect(() => {
        runDiagnostics();
    }, []);

    return (
        <Card className="w-full max-w-2xl mx-auto">
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <RefreshCw className="h-5 w-5" />
                    Connection Diagnostics
                </CardTitle>
                <CardDescription>
                    Diagnose connection issues between frontend and backend services
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                {connectionStatus && !connectionStatus.success && (
                    <Alert variant="destructive">
                        <XCircle className="h-4 w-4" />
                        <AlertDescription>
                            <strong>Connection Failed:</strong> {connectionStatus.message}
                            {connectionStatus.details?.error && (
                                <div className="mt-2 text-sm">
                                    Error Code: {connectionStatus.details.error}
                                </div>
                            )}
                        </AlertDescription>
                    </Alert>
                )}

                <div className="space-y-3">
                    {results.map((result, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="flex items-center gap-3">
                                {getStatusIcon(result.status)}
                                <div>
                                    <div className="font-medium">{result.test}</div>
                                    <div className="text-sm text-muted-foreground">{result.message}</div>
                                </div>
                            </div>
                            {getStatusBadge(result.status)}
                        </div>
                    ))}
                </div>

                <div className="flex gap-2">
                    <Button 
                        onClick={runDiagnostics} 
                        disabled={isRunning}
                        className="flex items-center gap-2"
                    >
                        {isRunning ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                            <RefreshCw className="h-4 w-4" />
                        )}
                        {isRunning ? 'Running Diagnostics...' : 'Run Diagnostics'}
                    </Button>
                </div>

                {connectionStatus?.success && (
                    <Alert>
                        <CheckCircle className="h-4 w-4" />
                        <AlertDescription>
                            <strong>Connection Successful!</strong> All systems appear to be working correctly.
                            If you're still experiencing issues, try refreshing the page or clearing your browser cache.
                        </AlertDescription>
                    </Alert>
                )}

                <div className="text-xs text-muted-foreground space-y-1">
                    <div><strong>Backend URL:</strong> {process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}</div>
                    <div><strong>Frontend URL:</strong> {currentOrigin}</div>
                </div>
            </CardContent>
        </Card>
    );
}
