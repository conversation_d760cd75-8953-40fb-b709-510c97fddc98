# Connection Issue Solutions & Fixes

## 🚨 **Problem Summary**

The error `Network error - please check your connection` indicates that the frontend cannot connect to the backend server. After analysis, I've identified and implemented several solutions.

---

## ✅ **Solutions Implemented**

### **1. Fixed API Client Configuration**

**Issue**: The API client was configured with `withCredentials: true` which can cause CORS issues.

**Fix Applied**:
```typescript
// Before
withCredentials: true, // Can cause CORS issues

// After  
withCredentials: false, // Avoid CORS issues with credentials
timeout: 30000, // Increased timeout for better reliability
```

### **2. Enhanced Error Handling**

**Issue**: Generic network error messages didn't provide specific diagnostics.

**Fix Applied**:
- Added specific error codes (ECONNREFUSED, ENOTFOUND, ETIMEDOUT)
- Detailed error messages with actionable solutions
- Enhanced logging for debugging

### **3. Added Debug Logging**

**Issue**: No visibility into API requests/responses for troubleshooting.

**Fix Applied**:
```typescript
// Request logging
if (process.env.NEXT_PUBLIC_DEBUG === 'true') {
    console.log('API Request:', {
        method: config.method?.toUpperCase(),
        url: config.url,
        fullURL: `${config.baseURL}${config.url}`,
        headers: config.headers
    });
}
```

### **4. Created Connection Test Tools**

**New Files Created**:
- `frontend/src/components/connection-diagnostic.tsx` - React component for testing connections
- `frontend/src/app/test-connection/page.tsx` - Test page for diagnosing issues
- `scripts/diagnose-connection.js` - Node.js diagnostic script
- `scripts/start-services.sh` - Linux/Mac startup script
- `scripts/start-services.bat` - Windows startup script

---

## 🔧 **How to Test the Fixes**

### **Step 1: Verify Services are Running**

**Check Backend**:
```bash
curl http://localhost:8000/health
# Should return: {"status":"healthy","version":"0.1.0"}
```

**Check Frontend**:
```bash
curl http://localhost:3000
# Should return HTML content
```

### **Step 2: Use the Test Page**

1. Navigate to: http://localhost:3000/test-connection
2. The page will automatically run connection diagnostics
3. Review the results for any issues

### **Step 3: Check Browser Console**

1. Open Developer Tools (F12)
2. Go to Console tab
3. Try to login
4. Look for detailed API request/response logs (if DEBUG=true)

### **Step 4: Manual Connection Test**

```javascript
// Test in browser console
fetch('http://localhost:8000/health')
  .then(response => response.json())
  .then(data => console.log('Backend connection:', data))
  .catch(error => console.error('Connection failed:', error));
```

---

## 🚀 **Automated Startup**

### **Windows**
```cmd
# Start all services
scripts\start-services.bat start

# Check status
scripts\start-services.bat status
```

### **Linux/Mac**
```bash
# Start all services
./scripts/start-services.sh start

# Check status  
./scripts/start-services.sh status
```

---

## 🔍 **Troubleshooting Steps**

### **If Backend is Not Running**

1. **Start Backend Manually**:
   ```cmd
   cd backend
   .venv\Scripts\activate
   uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
   ```

2. **Check for Errors**:
   - Database connection issues
   - Missing dependencies
   - Port conflicts

### **If CORS Issues Persist**

1. **Verify Backend CORS Settings**:
   ```python
   # In backend/app/main.py
   app.add_middleware(
       CORSMiddleware,
       allow_origins=["http://localhost:3000"],
       allow_credentials=True,
       allow_methods=["*"],
       allow_headers=["*"],
   )
   ```

2. **Check Environment Variables**:
   ```env
   # backend/.env
   BACKEND_CORS_ORIGINS=http://localhost:3000
   FRONTEND_BASE_URL=http://localhost:3000
   ```

### **If Connection Still Fails**

1. **Use Connection Diagnostic**:
   - Go to http://localhost:3000/test-connection
   - Run all diagnostic tests
   - Check specific error messages

2. **Check Firewall/Antivirus**:
   - Add exceptions for ports 3000 and 8000
   - Temporarily disable to test

3. **Try Different Ports**:
   ```env
   # If ports are blocked, use alternatives
   NEXT_PUBLIC_API_URL=http://localhost:8001
   ```

---

## 📋 **Quick Checklist**

- [ ] Backend server running on port 8000
- [ ] Frontend server running on port 3000
- [ ] Backend health endpoint accessible: http://localhost:8000/health
- [ ] API docs accessible: http://localhost:8000/docs
- [ ] Environment variables correctly set
- [ ] No firewall blocking connections
- [ ] CORS properly configured
- [ ] API client timeout increased to 30 seconds
- [ ] Debug logging enabled for troubleshooting

---

## 🆘 **Emergency Recovery**

If nothing else works:

1. **Complete Service Restart**:
   ```cmd
   # Stop everything
   scripts\start-services.bat stop
   
   # Wait 5 seconds
   timeout /t 5
   
   # Start everything
   scripts\start-services.bat start
   ```

2. **Reset Environment**:
   ```cmd
   # Backend
   cd backend
   rmdir /s .venv
   python -m venv .venv
   .venv\Scripts\activate
   pip install uv
   uv sync
   
   # Frontend
   cd frontend
   rmdir /s node_modules
   npm install
   ```

3. **Use Docker Fallback**:
   ```cmd
   docker-compose up --build
   ```

---

## 📊 **Verification**

After applying these fixes:

1. ✅ **Backend Health**: http://localhost:8000/health returns 200 OK
2. ✅ **API Documentation**: http://localhost:8000/docs loads correctly
3. ✅ **Frontend Loading**: http://localhost:3000 loads without errors
4. ✅ **Connection Test**: http://localhost:3000/test-connection shows all green
5. ✅ **Login Attempt**: Should now show proper validation errors instead of network errors

---

## 🎯 **Expected Results**

After implementing these fixes:

- **Network errors should be resolved**
- **Login attempts should reach the backend** (may show validation errors, which is expected)
- **Detailed error messages** instead of generic "Network error"
- **Better debugging capabilities** with console logging
- **Automated service management** with startup scripts

The connection diagnostic tools will help identify any remaining issues and provide specific solutions for your environment.

---

## 📞 **Next Steps**

1. **Test the connection** using the test page
2. **Try logging in** - should now get proper error messages
3. **Check console logs** for detailed request/response information
4. **Use startup scripts** for reliable service management

If issues persist, the diagnostic tools will provide specific error codes and solutions tailored to your environment.
