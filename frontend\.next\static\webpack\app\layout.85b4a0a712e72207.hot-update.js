"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/contexts/auth-context.tsx":
/*!***************************************!*\
  !*** ./src/contexts/auth-context.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthContext: () => (/* binding */ AuthContext),\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth-service */ \"(app-pages-browser)/./src/lib/auth-service.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,AuthContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n// Create context\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nvar _process_env_NEXT_PUBLIC_IDLE_LOGOUT_MINUTES;\n// Idle logout timeout – configurable via env var, defaults to 30 minutes\nconst IDLE_LOGOUT_MINUTES = parseInt((_process_env_NEXT_PUBLIC_IDLE_LOGOUT_MINUTES = \"30\") !== null && _process_env_NEXT_PUBLIC_IDLE_LOGOUT_MINUTES !== void 0 ? _process_env_NEXT_PUBLIC_IDLE_LOGOUT_MINUTES : \"30\", 10);\nconst IDLE_LOGOUT_MS = IDLE_LOGOUT_MINUTES * 60000;\n// Authentication Provider Component\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // State\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userFromToken, setUserFromToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Reactive authentication state\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // -------------------\n    // Proactive token refresh + idle logout\n    // -------------------\n    const refreshTimeoutRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(null);\n    const idleTimeoutRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(null);\n    const clearTimers = ()=>{\n        if (refreshTimeoutRef.current) {\n            clearTimeout(refreshTimeoutRef.current);\n            refreshTimeoutRef.current = null;\n        }\n        if (idleTimeoutRef.current) {\n            clearTimeout(idleTimeoutRef.current);\n            idleTimeoutRef.current = null;\n        }\n    };\n    // Logout function (moved here so it's defined before use in other hooks)\n    const logout = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"AuthProvider.useCallback[logout]\": async function(sessionId) {\n            let logoutAll = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            // Ensure any scheduled tasks are cancelled BEFORE state updates\n            clearTimers();\n            // Clear authentication state synchronously so UI can react immediately\n            setIsAuthenticated(false);\n            setUser(null);\n            setUserFromToken(null);\n            // Persisted auth data\n            _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.clearAuth();\n            // Attempt server-side logout *after* local cleanup so we don't block UI\n            const shouldCallApiLogout = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isAuthenticated() && !_lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isTokenExpired();\n            if (shouldCallApiLogout) {\n                _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.logout(sessionId, logoutAll).catch(console.warn);\n            }\n            // Use Next.js router navigation rather than full page reload for smoother UX\n            router.replace(\"/login\");\n        }\n    }[\"AuthProvider.useCallback[logout]\"], [\n        router\n    ]);\n    const scheduleTokenRefresh = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"AuthProvider.useCallback[scheduleTokenRefresh]\": ()=>{\n            if (!_lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isAuthenticated()) return;\n            const expDate = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getTokenExpiration();\n            if (!expDate) return;\n            const now = new Date();\n            // Refresh 60 seconds before expiry (or immediately if already <1 min)\n            const msUntilRefresh = Math.max(expDate.getTime() - now.getTime() - 60000, 0);\n            if (refreshTimeoutRef.current) {\n                clearTimeout(refreshTimeoutRef.current);\n            }\n            refreshTimeoutRef.current = setTimeout({\n                \"AuthProvider.useCallback[scheduleTokenRefresh]\": async ()=>{\n                    try {\n                        await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.refreshToken();\n                        scheduleTokenRefresh(); // reschedule for next expiry\n                    } catch (err) {\n                        console.error(\"Token refresh failed\", err);\n                        await logout();\n                    }\n                }\n            }[\"AuthProvider.useCallback[scheduleTokenRefresh]\"], msUntilRefresh);\n        }\n    }[\"AuthProvider.useCallback[scheduleTokenRefresh]\"], [\n        logout\n    ]);\n    const resetIdleTimer = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"AuthProvider.useCallback[resetIdleTimer]\": ()=>{\n            if (idleTimeoutRef.current) {\n                clearTimeout(idleTimeoutRef.current);\n            }\n            idleTimeoutRef.current = setTimeout({\n                \"AuthProvider.useCallback[resetIdleTimer]\": ()=>{\n                    logout();\n                }\n            }[\"AuthProvider.useCallback[resetIdleTimer]\"], IDLE_LOGOUT_MS);\n        }\n    }[\"AuthProvider.useCallback[resetIdleTimer]\"], [\n        logout\n    ]);\n    // ----------------------------------------------\n    // Setup timers only when authentication *state* changes\n    // ----------------------------------------------\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!isAuthenticated) {\n                // User just logged out – ensure timers are cleared\n                clearTimers();\n                return;\n            }\n            // User is authenticated – start / resume timers **once**\n            scheduleTokenRefresh();\n            resetIdleTimer();\n            // Attach user-activity listeners to reset the idle timer\n            const events = [\n                \"mousemove\",\n                \"keydown\",\n                \"click\",\n                // Removing programmatic scroll events avoids accidental resets\n                //'scroll',\n                \"touchstart\"\n            ];\n            events.forEach({\n                \"AuthProvider.useEffect\": (e)=>window.addEventListener(e, resetIdleTimer)\n            }[\"AuthProvider.useEffect\"]);\n            // Cleanup when component unmounts or auth state changes\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    events.forEach({\n                        \"AuthProvider.useEffect\": (e)=>window.removeEventListener(e, resetIdleTimer)\n                    }[\"AuthProvider.useEffect\"]);\n                    clearTimers();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        isAuthenticated,\n        scheduleTokenRefresh,\n        resetIdleTimer\n    ]);\n    // Initialize authentication state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initializeAuth = {\n                \"AuthProvider.useEffect.initializeAuth\": async function() {\n                    let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n                    try {\n                        setIsLoading(true);\n                        // Check if user is authenticated from token\n                        if (_lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isAuthenticated()) {\n                            const tokenUser = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUserFromToken();\n                            setUserFromToken(tokenUser);\n                            // Test backend connectivity before trying to fetch profile\n                            try {\n                                // Quick health check to verify backend is accessible\n                                await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.testConnection();\n                            } catch (connectionError) {\n                                console.warn(\"Backend not accessible during auth init, skipping profile fetch\");\n                                // Still set token-based auth, user can retry later\n                                return;\n                            }\n                            // Try to fetch full user profile\n                            try {\n                                const userProfile = await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUser();\n                                setUser(userProfile);\n                            } catch (error) {\n                                console.warn(\"Failed to fetch user profile during initialization:\", error);\n                                // Handle different types of errors\n                                if (error && typeof error === \"object\") {\n                                    // Check for HTTP response errors\n                                    if (\"response\" in error) {\n                                        var _error_response;\n                                        const status = (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status;\n                                        if (status === 401 || status === 403) {\n                                            // Invalid token - clear auth\n                                            console.log(\"Token is invalid, clearing auth\");\n                                            _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.clearAuth();\n                                            setUser(null);\n                                            setUserFromToken(null);\n                                        }\n                                    // For other HTTP errors (404, 500, etc.), keep token-based auth\n                                    } else if (\"message\" in error) {\n                                        const errorMessage = error.message;\n                                        if (errorMessage.includes(\"Network Error\") || errorMessage.includes(\"ECONNREFUSED\") || errorMessage.includes(\"ENOTFOUND\")) {\n                                            // Network connectivity issues - don't clear auth, user can retry later\n                                            console.warn(\"Network error during profile fetch, keeping token-based auth\");\n                                        } else {\n                                            // Other errors - log but don't clear auth\n                                            console.warn(\"Unknown error during profile fetch:\", errorMessage);\n                                        }\n                                    }\n                                }\n                            }\n                        } else {\n                            // Clear any stale data\n                            _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.clearAuth();\n                            setUser(null);\n                            setUserFromToken(null);\n                        }\n                    } catch (error) {\n                        console.error(\"Auth initialization error:\", error);\n                        _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.clearAuth();\n                        setUser(null);\n                        setUserFromToken(null);\n                    } finally{\n                        setIsLoading(false);\n                        setIsInitialized(true);\n                    }\n                }\n            }[\"AuthProvider.useEffect.initializeAuth\"];\n            initializeAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Refresh user data\n    const refreshUser = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[refreshUser]\": async ()=>{\n            try {\n                // Debug statements removed for production\n                // Force check auth state first\n                const currentAuthState = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isAuthenticated();\n                setIsAuthenticated(currentAuthState);\n                if (!currentAuthState) {\n                    //\n                    return;\n                }\n                // Try to get user profile, but don't fail if it doesn't work\n                try {\n                    const userProfile = await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUser();\n                    setUser(userProfile);\n                } catch (profileError) {\n                    console.warn(\"Failed to refresh user profile:\", profileError);\n                // Keep existing user data if profile fetch fails\n                }\n                // Update token user as well\n                const tokenUser = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUserFromToken();\n                setUserFromToken(tokenUser);\n            //\n            } catch (error) {\n                var _error_response;\n                console.error(\"Failed to refresh user:\", error);\n                console.error(\"Error details:\", {\n                    message: error instanceof Error ? error.message : \"Unknown error\",\n                    status: error && typeof error === \"object\" && \"response\" in error ? (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status : \"No status\"\n                });\n                // If refresh fails, user might be logged out\n                logout();\n            }\n        }\n    }[\"AuthProvider.useCallback[refreshUser]\"], [\n        logout\n    ]);\n    // Login function\n    const login = async (credentials)=>{\n        try {\n            setIsLoading(true);\n            await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.login(credentials);\n            // Get user profile - handle gracefully if it fails\n            try {\n                const userProfile = await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUser();\n                setUser(userProfile);\n            } catch (profileError) {\n                console.warn(\"Failed to get user profile after login:\", profileError);\n            // Don't fail the login if profile fetch fails\n            // The user can still access the app with token-based auth\n            }\n            const tokenUser = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUserFromToken();\n            setUserFromToken(tokenUser);\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Login successful!\");\n            // Redirect to dashboard - role-based rendering will be handled by the dashboard page\n            router.push(\"/dashboard\");\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            const errorMessage = error instanceof Error ? error.message : \"Login failed\";\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(errorMessage);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Register function\n    const register = async (userData)=>{\n        try {\n            setIsLoading(true);\n            const response = await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.register(userData);\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success(response.message || \"Registration successful! Please check your email to verify your account.\");\n            // Redirect to login page\n            router.push(\"/login\");\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            const errorMessage = error instanceof Error ? error.message : \"Registration failed\";\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(errorMessage);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Clear authentication data\n    const clearAuth = ()=>{\n        setUser(null);\n        setUserFromToken(null);\n        _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.clearAuth();\n    };\n    // Check if user has OAuth provider linked\n    const hasOAuthProvider = (provider)=>{\n        return _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.hasOAuthProvider(provider);\n    };\n    // Get OAuth providers list\n    const getOAuthProviders = async ()=>{\n        try {\n            return await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getOAuthProviders();\n        } catch (error) {\n            console.error(\"Failed to get OAuth providers:\", error);\n            return {};\n        }\n    };\n    // Update authentication state when tokens change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const updateAuthState = {\n                \"AuthProvider.useEffect.updateAuthState\": ()=>{\n                    const authState = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isAuthenticated();\n                    //\n                    setIsAuthenticated(authState);\n                }\n            }[\"AuthProvider.useEffect.updateAuthState\"];\n            // Initial check\n            updateAuthState();\n            // Listen for storage changes (when tokens are added/removed)\n            const handleStorageChange = {\n                \"AuthProvider.useEffect.handleStorageChange\": (e)=>{\n                    if (e.key === \"access_token\" || e.key === \"refresh_token\") {\n                        //\n                        updateAuthState();\n                    }\n                }\n            }[\"AuthProvider.useEffect.handleStorageChange\"];\n            window.addEventListener(\"storage\", handleStorageChange);\n            // Also check periodically for token expiration\n            const interval = setInterval(updateAuthState, 5000);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    window.removeEventListener(\"storage\", handleStorageChange);\n                    clearInterval(interval);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Context value\n    const value = {\n        user,\n        userFromToken,\n        isAuthenticated,\n        isLoading,\n        isInitialized,\n        login,\n        register,\n        logout,\n        refreshUser,\n        clearAuth,\n        hasOAuthProvider,\n        getOAuthProviders\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\contexts\\\\auth-context.tsx\",\n        lineNumber: 443,\n        columnNumber: 10\n    }, this);\n}\n_s(AuthProvider, \"vHRPUYhL0MK6zoFbLb0c6cGc8Pg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthProvider;\n// Custom hook to use auth context\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Export the context for advanced usage\n\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/auth-context.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2ea7bd8b2ece\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcbmV4dGpzXFx3ZWJhcHBcXGZyb250ZW5kXFxzcmNcXHN0eWxlc1xcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIyZWE3YmQ4YjJlY2VcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});