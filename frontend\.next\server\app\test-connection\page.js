/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/test-connection/page";
exports.ids = ["app/test-connection/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ftest-connection%2Fpage&page=%2Ftest-connection%2Fpage&appPaths=%2Ftest-connection%2Fpage&pagePath=private-next-app-dir%2Ftest-connection%2Fpage.tsx&appDir=D%3A%5Cnextjs%5Cwebapp%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cnextjs%5Cwebapp%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ftest-connection%2Fpage&page=%2Ftest-connection%2Fpage&appPaths=%2Ftest-connection%2Fpage&pagePath=private-next-app-dir%2Ftest-connection%2Fpage.tsx&appDir=D%3A%5Cnextjs%5Cwebapp%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cnextjs%5Cwebapp%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/test-connection/page.tsx */ \"(rsc)/./src/app/test-connection/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'test-connection',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/test-connection/page\",\n        pathname: \"/test-connection\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ftest-connection%2Fpage&page=%2Ftest-connection%2Fpage&appPaths=%2Ftest-connection%2Fpage&pagePath=private-next-app-dir%2Ftest-connection%2Fpage.tsx&appDir=D%3A%5Cnextjs%5Cwebapp%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cnextjs%5Cwebapp%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNuZXh0anMlNUMlNUN3ZWJhcHAlNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNuZXh0anMlNUMlNUN3ZWJhcHAlNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1zZWdtZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNuZXh0anMlNUMlNUN3ZWJhcHAlNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNuZXh0anMlNUMlNUN3ZWJhcHAlNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2h0dHAtYWNjZXNzLWZhbGxiYWNrJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q25leHRqcyU1QyU1Q3dlYmFwcCU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDbmV4dGpzJTVDJTVDd2ViYXBwJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNuZXh0anMlNUMlNUN3ZWJhcHAlNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q21ldGFkYXRhJTVDJTVDbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q25leHRqcyU1QyU1Q3dlYmFwcCU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb09BQStIO0FBQy9IO0FBQ0EsME9BQWtJO0FBQ2xJO0FBQ0EsME9BQWtJO0FBQ2xJO0FBQ0Esb1JBQXdKO0FBQ3hKO0FBQ0Esd09BQWlJO0FBQ2pJO0FBQ0EsNFBBQTRJO0FBQzVJO0FBQ0Esa1FBQStJO0FBQy9JO0FBQ0Esc1FBQWdKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxuZXh0anNcXFxcd2ViYXBwXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXG5leHRqc1xcXFx3ZWJhcHBcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcbmV4dGpzXFxcXHdlYmFwcFxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxuZXh0anNcXFxcd2ViYXBwXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcaHR0cC1hY2Nlc3MtZmFsbGJhY2tcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXG5leHRqc1xcXFx3ZWJhcHBcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxuZXh0anNcXFxcd2ViYXBwXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcYXN5bmMtbWV0YWRhdGEuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXG5leHRqc1xcXFx3ZWJhcHBcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxtZXRhZGF0YS1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcbmV4dGpzXFxcXHdlYmFwcFxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXHJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/theme-provider.tsx */ \"(rsc)/./src/components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/sonner.tsx */ \"(rsc)/./src/components/ui/sonner.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/auth-context.tsx */ \"(rsc)/./src/contexts/auth-context.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Ctest-connection%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Ctest-connection%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/test-connection/page.tsx */ \"(rsc)/./src/app/test-connection/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNuZXh0anMlNUMlNUN3ZWJhcHAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3Rlc3QtY29ubmVjdGlvbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTEFBc0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXG5leHRqc1xcXFx3ZWJhcHBcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFx0ZXN0LWNvbm5lY3Rpb25cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Ctest-connection%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./src/components/theme-provider.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/auth-context */ \"(rsc)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_ui_sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/sonner */ \"(rsc)/./src/components/ui/sonner.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: \"Aixiate - AI Solutions Platform\",\n        template: \"%s | Aixiate\"\n    },\n    description: \"Aixiate is a cutting-edge AI solutions platform that helps businesses leverage artificial intelligence to drive innovation, efficiency, and growth. Discover our comprehensive suite of AI tools and services.\",\n    keywords: [\n        \"AI solutions\",\n        \"artificial intelligence\",\n        \"machine learning\",\n        \"business automation\",\n        \"AI platform\",\n        \"enterprise AI\",\n        \"AI consulting\",\n        \"data analytics\",\n        \"AI tools\",\n        \"digital transformation\"\n    ],\n    authors: [\n        {\n            name: \"Aixiate Team\"\n        }\n    ],\n    creator: \"Aixiate\",\n    publisher: \"Aixiate\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || \"http://localhost:3000\"),\n    alternates: {\n        canonical: \"/\"\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"/\",\n        title: \"Aixiate - AI Solutions Platform\",\n        description: \"Aixiate is a cutting-edge AI solutions platform that helps businesses leverage artificial intelligence to drive innovation, efficiency, and growth.\",\n        siteName: \"Aixiate\",\n        images: [\n            {\n                url: \"/img/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Aixiate AI Solutions Platform\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Aixiate - AI Solutions Platform\",\n        description: \"Aixiate is a cutting-edge AI solutions platform that helps businesses leverage artificial intelligence to drive innovation, efficiency, and growth.\",\n        images: [\n            \"/img/og-image.jpg\"\n        ],\n        creator: \"@aixiate\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: process.env.GOOGLE_SITE_VERIFICATION,\n        yandex: process.env.YANDEX_VERIFICATION,\n        yahoo: process.env.YAHOO_VERIFICATION\n    }\n};\nconst viewport = {\n    themeColor: [\n        {\n            media: \"(prefers-color-scheme: light)\",\n            color: \"white\"\n        },\n        {\n            media: \"(prefers-color-scheme: dark)\",\n            color: \"black\"\n        }\n    ],\n    width: \"device-width\",\n    initialScale: 1,\n    maximumScale: 1,\n    userScalable: false\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//fonts.gstatic.com\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"Organization\",\n                                name: \"Aixiate\",\n                                url: \"https://aixiate.com\",\n                                logo: \"https://aixiate.com/img/logo.png\",\n                                description: \"Aixiate is a cutting-edge AI solutions platform that helps businesses leverage artificial intelligence to drive innovation, efficiency, and growth.\",\n                                sameAs: [\n                                    \"https://twitter.com/aixiate\",\n                                    \"https://linkedin.com/company/aixiate\",\n                                    \"https://github.com/aixiate\"\n                                ],\n                                contactPoint: {\n                                    \"@type\": \"ContactPoint\",\n                                    telephone: \"******-123-4567\",\n                                    contactType: \"customer service\",\n                                    email: \"<EMAIL>\"\n                                },\n                                address: {\n                                    \"@type\": \"PostalAddress\",\n                                    streetAddress: \"123 AI Street\",\n                                    addressLocality: \"Tech City\",\n                                    addressRegion: \"TC\",\n                                    postalCode: \"12345\",\n                                    addressCountry: \"US\"\n                                }\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6___default().variable)} antialiased`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    attribute: \"class\",\n                    defaultTheme: \"system\",\n                    enableSystem: true,\n                    disableTransitionOnChange: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                        children: [\n                            children,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sonner__WEBPACK_IMPORTED_MODULE_4__.Toaster, {}, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/test-connection/page.tsx":
/*!******************************************!*\
  !*** ./src/app/test-connection/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\nextjs\\webapp\\frontend\\src\\app\\test-connection\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/theme-provider.tsx":
/*!*******************************************!*\
  !*** ./src/components/theme-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\nextjs\\webapp\\frontend\\src\\components\\theme-provider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(rsc)/./src/components/ui/sonner.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/sonner.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ Toaster)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Toaster = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\nextjs\\webapp\\frontend\\src\\components\\ui\\sonner.tsx",
"Toaster",
);

/***/ }),

/***/ "(rsc)/./src/contexts/auth-context.tsx":
/*!***************************************!*\
  !*** ./src/contexts/auth-context.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthContext: () => (/* binding */ AuthContext),
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\nextjs\\webapp\\frontend\\src\\contexts\\auth-context.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\nextjs\\webapp\\frontend\\src\\contexts\\auth-context.tsx",
"useAuth",
);const AuthContext = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthContext() from the server but AuthContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\nextjs\\webapp\\frontend\\src\\contexts\\auth-context.tsx",
"AuthContext",
);

/***/ }),

/***/ "(rsc)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"48af3c8e7693\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcbmV4dGpzXFx3ZWJhcHBcXGZyb250ZW5kXFxzcmNcXHN0eWxlc1xcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0OGFmM2M4ZTc2OTNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/theme-provider.tsx */ \"(ssr)/./src/components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/sonner.tsx */ \"(ssr)/./src/components/ui/sonner.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/auth-context.tsx */ \"(ssr)/./src/contexts/auth-context.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Ctest-connection%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Ctest-connection%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/test-connection/page.tsx */ \"(ssr)/./src/app/test-connection/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNuZXh0anMlNUMlNUN3ZWJhcHAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3Rlc3QtY29ubmVjdGlvbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTEFBc0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXG5leHRqc1xcXFx3ZWJhcHBcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFx0ZXN0LWNvbm5lY3Rpb25cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cnextjs%5C%5Cwebapp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Ctest-connection%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/test-connection/page.tsx":
/*!******************************************!*\
  !*** ./src/app/test-connection/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestConnectionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_connection_diagnostic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/connection-diagnostic */ \"(ssr)/./src/components/connection-diagnostic.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api-client */ \"(ssr)/./src/lib/api-client.ts\");\n/* harmony import */ var _hooks_use_client_side__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-client-side */ \"(ssr)/./src/hooks/use-client-side.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction TestConnectionPage() {\n    const [testResults, setTestResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const currentOrigin = (0,_hooks_use_client_side__WEBPACK_IMPORTED_MODULE_7__.useCurrentOrigin)();\n    const runManualTests = async ()=>{\n        setIsLoading(true);\n        setTestResults([]);\n        const tests = [\n            {\n                name: 'Direct Health Check',\n                test: async ()=>{\n                    const response = await fetch('http://localhost:8000/health');\n                    const data = await response.json();\n                    return {\n                        success: true,\n                        data\n                    };\n                }\n            },\n            {\n                name: 'API Client Health Check',\n                test: async ()=>{\n                    const data = await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.get('/health');\n                    return {\n                        success: true,\n                        data\n                    };\n                }\n            },\n            {\n                name: 'API Client Connection Test',\n                test: async ()=>{\n                    const result = await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.testConnection();\n                    return result;\n                }\n            },\n            {\n                name: 'Auth Endpoint Test',\n                test: async ()=>{\n                    try {\n                        await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.post('/auth/login', {\n                            email_or_username: '<EMAIL>',\n                            password: 'testpassword'\n                        });\n                        return {\n                            success: false,\n                            message: 'Unexpected success'\n                        };\n                    } catch (error) {\n                        if (error.message.includes('Validation error') || error.message.includes('Invalid credentials') || error.message.includes('User not found')) {\n                            return {\n                                success: true,\n                                message: 'Expected validation error',\n                                error: error.message\n                            };\n                        } else {\n                            return {\n                                success: false,\n                                message: error.message || 'Auth endpoint error',\n                                error: {\n                                    message: error.message,\n                                    code: error.code,\n                                    status: error.response?.status,\n                                    data: error.response?.data\n                                }\n                            };\n                        }\n                    }\n                }\n            }\n        ];\n        const results = [];\n        for (const test of tests){\n            try {\n                const result = await test.test();\n                results.push({\n                    name: test.name,\n                    ...result\n                });\n            } catch (error) {\n                results.push({\n                    name: test.name,\n                    success: false,\n                    message: error.message || 'Test failed',\n                    error: {\n                        message: error.message,\n                        code: error.code,\n                        status: error.response?.status,\n                        data: error.response?.data\n                    }\n                });\n            }\n        }\n        setTestResults(results);\n        setIsLoading(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestConnectionPage.useEffect\": ()=>{\n            runManualTests();\n        }\n    }[\"TestConnectionPage.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-8 space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"Connection Test Page\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground mt-2\",\n                        children: \"Diagnose connection issues between frontend and backend\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                lineNumber: 107,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            children: \"Environment Configuration\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Frontend URL:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 29\n                                        }, this),\n                                        \" \",\n                                        currentOrigin\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Backend URL:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 29\n                                        }, this),\n                                        \" \",\n                                        \"http://127.0.0.1:8000\" || 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Debug Mode:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 29\n                                        }, this),\n                                        \" \",\n                                        \"true\" || 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Node Env:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 29\n                                        }, this),\n                                        \" \",\n                                        \"development\" || 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: \"Manual Connection Tests\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"These tests check different aspects of the connection\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: runManualTests,\n                                disabled: isLoading,\n                                children: isLoading ? 'Running Tests...' : 'Run Manual Tests'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 21\n                            }, this),\n                            testResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: testResults.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                        variant: result.success ? 'default' : 'destructive',\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium\",\n                                                    children: result.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm mt-1\",\n                                                    children: [\n                                                        result.success ? '✅' : '❌',\n                                                        \" \",\n                                                        result.message\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 41\n                                                }, this),\n                                                result.data && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                                    className: \"mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                            className: \"cursor-pointer text-xs\",\n                                                            children: \"Show Data\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 49\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            className: \"text-xs mt-1 p-2 bg-muted rounded\",\n                                                            children: JSON.stringify(result.data, null, 2)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 45\n                                                }, this),\n                                                result.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                                    className: \"mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                            className: \"cursor-pointer text-xs\",\n                                                            children: \"Show Error\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 49\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            className: \"text-xs mt-1 p-2 bg-muted rounded\",\n                                                            children: JSON.stringify(result.error, null, 2)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 33\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_connection_diagnostic__WEBPACK_IMPORTED_MODULE_2__.ConnectionDiagnostic, {}, void 0, false, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                lineNumber: 184,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            children: \"Quick Links\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"http://localhost:8000/health\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"p-3 border rounded-lg hover:bg-muted transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: \"Backend Health\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"http://localhost:8000/health\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"http://localhost:8000/docs\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"p-3 border rounded-lg hover:bg-muted transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: \"API Documentation\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"http://localhost:8000/docs\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/login\",\n                                    className: \"p-3 border rounded-lg hover:bg-muted transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: \"Login Page\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Test authentication\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                lineNumber: 187,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n        lineNumber: 106,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/test-connection/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/connection-diagnostic.tsx":
/*!**************************************************!*\
  !*** ./src/components/connection-diagnostic.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectionDiagnostic: () => (/* binding */ ConnectionDiagnostic)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Loader2,RefreshCw,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Loader2,RefreshCw,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Loader2,RefreshCw,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Loader2,RefreshCw,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Loader2,RefreshCw,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api-client */ \"(ssr)/./src/lib/api-client.ts\");\n/* harmony import */ var _hooks_use_client_side__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-client-side */ \"(ssr)/./src/hooks/use-client-side.ts\");\n/* __next_internal_client_entry_do_not_use__ ConnectionDiagnostic auto */ \n\n\n\n\n\n\n\n\nfunction ConnectionDiagnostic() {\n    const [isRunning, setIsRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const currentOrigin = (0,_hooks_use_client_side__WEBPACK_IMPORTED_MODULE_7__.useCurrentOrigin)();\n    const diagnosticTests = [\n        {\n            name: 'Backend Connection',\n            test: 'connection'\n        },\n        {\n            name: 'API Health Check',\n            test: 'health'\n        },\n        {\n            name: 'Authentication Endpoint',\n            test: 'auth'\n        },\n        {\n            name: 'CORS Configuration',\n            test: 'cors'\n        }\n    ];\n    const runDiagnostics = async ()=>{\n        setIsRunning(true);\n        setResults([]);\n        setConnectionStatus(null);\n        // Initialize results with pending status\n        const initialResults = diagnosticTests.map((test)=>({\n                test: test.name,\n                status: 'pending',\n                message: 'Running...'\n            }));\n        setResults(initialResults);\n        try {\n            // Test 1: Basic connection\n            await updateResult('Backend Connection', async ()=>{\n                const result = await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.testConnection();\n                setConnectionStatus(result);\n                if (result.success) {\n                    return {\n                        status: 'success',\n                        message: result.message,\n                        details: result.details\n                    };\n                } else {\n                    return {\n                        status: 'error',\n                        message: result.message,\n                        details: result.details\n                    };\n                }\n            });\n            // Test 2: Health endpoint\n            await updateResult('API Health Check', async ()=>{\n                try {\n                    const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.get('/health');\n                    return {\n                        status: 'success',\n                        message: 'Health endpoint accessible',\n                        details: response\n                    };\n                } catch (error) {\n                    return {\n                        status: 'error',\n                        message: `Health check failed: ${error.message}`,\n                        details: error\n                    };\n                }\n            });\n            // Test 3: Auth endpoint (expect validation error)\n            await updateResult('Authentication Endpoint', async ()=>{\n                try {\n                    await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.post('/auth/login', {\n                        email_or_username: '<EMAIL>',\n                        password: 'testpassword'\n                    });\n                    return {\n                        status: 'warning',\n                        message: 'Unexpected success - endpoint may have issues'\n                    };\n                } catch (error) {\n                    if (error.message.includes('Validation error') || error.message.includes('Invalid credentials')) {\n                        return {\n                            status: 'success',\n                            message: 'Auth endpoint accessible (expected validation error)',\n                            details: error.message\n                        };\n                    } else {\n                        return {\n                            status: 'error',\n                            message: `Auth endpoint error: ${error.message}`,\n                            details: error\n                        };\n                    }\n                }\n            });\n            // Test 4: CORS (simplified check)\n            await updateResult('CORS Configuration', async ()=>{\n                try {\n                    // If we can make requests, CORS is likely configured correctly\n                    await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.get('/health');\n                    return {\n                        status: 'success',\n                        message: 'CORS appears to be configured correctly'\n                    };\n                } catch (error) {\n                    if (error.message.includes('CORS')) {\n                        return {\n                            status: 'error',\n                            message: 'CORS configuration issue detected',\n                            details: error\n                        };\n                    } else {\n                        return {\n                            status: 'warning',\n                            message: 'Unable to verify CORS (other connection issues present)'\n                        };\n                    }\n                }\n            });\n        } catch (error) {\n            console.error('Diagnostic error:', error);\n        } finally{\n            setIsRunning(false);\n        }\n    };\n    const updateResult = async (testName, testFunction)=>{\n        try {\n            const result = await testFunction();\n            setResults((prev)=>prev.map((r)=>r.test === testName ? {\n                        ...r,\n                        ...result\n                    } : r));\n        } catch (error) {\n            setResults((prev)=>prev.map((r)=>r.test === testName ? {\n                        ...r,\n                        status: 'error',\n                        message: `Test failed: ${error.message}`,\n                        details: error\n                    } : r));\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'success':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 24\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 24\n                }, this);\n            case 'warning':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 24\n                }, this);\n            case 'pending':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 animate-spin text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 24\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const variants = {\n            success: 'default',\n            error: 'destructive',\n            warning: 'secondary',\n            pending: 'outline'\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n            variant: variants[status] || 'outline',\n            children: status.toUpperCase()\n        }, void 0, false, {\n            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n            lineNumber: 184,\n            columnNumber: 13\n        }, this);\n    };\n    // Auto-run diagnostics on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConnectionDiagnostic.useEffect\": ()=>{\n            runDiagnostics();\n        }\n    }[\"ConnectionDiagnostic.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: \"w-full max-w-2xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 21\n                            }, this),\n                            \"Connection Diagnostics\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                        children: \"Diagnose connection issues between frontend and backend services\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                lineNumber: 197,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"space-y-4\",\n                children: [\n                    connectionStatus && !connectionStatus.success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                        variant: \"destructive\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.AlertDescription, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Connection Failed:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 29\n                                    }, this),\n                                    \" \",\n                                    connectionStatus.message,\n                                    connectionStatus.details?.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 text-sm\",\n                                        children: [\n                                            \"Error Code: \",\n                                            connectionStatus.details.error\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: results.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            getStatusIcon(result.status),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: result.test\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: result.message\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 29\n                                    }, this),\n                                    getStatusBadge(result.status)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 25\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: runDiagnostics,\n                            disabled: isRunning,\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                isRunning ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 29\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 29\n                                }, this),\n                                isRunning ? 'Running Diagnostics...' : 'Run Diagnostics'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 17\n                    }, this),\n                    connectionStatus?.success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.AlertDescription, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Connection Successful!\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 29\n                                    }, this),\n                                    \" All systems appear to be working correctly. If you're still experiencing issues, try refreshing the page or clearing your browser cache.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-muted-foreground space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Backend URL:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 26\n                                    }, this),\n                                    \" \",\n                                    \"http://127.0.0.1:8000\" || 0\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Frontend URL:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 26\n                                    }, this),\n                                    \" \",\n                                    currentOrigin\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                lineNumber: 206,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n        lineNumber: 196,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9jb25uZWN0aW9uLWRpYWdub3N0aWMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRW1EO0FBQ0g7QUFDaUQ7QUFDakM7QUFDbEI7QUFDeUM7QUFDbEM7QUFDTTtBQWVwRCxTQUFTbUI7SUFDWixNQUFNLENBQUNDLFdBQVdDLGFBQWEsR0FBR3BCLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ3FCLFNBQVNDLFdBQVcsR0FBR3RCLCtDQUFRQSxDQUFxQixFQUFFO0lBQzdELE1BQU0sQ0FBQ3VCLGtCQUFrQkMsb0JBQW9CLEdBQUd4QiwrQ0FBUUEsQ0FBMEI7SUFDbEYsTUFBTXlCLGdCQUFnQlIsd0VBQWdCQTtJQUV0QyxNQUFNUyxrQkFBa0I7UUFDcEI7WUFBRUMsTUFBTTtZQUFzQkMsTUFBTTtRQUFhO1FBQ2pEO1lBQUVELE1BQU07WUFBb0JDLE1BQU07UUFBUztRQUMzQztZQUFFRCxNQUFNO1lBQTJCQyxNQUFNO1FBQU87UUFDaEQ7WUFBRUQsTUFBTTtZQUFzQkMsTUFBTTtRQUFPO0tBQzlDO0lBRUQsTUFBTUMsaUJBQWlCO1FBQ25CVCxhQUFhO1FBQ2JFLFdBQVcsRUFBRTtRQUNiRSxvQkFBb0I7UUFFcEIseUNBQXlDO1FBQ3pDLE1BQU1NLGlCQUFpQkosZ0JBQWdCSyxHQUFHLENBQUNILENBQUFBLE9BQVM7Z0JBQ2hEQSxNQUFNQSxLQUFLRCxJQUFJO2dCQUNmSyxRQUFRO2dCQUNSQyxTQUFTO1lBQ2I7UUFDQVgsV0FBV1E7UUFFWCxJQUFJO1lBQ0EsMkJBQTJCO1lBQzNCLE1BQU1JLGFBQWEsc0JBQXNCO2dCQUNyQyxNQUFNQyxTQUFTLE1BQU1uQiw4REFBaUJBLENBQUNvQixjQUFjO2dCQUNyRFosb0JBQW9CVztnQkFFcEIsSUFBSUEsT0FBT0UsT0FBTyxFQUFFO29CQUNoQixPQUFPO3dCQUFFTCxRQUFRO3dCQUFXQyxTQUFTRSxPQUFPRixPQUFPO3dCQUFFSyxTQUFTSCxPQUFPRyxPQUFPO29CQUFDO2dCQUNqRixPQUFPO29CQUNILE9BQU87d0JBQUVOLFFBQVE7d0JBQVNDLFNBQVNFLE9BQU9GLE9BQU87d0JBQUVLLFNBQVNILE9BQU9HLE9BQU87b0JBQUM7Z0JBQy9FO1lBQ0o7WUFFQSwwQkFBMEI7WUFDMUIsTUFBTUosYUFBYSxvQkFBb0I7Z0JBQ25DLElBQUk7b0JBQ0EsTUFBTUssV0FBVyxNQUFNdkIsOERBQWlCQSxDQUFDd0IsR0FBRyxDQUFDO29CQUM3QyxPQUFPO3dCQUNIUixRQUFRO3dCQUNSQyxTQUFTO3dCQUNUSyxTQUFTQztvQkFDYjtnQkFDSixFQUFFLE9BQU9FLE9BQVk7b0JBQ2pCLE9BQU87d0JBQ0hULFFBQVE7d0JBQ1JDLFNBQVMsQ0FBQyxxQkFBcUIsRUFBRVEsTUFBTVIsT0FBTyxFQUFFO3dCQUNoREssU0FBU0c7b0JBQ2I7Z0JBQ0o7WUFDSjtZQUVBLGtEQUFrRDtZQUNsRCxNQUFNUCxhQUFhLDJCQUEyQjtnQkFDMUMsSUFBSTtvQkFDQSxNQUFNbEIsOERBQWlCQSxDQUFDMEIsSUFBSSxDQUFDLGVBQWU7d0JBQ3hDQyxtQkFBbUI7d0JBQ25CQyxVQUFVO29CQUNkO29CQUNBLE9BQU87d0JBQ0haLFFBQVE7d0JBQ1JDLFNBQVM7b0JBQ2I7Z0JBQ0osRUFBRSxPQUFPUSxPQUFZO29CQUNqQixJQUFJQSxNQUFNUixPQUFPLENBQUNZLFFBQVEsQ0FBQyx1QkFBdUJKLE1BQU1SLE9BQU8sQ0FBQ1ksUUFBUSxDQUFDLHdCQUF3Qjt3QkFDN0YsT0FBTzs0QkFDSGIsUUFBUTs0QkFDUkMsU0FBUzs0QkFDVEssU0FBU0csTUFBTVIsT0FBTzt3QkFDMUI7b0JBQ0osT0FBTzt3QkFDSCxPQUFPOzRCQUNIRCxRQUFROzRCQUNSQyxTQUFTLENBQUMscUJBQXFCLEVBQUVRLE1BQU1SLE9BQU8sRUFBRTs0QkFDaERLLFNBQVNHO3dCQUNiO29CQUNKO2dCQUNKO1lBQ0o7WUFFQSxrQ0FBa0M7WUFDbEMsTUFBTVAsYUFBYSxzQkFBc0I7Z0JBQ3JDLElBQUk7b0JBQ0EsK0RBQStEO29CQUMvRCxNQUFNbEIsOERBQWlCQSxDQUFDd0IsR0FBRyxDQUFDO29CQUM1QixPQUFPO3dCQUNIUixRQUFRO3dCQUNSQyxTQUFTO29CQUNiO2dCQUNKLEVBQUUsT0FBT1EsT0FBWTtvQkFDakIsSUFBSUEsTUFBTVIsT0FBTyxDQUFDWSxRQUFRLENBQUMsU0FBUzt3QkFDaEMsT0FBTzs0QkFDSGIsUUFBUTs0QkFDUkMsU0FBUzs0QkFDVEssU0FBU0c7d0JBQ2I7b0JBQ0osT0FBTzt3QkFDSCxPQUFPOzRCQUNIVCxRQUFROzRCQUNSQyxTQUFTO3dCQUNiO29CQUNKO2dCQUNKO1lBQ0o7UUFFSixFQUFFLE9BQU9RLE9BQU87WUFDWkssUUFBUUwsS0FBSyxDQUFDLHFCQUFxQkE7UUFDdkMsU0FBVTtZQUNOckIsYUFBYTtRQUNqQjtJQUNKO0lBRUEsTUFBTWMsZUFBZSxPQUFPYSxVQUFrQkM7UUFDMUMsSUFBSTtZQUNBLE1BQU1iLFNBQVMsTUFBTWE7WUFDckIxQixXQUFXMkIsQ0FBQUEsT0FBUUEsS0FBS2xCLEdBQUcsQ0FBQ21CLENBQUFBLElBQ3hCQSxFQUFFdEIsSUFBSSxLQUFLbUIsV0FBVzt3QkFBRSxHQUFHRyxDQUFDO3dCQUFFLEdBQUdmLE1BQU07b0JBQUMsSUFBSWU7UUFFcEQsRUFBRSxPQUFPVCxPQUFZO1lBQ2pCbkIsV0FBVzJCLENBQUFBLE9BQVFBLEtBQUtsQixHQUFHLENBQUNtQixDQUFBQSxJQUN4QkEsRUFBRXRCLElBQUksS0FBS21CLFdBQVc7d0JBQ2xCLEdBQUdHLENBQUM7d0JBQ0psQixRQUFRO3dCQUNSQyxTQUFTLENBQUMsYUFBYSxFQUFFUSxNQUFNUixPQUFPLEVBQUU7d0JBQ3hDSyxTQUFTRztvQkFDYixJQUFJUztRQUVaO0lBQ0o7SUFFQSxNQUFNQyxnQkFBZ0IsQ0FBQ25CO1FBQ25CLE9BQVFBO1lBQ0osS0FBSztnQkFDRCxxQkFBTyw4REFBQ3BCLCtIQUFXQTtvQkFBQ3dDLFdBQVU7Ozs7OztZQUNsQyxLQUFLO2dCQUNELHFCQUFPLDhEQUFDdkMsK0hBQU9BO29CQUFDdUMsV0FBVTs7Ozs7O1lBQzlCLEtBQUs7Z0JBQ0QscUJBQU8sOERBQUN0QyxnSUFBYUE7b0JBQUNzQyxXQUFVOzs7Ozs7WUFDcEMsS0FBSztnQkFDRCxxQkFBTyw4REFBQ3pDLGdJQUFPQTtvQkFBQ3lDLFdBQVU7Ozs7OztZQUM5QjtnQkFDSSxPQUFPO1FBQ2Y7SUFDSjtJQUVBLE1BQU1DLGlCQUFpQixDQUFDckI7UUFDcEIsTUFBTXNCLFdBQVc7WUFDYmpCLFNBQVM7WUFDVEksT0FBTztZQUNQYyxTQUFTO1lBQ1RDLFNBQVM7UUFDYjtRQUVBLHFCQUNJLDhEQUFDOUMsdURBQUtBO1lBQUMrQyxTQUFTSCxRQUFRLENBQUN0QixPQUFnQyxJQUFJO3NCQUN4REEsT0FBTzBCLFdBQVc7Ozs7OztJQUcvQjtJQUVBLDBDQUEwQztJQUMxQ3pELGdEQUFTQTswQ0FBQztZQUNONEI7UUFDSjt5Q0FBRyxFQUFFO0lBRUwscUJBQ0ksOERBQUMxQixxREFBSUE7UUFBQ2lELFdBQVU7OzBCQUNaLDhEQUFDOUMsMkRBQVVBOztrQ0FDUCw4REFBQ0MsMERBQVNBO3dCQUFDNkMsV0FBVTs7MENBQ2pCLDhEQUFDckMsZ0lBQVNBO2dDQUFDcUMsV0FBVTs7Ozs7OzRCQUFZOzs7Ozs7O2tDQUdyQyw4REFBQy9DLGdFQUFlQTtrQ0FBQzs7Ozs7Ozs7Ozs7OzBCQUlyQiw4REFBQ0QsNERBQVdBO2dCQUFDZ0QsV0FBVTs7b0JBQ2xCN0Isb0JBQW9CLENBQUNBLGlCQUFpQmMsT0FBTyxrQkFDMUMsOERBQUM3Qix1REFBS0E7d0JBQUNpRCxTQUFROzswQ0FDWCw4REFBQzVDLCtIQUFPQTtnQ0FBQ3VDLFdBQVU7Ozs7OzswQ0FDbkIsOERBQUMzQyxrRUFBZ0JBOztrREFDYiw4REFBQ2tEO2tEQUFPOzs7Ozs7b0NBQTJCO29DQUFFcEMsaUJBQWlCVSxPQUFPO29DQUM1RFYsaUJBQWlCZSxPQUFPLEVBQUVHLHVCQUN2Qiw4REFBQ21CO3dDQUFJUixXQUFVOzs0Q0FBZTs0Q0FDYjdCLGlCQUFpQmUsT0FBTyxDQUFDRyxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU8vRCw4REFBQ21CO3dCQUFJUixXQUFVO2tDQUNWL0IsUUFBUVUsR0FBRyxDQUFDLENBQUNJLFFBQVEwQixzQkFDbEIsOERBQUNEO2dDQUFnQlIsV0FBVTs7a0RBQ3ZCLDhEQUFDUTt3Q0FBSVIsV0FBVTs7NENBQ1ZELGNBQWNoQixPQUFPSCxNQUFNOzBEQUM1Qiw4REFBQzRCOztrRUFDRyw4REFBQ0E7d0RBQUlSLFdBQVU7a0VBQWVqQixPQUFPUCxJQUFJOzs7Ozs7a0VBQ3pDLDhEQUFDZ0M7d0RBQUlSLFdBQVU7a0VBQWlDakIsT0FBT0YsT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7O29DQUdyRW9CLGVBQWVsQixPQUFPSCxNQUFNOzsrQkFSdkI2Qjs7Ozs7Ozs7OztrQ0FhbEIsOERBQUNEO3dCQUFJUixXQUFVO2tDQUNYLDRFQUFDbEQseURBQU1BOzRCQUNINEQsU0FBU2pDOzRCQUNUa0MsVUFBVTVDOzRCQUNWaUMsV0FBVTs7Z0NBRVRqQywwQkFDRyw4REFBQ1IsZ0lBQU9BO29DQUFDeUMsV0FBVTs7Ozs7eURBRW5CLDhEQUFDckMsZ0lBQVNBO29DQUFDcUMsV0FBVTs7Ozs7O2dDQUV4QmpDLFlBQVksMkJBQTJCOzs7Ozs7Ozs7Ozs7b0JBSS9DSSxrQkFBa0JjLHlCQUNmLDhEQUFDN0IsdURBQUtBOzswQ0FDRiw4REFBQ0ksK0hBQVdBO2dDQUFDd0MsV0FBVTs7Ozs7OzBDQUN2Qiw4REFBQzNDLGtFQUFnQkE7O2tEQUNiLDhEQUFDa0Q7a0RBQU87Ozs7OztvQ0FBK0I7Ozs7Ozs7Ozs7Ozs7a0NBTW5ELDhEQUFDQzt3QkFBSVIsV0FBVTs7MENBQ1gsOERBQUNROztrREFBSSw4REFBQ0Q7a0RBQU87Ozs7OztvQ0FBcUI7b0NBQUVLLHVCQUErQixJQUFJLENBQXVCOzs7Ozs7OzBDQUM5Riw4REFBQ0o7O2tEQUFJLDhEQUFDRDtrREFBTzs7Ozs7O29DQUFzQjtvQ0FBRWxDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS3pEIiwic291cmNlcyI6WyJEOlxcbmV4dGpzXFx3ZWJhcHBcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGNvbm5lY3Rpb24tZGlhZ25vc3RpYy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nO1xuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmREZXNjcmlwdGlvbiwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnO1xuaW1wb3J0IHsgQWxlcnQsIEFsZXJ0RGVzY3JpcHRpb24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYWxlcnQnO1xuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYmFkZ2UnO1xuaW1wb3J0IHsgTG9hZGVyMiwgQ2hlY2tDaXJjbGUsIFhDaXJjbGUsIEFsZXJ0VHJpYW5nbGUsIFJlZnJlc2hDdyB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgeyBhcGlDbGllbnRJbnN0YW5jZSB9IGZyb20gJ0AvbGliL2FwaS1jbGllbnQnO1xuaW1wb3J0IHsgdXNlQ3VycmVudE9yaWdpbiB9IGZyb20gJ0AvaG9va3MvdXNlLWNsaWVudC1zaWRlJztcblxuaW50ZXJmYWNlIENvbm5lY3Rpb25TdGF0dXMge1xuICAgIHN1Y2Nlc3M6IGJvb2xlYW47XG4gICAgbWVzc2FnZTogc3RyaW5nO1xuICAgIGRldGFpbHM/OiBhbnk7XG59XG5cbmludGVyZmFjZSBEaWFnbm9zdGljUmVzdWx0IHtcbiAgICB0ZXN0OiBzdHJpbmc7XG4gICAgc3RhdHVzOiAnc3VjY2VzcycgfCAnZXJyb3InIHwgJ3dhcm5pbmcnIHwgJ3BlbmRpbmcnO1xuICAgIG1lc3NhZ2U6IHN0cmluZztcbiAgICBkZXRhaWxzPzogYW55O1xufVxuXG5leHBvcnQgZnVuY3Rpb24gQ29ubmVjdGlvbkRpYWdub3N0aWMoKSB7XG4gICAgY29uc3QgW2lzUnVubmluZywgc2V0SXNSdW5uaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgICBjb25zdCBbcmVzdWx0cywgc2V0UmVzdWx0c10gPSB1c2VTdGF0ZTxEaWFnbm9zdGljUmVzdWx0W10+KFtdKTtcbiAgICBjb25zdCBbY29ubmVjdGlvblN0YXR1cywgc2V0Q29ubmVjdGlvblN0YXR1c10gPSB1c2VTdGF0ZTxDb25uZWN0aW9uU3RhdHVzIHwgbnVsbD4obnVsbCk7XG4gICAgY29uc3QgY3VycmVudE9yaWdpbiA9IHVzZUN1cnJlbnRPcmlnaW4oKTtcblxuICAgIGNvbnN0IGRpYWdub3N0aWNUZXN0cyA9IFtcbiAgICAgICAgeyBuYW1lOiAnQmFja2VuZCBDb25uZWN0aW9uJywgdGVzdDogJ2Nvbm5lY3Rpb24nIH0sXG4gICAgICAgIHsgbmFtZTogJ0FQSSBIZWFsdGggQ2hlY2snLCB0ZXN0OiAnaGVhbHRoJyB9LFxuICAgICAgICB7IG5hbWU6ICdBdXRoZW50aWNhdGlvbiBFbmRwb2ludCcsIHRlc3Q6ICdhdXRoJyB9LFxuICAgICAgICB7IG5hbWU6ICdDT1JTIENvbmZpZ3VyYXRpb24nLCB0ZXN0OiAnY29ycycgfSxcbiAgICBdO1xuXG4gICAgY29uc3QgcnVuRGlhZ25vc3RpY3MgPSBhc3luYyAoKSA9PiB7XG4gICAgICAgIHNldElzUnVubmluZyh0cnVlKTtcbiAgICAgICAgc2V0UmVzdWx0cyhbXSk7XG4gICAgICAgIHNldENvbm5lY3Rpb25TdGF0dXMobnVsbCk7XG5cbiAgICAgICAgLy8gSW5pdGlhbGl6ZSByZXN1bHRzIHdpdGggcGVuZGluZyBzdGF0dXNcbiAgICAgICAgY29uc3QgaW5pdGlhbFJlc3VsdHMgPSBkaWFnbm9zdGljVGVzdHMubWFwKHRlc3QgPT4gKHtcbiAgICAgICAgICAgIHRlc3Q6IHRlc3QubmFtZSxcbiAgICAgICAgICAgIHN0YXR1czogJ3BlbmRpbmcnIGFzIGNvbnN0LFxuICAgICAgICAgICAgbWVzc2FnZTogJ1J1bm5pbmcuLi4nLFxuICAgICAgICB9KSk7XG4gICAgICAgIHNldFJlc3VsdHMoaW5pdGlhbFJlc3VsdHMpO1xuXG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICAvLyBUZXN0IDE6IEJhc2ljIGNvbm5lY3Rpb25cbiAgICAgICAgICAgIGF3YWl0IHVwZGF0ZVJlc3VsdCgnQmFja2VuZCBDb25uZWN0aW9uJywgYXN5bmMgKCkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGFwaUNsaWVudEluc3RhbmNlLnRlc3RDb25uZWN0aW9uKCk7XG4gICAgICAgICAgICAgICAgc2V0Q29ubmVjdGlvblN0YXR1cyhyZXN1bHQpO1xuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIGlmIChyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4geyBzdGF0dXM6ICdzdWNjZXNzJywgbWVzc2FnZTogcmVzdWx0Lm1lc3NhZ2UsIGRldGFpbHM6IHJlc3VsdC5kZXRhaWxzIH07XG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHsgc3RhdHVzOiAnZXJyb3InLCBtZXNzYWdlOiByZXN1bHQubWVzc2FnZSwgZGV0YWlsczogcmVzdWx0LmRldGFpbHMgfTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcblxuICAgICAgICAgICAgLy8gVGVzdCAyOiBIZWFsdGggZW5kcG9pbnRcbiAgICAgICAgICAgIGF3YWl0IHVwZGF0ZVJlc3VsdCgnQVBJIEhlYWx0aCBDaGVjaycsIGFzeW5jICgpID0+IHtcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudEluc3RhbmNlLmdldCgnL2hlYWx0aCcpO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiAnc3VjY2VzcycsXG4gICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAnSGVhbHRoIGVuZHBvaW50IGFjY2Vzc2libGUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgZGV0YWlsczogcmVzcG9uc2VcbiAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXM6ICdlcnJvcicsXG4gICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiBgSGVhbHRoIGNoZWNrIGZhaWxlZDogJHtlcnJvci5tZXNzYWdlfWAsXG4gICAgICAgICAgICAgICAgICAgICAgICBkZXRhaWxzOiBlcnJvclxuICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICAvLyBUZXN0IDM6IEF1dGggZW5kcG9pbnQgKGV4cGVjdCB2YWxpZGF0aW9uIGVycm9yKVxuICAgICAgICAgICAgYXdhaXQgdXBkYXRlUmVzdWx0KCdBdXRoZW50aWNhdGlvbiBFbmRwb2ludCcsIGFzeW5jICgpID0+IHtcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICBhd2FpdCBhcGlDbGllbnRJbnN0YW5jZS5wb3N0KCcvYXV0aC9sb2dpbicsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGVtYWlsX29yX3VzZXJuYW1lOiAndGVzdEBleGFtcGxlLmNvbScsXG4gICAgICAgICAgICAgICAgICAgICAgICBwYXNzd29yZDogJ3Rlc3RwYXNzd29yZCdcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXM6ICd3YXJuaW5nJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICdVbmV4cGVjdGVkIHN1Y2Nlc3MgLSBlbmRwb2ludCBtYXkgaGF2ZSBpc3N1ZXMnXG4gICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICAgICAgICAgICAgICBpZiAoZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnVmFsaWRhdGlvbiBlcnJvcicpIHx8IGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoJ0ludmFsaWQgY3JlZGVudGlhbHMnKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXM6ICdzdWNjZXNzJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAnQXV0aCBlbmRwb2ludCBhY2Nlc3NpYmxlIChleHBlY3RlZCB2YWxpZGF0aW9uIGVycm9yKScsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGV0YWlsczogZXJyb3IubWVzc2FnZVxuICAgICAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IGBBdXRoIGVuZHBvaW50IGVycm9yOiAke2Vycm9yLm1lc3NhZ2V9YCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZXRhaWxzOiBlcnJvclxuICAgICAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICAvLyBUZXN0IDQ6IENPUlMgKHNpbXBsaWZpZWQgY2hlY2spXG4gICAgICAgICAgICBhd2FpdCB1cGRhdGVSZXN1bHQoJ0NPUlMgQ29uZmlndXJhdGlvbicsIGFzeW5jICgpID0+IHtcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICAvLyBJZiB3ZSBjYW4gbWFrZSByZXF1ZXN0cywgQ09SUyBpcyBsaWtlbHkgY29uZmlndXJlZCBjb3JyZWN0bHlcbiAgICAgICAgICAgICAgICAgICAgYXdhaXQgYXBpQ2xpZW50SW5zdGFuY2UuZ2V0KCcvaGVhbHRoJyk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXM6ICdzdWNjZXNzJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICdDT1JTIGFwcGVhcnMgdG8gYmUgY29uZmlndXJlZCBjb3JyZWN0bHknXG4gICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICAgICAgICAgICAgICBpZiAoZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnQ09SUycpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXR1czogJ2Vycm9yJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAnQ09SUyBjb25maWd1cmF0aW9uIGlzc3VlIGRldGVjdGVkJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZXRhaWxzOiBlcnJvclxuICAgICAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiAnd2FybmluZycsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogJ1VuYWJsZSB0byB2ZXJpZnkgQ09SUyAob3RoZXIgY29ubmVjdGlvbiBpc3N1ZXMgcHJlc2VudCknXG4gICAgICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0RpYWdub3N0aWMgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgICB9IGZpbmFsbHkge1xuICAgICAgICAgICAgc2V0SXNSdW5uaW5nKGZhbHNlKTtcbiAgICAgICAgfVxuICAgIH07XG5cbiAgICBjb25zdCB1cGRhdGVSZXN1bHQgPSBhc3luYyAodGVzdE5hbWU6IHN0cmluZywgdGVzdEZ1bmN0aW9uOiAoKSA9PiBQcm9taXNlPGFueT4pID0+IHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHRlc3RGdW5jdGlvbigpO1xuICAgICAgICAgICAgc2V0UmVzdWx0cyhwcmV2ID0+IHByZXYubWFwKHIgPT4gXG4gICAgICAgICAgICAgICAgci50ZXN0ID09PSB0ZXN0TmFtZSA/IHsgLi4uciwgLi4ucmVzdWx0IH0gOiByXG4gICAgICAgICAgICApKTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICAgICAgc2V0UmVzdWx0cyhwcmV2ID0+IHByZXYubWFwKHIgPT4gXG4gICAgICAgICAgICAgICAgci50ZXN0ID09PSB0ZXN0TmFtZSA/IHtcbiAgICAgICAgICAgICAgICAgICAgLi4ucixcbiAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiBgVGVzdCBmYWlsZWQ6ICR7ZXJyb3IubWVzc2FnZX1gLFxuICAgICAgICAgICAgICAgICAgICBkZXRhaWxzOiBlcnJvclxuICAgICAgICAgICAgICAgIH0gOiByXG4gICAgICAgICAgICApKTtcbiAgICAgICAgfVxuICAgIH07XG5cbiAgICBjb25zdCBnZXRTdGF0dXNJY29uID0gKHN0YXR1czogc3RyaW5nKSA9PiB7XG4gICAgICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICAgICAgICBjYXNlICdzdWNjZXNzJzpcbiAgICAgICAgICAgICAgICByZXR1cm4gPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmVlbi01MDBcIiAvPjtcbiAgICAgICAgICAgIGNhc2UgJ2Vycm9yJzpcbiAgICAgICAgICAgICAgICByZXR1cm4gPFhDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LXJlZC01MDBcIiAvPjtcbiAgICAgICAgICAgIGNhc2UgJ3dhcm5pbmcnOlxuICAgICAgICAgICAgICAgIHJldHVybiA8QWxlcnRUcmlhbmdsZSBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQteWVsbG93LTUwMFwiIC8+O1xuICAgICAgICAgICAgY2FzZSAncGVuZGluZyc6XG4gICAgICAgICAgICAgICAgcmV0dXJuIDxMb2FkZXIyIGNsYXNzTmFtZT1cImgtNCB3LTQgYW5pbWF0ZS1zcGluIHRleHQtYmx1ZS01MDBcIiAvPjtcbiAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cbiAgICB9O1xuXG4gICAgY29uc3QgZ2V0U3RhdHVzQmFkZ2UgPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgICAgICAgY29uc3QgdmFyaWFudHMgPSB7XG4gICAgICAgICAgICBzdWNjZXNzOiAnZGVmYXVsdCcsXG4gICAgICAgICAgICBlcnJvcjogJ2Rlc3RydWN0aXZlJyxcbiAgICAgICAgICAgIHdhcm5pbmc6ICdzZWNvbmRhcnknLFxuICAgICAgICAgICAgcGVuZGluZzogJ291dGxpbmUnXG4gICAgICAgIH0gYXMgY29uc3Q7XG4gICAgICAgIFxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9e3ZhcmlhbnRzW3N0YXR1cyBhcyBrZXlvZiB0eXBlb2YgdmFyaWFudHNdIHx8ICdvdXRsaW5lJ30+XG4gICAgICAgICAgICAgICAge3N0YXR1cy50b1VwcGVyQ2FzZSgpfVxuICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgKTtcbiAgICB9O1xuXG4gICAgLy8gQXV0by1ydW4gZGlhZ25vc3RpY3Mgb24gY29tcG9uZW50IG1vdW50XG4gICAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgcnVuRGlhZ25vc3RpY3MoKTtcbiAgICB9LCBbXSk7XG5cbiAgICByZXR1cm4gKFxuICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJ3LWZ1bGwgbWF4LXctMnhsIG14LWF1dG9cIj5cbiAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgQ29ubmVjdGlvbiBEaWFnbm9zdGljc1xuICAgICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIERpYWdub3NlIGNvbm5lY3Rpb24gaXNzdWVzIGJldHdlZW4gZnJvbnRlbmQgYW5kIGJhY2tlbmQgc2VydmljZXNcbiAgICAgICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICB7Y29ubmVjdGlvblN0YXR1cyAmJiAhY29ubmVjdGlvblN0YXR1cy5zdWNjZXNzICYmIChcbiAgICAgICAgICAgICAgICAgICAgPEFsZXJ0IHZhcmlhbnQ9XCJkZXN0cnVjdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFhDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8QWxlcnREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Ryb25nPkNvbm5lY3Rpb24gRmFpbGVkOjwvc3Ryb25nPiB7Y29ubmVjdGlvblN0YXR1cy5tZXNzYWdlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjb25uZWN0aW9uU3RhdHVzLmRldGFpbHM/LmVycm9yICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0yIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEVycm9yIENvZGU6IHtjb25uZWN0aW9uU3RhdHVzLmRldGFpbHMuZXJyb3J9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0FsZXJ0RGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIDwvQWxlcnQ+XG4gICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgICAgIHtyZXN1bHRzLm1hcCgocmVzdWx0LCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC0zIGJvcmRlciByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0U3RhdHVzSWNvbihyZXN1bHQuc3RhdHVzKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57cmVzdWx0LnRlc3R9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+e3Jlc3VsdC5tZXNzYWdlfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0U3RhdHVzQmFkZ2UocmVzdWx0LnN0YXR1cyl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiBcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3J1bkRpYWdub3N0aWNzfSBcbiAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc1J1bm5pbmd9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtpc1J1bm5pbmcgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPExvYWRlcjIgY2xhc3NOYW1lPVwiaC00IHctNCBhbmltYXRlLXNwaW5cIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UmVmcmVzaEN3IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHtpc1J1bm5pbmcgPyAnUnVubmluZyBEaWFnbm9zdGljcy4uLicgOiAnUnVuIERpYWdub3N0aWNzJ31cbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7Y29ubmVjdGlvblN0YXR1cz8uc3VjY2VzcyAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxBbGVydD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxBbGVydERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdHJvbmc+Q29ubmVjdGlvbiBTdWNjZXNzZnVsITwvc3Ryb25nPiBBbGwgc3lzdGVtcyBhcHBlYXIgdG8gYmUgd29ya2luZyBjb3JyZWN0bHkuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgSWYgeW91J3JlIHN0aWxsIGV4cGVyaWVuY2luZyBpc3N1ZXMsIHRyeSByZWZyZXNoaW5nIHRoZSBwYWdlIG9yIGNsZWFyaW5nIHlvdXIgYnJvd3NlciBjYWNoZS5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQWxlcnREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9BbGVydD5cbiAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj48c3Ryb25nPkJhY2tlbmQgVVJMOjwvc3Ryb25nPiB7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDo4MDAwJ308L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj48c3Ryb25nPkZyb250ZW5kIFVSTDo8L3N0cm9uZz4ge2N1cnJlbnRPcmlnaW59PC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG4gICAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiQnV0dG9uIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZERlc2NyaXB0aW9uIiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkFsZXJ0IiwiQWxlcnREZXNjcmlwdGlvbiIsIkJhZGdlIiwiTG9hZGVyMiIsIkNoZWNrQ2lyY2xlIiwiWENpcmNsZSIsIkFsZXJ0VHJpYW5nbGUiLCJSZWZyZXNoQ3ciLCJhcGlDbGllbnRJbnN0YW5jZSIsInVzZUN1cnJlbnRPcmlnaW4iLCJDb25uZWN0aW9uRGlhZ25vc3RpYyIsImlzUnVubmluZyIsInNldElzUnVubmluZyIsInJlc3VsdHMiLCJzZXRSZXN1bHRzIiwiY29ubmVjdGlvblN0YXR1cyIsInNldENvbm5lY3Rpb25TdGF0dXMiLCJjdXJyZW50T3JpZ2luIiwiZGlhZ25vc3RpY1Rlc3RzIiwibmFtZSIsInRlc3QiLCJydW5EaWFnbm9zdGljcyIsImluaXRpYWxSZXN1bHRzIiwibWFwIiwic3RhdHVzIiwibWVzc2FnZSIsInVwZGF0ZVJlc3VsdCIsInJlc3VsdCIsInRlc3RDb25uZWN0aW9uIiwic3VjY2VzcyIsImRldGFpbHMiLCJyZXNwb25zZSIsImdldCIsImVycm9yIiwicG9zdCIsImVtYWlsX29yX3VzZXJuYW1lIiwicGFzc3dvcmQiLCJpbmNsdWRlcyIsImNvbnNvbGUiLCJ0ZXN0TmFtZSIsInRlc3RGdW5jdGlvbiIsInByZXYiLCJyIiwiZ2V0U3RhdHVzSWNvbiIsImNsYXNzTmFtZSIsImdldFN0YXR1c0JhZGdlIiwidmFyaWFudHMiLCJ3YXJuaW5nIiwicGVuZGluZyIsInZhcmlhbnQiLCJ0b1VwcGVyQ2FzZSIsInN0cm9uZyIsImRpdiIsImluZGV4Iiwib25DbGljayIsImRpc2FibGVkIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9VUkwiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/connection-diagnostic.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/theme-provider.tsx":
/*!*******************************************!*\
  !*** ./src/components/theme-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy90aGVtZS1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUUrQjtBQUNtQztBQUczRCxTQUFTQyxjQUFjLEVBQUVFLFFBQVEsRUFBRSxHQUFHQyxPQUEyQjtJQUN0RSxxQkFBTyw4REFBQ0Ysc0RBQWtCQTtRQUFFLEdBQUdFLEtBQUs7a0JBQUdEOzs7Ozs7QUFDekMiLCJzb3VyY2VzIjpbIkQ6XFxuZXh0anNcXHdlYmFwcFxcZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcdGhlbWUtcHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5cclxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyIH0gZnJvbSBcIm5leHQtdGhlbWVzXCI7XHJcbmltcG9ydCB7IHR5cGUgVGhlbWVQcm92aWRlclByb3BzIH0gZnJvbSBcIm5leHQtdGhlbWVzXCI7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9OiBUaGVtZVByb3ZpZGVyUHJvcHMpIHtcclxuICByZXR1cm4gPE5leHRUaGVtZXNQcm92aWRlciB7Li4ucHJvcHN9PntjaGlsZHJlbn08L05leHRUaGVtZXNQcm92aWRlcj47XHJcbn1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiVGhlbWVQcm92aWRlciIsIk5leHRUaGVtZXNQcm92aWRlciIsImNoaWxkcmVuIiwicHJvcHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/alert.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/alert.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\", {\n    variants: {\n        variant: {\n            default: \"bg-card text-card-foreground border-border\",\n            primary: \"bg-blue-50 dark:bg-blue-950/20 text-blue-900 dark:text-blue-100 border-blue-200 dark:border-blue-800 [&>svg]:text-blue-600 dark:[&>svg]:text-blue-400\",\n            destructive: \"bg-red-50 dark:bg-red-950/20 text-red-900 dark:text-red-100 border-red-200 dark:border-red-800 [&>svg]:text-red-600 dark:[&>svg]:text-red-400\",\n            success: \"bg-green-50 dark:bg-green-950/20 text-green-900 dark:text-green-100 border-green-200 dark:border-green-800 [&>svg]:text-green-600 dark:[&>svg]:text-green-400\",\n            warning: \"bg-yellow-50 dark:bg-yellow-950/20 text-yellow-900 dark:text-yellow-100 border-yellow-200 dark:border-yellow-800 [&>svg]:text-yellow-600 dark:[&>svg]:text-yellow-400\",\n            info: \"bg-blue-50 dark:bg-blue-950/20 text-blue-900 dark:text-blue-100 border-blue-200 dark:border-blue-800 [&>svg]:text-blue-600 dark:[&>svg]:text-blue-400\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Alert({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"alert\",\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\nfunction AlertTitle({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"alert-title\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\nfunction AlertDescription({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"alert-description\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"col-start-2 grid justify-items-start gap-1 text-sm opacity-90 [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/alert.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n            destructive: \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"span\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"badge\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDYTtBQUNzQjtBQUVqQztBQUVoQyxNQUFNSSxpQkFBaUJGLDZEQUFHQSxDQUN4QiwrYkFDQTtJQUNFRyxVQUFVO1FBQ1JDLFNBQVM7WUFDUEMsU0FDRTtZQUNGQyxhQUNFO1lBQ0ZDLFNBQ0U7WUFDRkMsV0FDRTtZQUNGQyxPQUNFO1lBQ0ZDLE1BQU07UUFDUjtRQUNBQyxNQUFNO1lBQ0pOLFNBQVM7WUFDVE8sSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLE1BQU07UUFDUjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmWCxTQUFTO1FBQ1RPLE1BQU07SUFDUjtBQUNGO0FBR0YsU0FBU0ssT0FBTyxFQUNkQyxTQUFTLEVBQ1RiLE9BQU8sRUFDUE8sSUFBSSxFQUNKTyxVQUFVLEtBQUssRUFDZixHQUFHQyxPQUlGO0lBQ0QsTUFBTUMsT0FBT0YsVUFBVW5CLHNEQUFJQSxHQUFHO0lBRTlCLHFCQUNFLDhEQUFDcUI7UUFDQ0MsYUFBVTtRQUNWSixXQUFXaEIsOENBQUVBLENBQUNDLGVBQWU7WUFBRUU7WUFBU087WUFBTU07UUFBVTtRQUN2RCxHQUFHRSxLQUFLOzs7Ozs7QUFHZjtBQUVpQyIsInNvdXJjZXMiOlsiRDpcXG5leHRqc1xcd2ViYXBwXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFx1aVxcYnV0dG9uLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgU2xvdCB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3Qtc2xvdFwiXG5pbXBvcnQgeyBjdmEsIHR5cGUgVmFyaWFudFByb3BzIH0gZnJvbSBcImNsYXNzLXZhcmlhbmNlLWF1dGhvcml0eVwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgYnV0dG9uVmFyaWFudHMgPSBjdmEoXG4gIFwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0yIHdoaXRlc3BhY2Utbm93cmFwIHJvdW5kZWQtbWQgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWFsbCBkaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lIGRpc2FibGVkOm9wYWNpdHktNTAgWyZfc3ZnXTpwb2ludGVyLWV2ZW50cy1ub25lIFsmX3N2Zzpub3QoW2NsYXNzKj0nc2l6ZS0nXSldOnNpemUtNCBzaHJpbmstMCBbJl9zdmddOnNocmluay0wIG91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOmJvcmRlci1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1yaW5nLzUwIGZvY3VzLXZpc2libGU6cmluZy1bM3B4XSBhcmlhLWludmFsaWQ6cmluZy1kZXN0cnVjdGl2ZS8yMCBkYXJrOmFyaWEtaW52YWxpZDpyaW5nLWRlc3RydWN0aXZlLzQwIGFyaWEtaW52YWxpZDpib3JkZXItZGVzdHJ1Y3RpdmVcIixcbiAge1xuICAgIHZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiB7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgXCJiZy1wcmltYXJ5IHRleHQtcHJpbWFyeS1mb3JlZ3JvdW5kIHNoYWRvdy14cyBob3ZlcjpiZy1wcmltYXJ5LzkwXCIsXG4gICAgICAgIGRlc3RydWN0aXZlOlxuICAgICAgICAgIFwiYmctZGVzdHJ1Y3RpdmUgdGV4dC13aGl0ZSBzaGFkb3cteHMgaG92ZXI6YmctZGVzdHJ1Y3RpdmUvOTAgZm9jdXMtdmlzaWJsZTpyaW5nLWRlc3RydWN0aXZlLzIwIGRhcms6Zm9jdXMtdmlzaWJsZTpyaW5nLWRlc3RydWN0aXZlLzQwIGRhcms6YmctZGVzdHJ1Y3RpdmUvNjBcIixcbiAgICAgICAgb3V0bGluZTpcbiAgICAgICAgICBcImJvcmRlciBiZy1iYWNrZ3JvdW5kIHNoYWRvdy14cyBob3ZlcjpiZy1hY2NlbnQgaG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZCBkYXJrOmJnLWlucHV0LzMwIGRhcms6Ym9yZGVyLWlucHV0IGRhcms6aG92ZXI6YmctaW5wdXQvNTBcIixcbiAgICAgICAgc2Vjb25kYXJ5OlxuICAgICAgICAgIFwiYmctc2Vjb25kYXJ5IHRleHQtc2Vjb25kYXJ5LWZvcmVncm91bmQgc2hhZG93LXhzIGhvdmVyOmJnLXNlY29uZGFyeS84MFwiLFxuICAgICAgICBnaG9zdDpcbiAgICAgICAgICBcImhvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kIGRhcms6aG92ZXI6YmctYWNjZW50LzUwXCIsXG4gICAgICAgIGxpbms6IFwidGV4dC1wcmltYXJ5IHVuZGVybGluZS1vZmZzZXQtNCBob3Zlcjp1bmRlcmxpbmVcIixcbiAgICAgIH0sXG4gICAgICBzaXplOiB7XG4gICAgICAgIGRlZmF1bHQ6IFwiaC05IHB4LTQgcHktMiBoYXMtWz5zdmddOnB4LTNcIixcbiAgICAgICAgc206IFwiaC04IHJvdW5kZWQtbWQgZ2FwLTEuNSBweC0zIGhhcy1bPnN2Z106cHgtMi41XCIsXG4gICAgICAgIGxnOiBcImgtMTAgcm91bmRlZC1tZCBweC02IGhhcy1bPnN2Z106cHgtNFwiLFxuICAgICAgICBpY29uOiBcInNpemUtOVwiLFxuICAgICAgfSxcbiAgICB9LFxuICAgIGRlZmF1bHRWYXJpYW50czoge1xuICAgICAgdmFyaWFudDogXCJkZWZhdWx0XCIsXG4gICAgICBzaXplOiBcImRlZmF1bHRcIixcbiAgICB9LFxuICB9XG4pXG5cbmZ1bmN0aW9uIEJ1dHRvbih7XG4gIGNsYXNzTmFtZSxcbiAgdmFyaWFudCxcbiAgc2l6ZSxcbiAgYXNDaGlsZCA9IGZhbHNlLFxuICAuLi5wcm9wc1xufTogUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJidXR0b25cIj4gJlxuICBWYXJpYW50UHJvcHM8dHlwZW9mIGJ1dHRvblZhcmlhbnRzPiAmIHtcbiAgICBhc0NoaWxkPzogYm9vbGVhblxuICB9KSB7XG4gIGNvbnN0IENvbXAgPSBhc0NoaWxkID8gU2xvdCA6IFwiYnV0dG9uXCJcblxuICByZXR1cm4gKFxuICAgIDxDb21wXG4gICAgICBkYXRhLXNsb3Q9XCJidXR0b25cIlxuICAgICAgY2xhc3NOYW1lPXtjbihidXR0b25WYXJpYW50cyh7IHZhcmlhbnQsIHNpemUsIGNsYXNzTmFtZSB9KSl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKVxufVxuXG5leHBvcnQgeyBCdXR0b24sIGJ1dHRvblZhcmlhbnRzIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlNsb3QiLCJjdmEiLCJjbiIsImJ1dHRvblZhcmlhbnRzIiwidmFyaWFudHMiLCJ2YXJpYW50IiwiZGVmYXVsdCIsImRlc3RydWN0aXZlIiwib3V0bGluZSIsInNlY29uZGFyeSIsImdob3N0IiwibGluayIsInNpemUiLCJzbSIsImxnIiwiaWNvbiIsImRlZmF1bHRWYXJpYW50cyIsIkJ1dHRvbiIsImNsYXNzTmFtZSIsImFzQ2hpbGQiLCJwcm9wcyIsIkNvbXAiLCJkYXRhLXNsb3QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardAction: () => (/* binding */ CardAction),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction Card({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\nfunction CardHeader({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-header\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\nfunction CardTitle({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-title\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"leading-none font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\nfunction CardDescription({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-description\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground text-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\nfunction CardAction({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-action\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"col-start-2 row-span-2 row-start-1 self-start justify-self-end\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\nfunction CardContent({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\nfunction CardFooter({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-footer\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center px-6 [.border-t]:pt-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/sonner.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/sonner.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nconst Toaster = ({ ...props })=>{\n    const { theme = \"system\" } = (0,next_themes__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n        theme: theme,\n        className: \"toaster group\",\n        style: {\n            \"--normal-bg\": \"var(--popover)\",\n            \"--normal-text\": \"var(--popover-foreground)\",\n            \"--normal-border\": \"var(--border)\"\n        },\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\ui\\\\sonner.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9zb25uZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVzQztBQUNrQjtBQUV4RCxNQUFNQyxVQUFVLENBQUMsRUFBRSxHQUFHRSxPQUFxQjtJQUN6QyxNQUFNLEVBQUVDLFFBQVEsUUFBUSxFQUFFLEdBQUdKLHFEQUFRQTtJQUVyQyxxQkFDRSw4REFBQ0UsMkNBQU1BO1FBQ0xFLE9BQU9BO1FBQ1BDLFdBQVU7UUFDVkMsT0FDRTtZQUNFLGVBQWU7WUFDZixpQkFBaUI7WUFDakIsbUJBQW1CO1FBQ3JCO1FBRUQsR0FBR0gsS0FBSzs7Ozs7O0FBR2Y7QUFFa0IiLCJzb3VyY2VzIjpbIkQ6XFxuZXh0anNcXHdlYmFwcFxcZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcdWlcXHNvbm5lci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IHsgdXNlVGhlbWUgfSBmcm9tIFwibmV4dC10aGVtZXNcIlxuaW1wb3J0IHsgVG9hc3RlciBhcyBTb25uZXIsIFRvYXN0ZXJQcm9wcyB9IGZyb20gXCJzb25uZXJcIlxuXG5jb25zdCBUb2FzdGVyID0gKHsgLi4ucHJvcHMgfTogVG9hc3RlclByb3BzKSA9PiB7XG4gIGNvbnN0IHsgdGhlbWUgPSBcInN5c3RlbVwiIH0gPSB1c2VUaGVtZSgpXG5cbiAgcmV0dXJuIChcbiAgICA8U29ubmVyXG4gICAgICB0aGVtZT17dGhlbWUgYXMgVG9hc3RlclByb3BzW1widGhlbWVcIl19XG4gICAgICBjbGFzc05hbWU9XCJ0b2FzdGVyIGdyb3VwXCJcbiAgICAgIHN0eWxlPXtcbiAgICAgICAge1xuICAgICAgICAgIFwiLS1ub3JtYWwtYmdcIjogXCJ2YXIoLS1wb3BvdmVyKVwiLFxuICAgICAgICAgIFwiLS1ub3JtYWwtdGV4dFwiOiBcInZhcigtLXBvcG92ZXItZm9yZWdyb3VuZClcIixcbiAgICAgICAgICBcIi0tbm9ybWFsLWJvcmRlclwiOiBcInZhcigtLWJvcmRlcilcIixcbiAgICAgICAgfSBhcyBSZWFjdC5DU1NQcm9wZXJ0aWVzXG4gICAgICB9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKVxufVxuXG5leHBvcnQgeyBUb2FzdGVyIH1cbiJdLCJuYW1lcyI6WyJ1c2VUaGVtZSIsIlRvYXN0ZXIiLCJTb25uZXIiLCJwcm9wcyIsInRoZW1lIiwiY2xhc3NOYW1lIiwic3R5bGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/sonner.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/auth-context.tsx":
/*!***************************************!*\
  !*** ./src/contexts/auth-context.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthContext: () => (/* binding */ AuthContext),\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth-service */ \"(ssr)/./src/lib/auth-service.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,AuthContext auto */ \n\n\n\n\n// Create context\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Idle logout timeout – configurable via env var, defaults to 30 minutes\nconst IDLE_LOGOUT_MINUTES = parseInt(\"30\" ?? 0, 10);\nconst IDLE_LOGOUT_MS = IDLE_LOGOUT_MINUTES * 60000;\n// Authentication Provider Component\nfunction AuthProvider({ children }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // State\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userFromToken, setUserFromToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Reactive authentication state\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // -------------------\n    // Proactive token refresh + idle logout\n    // -------------------\n    const refreshTimeoutRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(null);\n    const idleTimeoutRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(null);\n    const clearTimers = ()=>{\n        if (refreshTimeoutRef.current) {\n            clearTimeout(refreshTimeoutRef.current);\n            refreshTimeoutRef.current = null;\n        }\n        if (idleTimeoutRef.current) {\n            clearTimeout(idleTimeoutRef.current);\n            idleTimeoutRef.current = null;\n        }\n    };\n    // Logout function (moved here so it's defined before use in other hooks)\n    const logout = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"AuthProvider.useCallback[logout]\": async (sessionId, logoutAll = false)=>{\n            // Ensure any scheduled tasks are cancelled BEFORE state updates\n            clearTimers();\n            // Clear authentication state synchronously so UI can react immediately\n            setIsAuthenticated(false);\n            setUser(null);\n            setUserFromToken(null);\n            // Persisted auth data\n            _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.clearAuth();\n            // Attempt server-side logout *after* local cleanup so we don't block UI\n            const shouldCallApiLogout = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isAuthenticated() && !_lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isTokenExpired();\n            if (shouldCallApiLogout) {\n                _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.logout(sessionId, logoutAll).catch(console.warn);\n            }\n            // Use Next.js router navigation rather than full page reload for smoother UX\n            router.replace(\"/login\");\n        }\n    }[\"AuthProvider.useCallback[logout]\"], [\n        router\n    ]);\n    const scheduleTokenRefresh = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"AuthProvider.useCallback[scheduleTokenRefresh]\": ()=>{\n            if (!_lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isAuthenticated()) return;\n            const expDate = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getTokenExpiration();\n            if (!expDate) return;\n            const now = new Date();\n            // Refresh 60 seconds before expiry (or immediately if already <1 min)\n            const msUntilRefresh = Math.max(expDate.getTime() - now.getTime() - 60000, 0);\n            if (refreshTimeoutRef.current) {\n                clearTimeout(refreshTimeoutRef.current);\n            }\n            refreshTimeoutRef.current = setTimeout({\n                \"AuthProvider.useCallback[scheduleTokenRefresh]\": async ()=>{\n                    try {\n                        await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.refreshToken();\n                        scheduleTokenRefresh(); // reschedule for next expiry\n                    } catch (err) {\n                        console.error(\"Token refresh failed\", err);\n                        await logout();\n                    }\n                }\n            }[\"AuthProvider.useCallback[scheduleTokenRefresh]\"], msUntilRefresh);\n        }\n    }[\"AuthProvider.useCallback[scheduleTokenRefresh]\"], [\n        logout\n    ]);\n    const resetIdleTimer = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"AuthProvider.useCallback[resetIdleTimer]\": ()=>{\n            if (idleTimeoutRef.current) {\n                clearTimeout(idleTimeoutRef.current);\n            }\n            idleTimeoutRef.current = setTimeout({\n                \"AuthProvider.useCallback[resetIdleTimer]\": ()=>{\n                    logout();\n                }\n            }[\"AuthProvider.useCallback[resetIdleTimer]\"], IDLE_LOGOUT_MS);\n        }\n    }[\"AuthProvider.useCallback[resetIdleTimer]\"], [\n        logout\n    ]);\n    // ----------------------------------------------\n    // Setup timers only when authentication *state* changes\n    // ----------------------------------------------\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!isAuthenticated) {\n                // User just logged out – ensure timers are cleared\n                clearTimers();\n                return;\n            }\n            // User is authenticated – start / resume timers **once**\n            scheduleTokenRefresh();\n            resetIdleTimer();\n            // Attach user-activity listeners to reset the idle timer\n            const events = [\n                \"mousemove\",\n                \"keydown\",\n                \"click\",\n                // Removing programmatic scroll events avoids accidental resets\n                //'scroll',\n                \"touchstart\"\n            ];\n            events.forEach({\n                \"AuthProvider.useEffect\": (e)=>window.addEventListener(e, resetIdleTimer)\n            }[\"AuthProvider.useEffect\"]);\n            // Cleanup when component unmounts or auth state changes\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    events.forEach({\n                        \"AuthProvider.useEffect\": (e)=>window.removeEventListener(e, resetIdleTimer)\n                    }[\"AuthProvider.useEffect\"]);\n                    clearTimers();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        isAuthenticated,\n        scheduleTokenRefresh,\n        resetIdleTimer\n    ]);\n    // Initialize authentication state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initializeAuth = {\n                \"AuthProvider.useEffect.initializeAuth\": async (retryCount = 0)=>{\n                    try {\n                        setIsLoading(true);\n                        // Check if user is authenticated from token\n                        if (_lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isAuthenticated()) {\n                            const tokenUser = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUserFromToken();\n                            setUserFromToken(tokenUser);\n                            // Test backend connectivity before trying to fetch profile\n                            try {\n                                // Quick health check to verify backend is accessible\n                                await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.testConnection();\n                            } catch (connectionError) {\n                                console.warn(\"Backend not accessible during auth init, skipping profile fetch\");\n                                // Still set token-based auth, user can retry later\n                                return;\n                            }\n                            // Try to fetch full user profile\n                            try {\n                                const userProfile = await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUser();\n                                setUser(userProfile);\n                            } catch (error) {\n                                console.warn(\"Failed to fetch user profile during initialization:\", error);\n                                // Handle different types of errors\n                                if (error && typeof error === \"object\") {\n                                    // Check for HTTP response errors\n                                    if (\"response\" in error) {\n                                        const status = error.response?.status;\n                                        if (status === 401 || status === 403) {\n                                            // Invalid token - clear auth\n                                            console.log(\"Token is invalid, clearing auth\");\n                                            _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.clearAuth();\n                                            setUser(null);\n                                            setUserFromToken(null);\n                                        }\n                                    // For other HTTP errors (404, 500, etc.), keep token-based auth\n                                    } else if (\"message\" in error) {\n                                        const errorMessage = error.message;\n                                        if (errorMessage.includes(\"Network Error\") || errorMessage.includes(\"ECONNREFUSED\") || errorMessage.includes(\"ENOTFOUND\")) {\n                                            // Network connectivity issues - don't clear auth, user can retry later\n                                            console.warn(\"Network error during profile fetch, keeping token-based auth\");\n                                        } else {\n                                            // Other errors - log but don't clear auth\n                                            console.warn(\"Unknown error during profile fetch:\", errorMessage);\n                                        }\n                                    }\n                                }\n                            }\n                        } else {\n                            // Clear any stale data\n                            _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.clearAuth();\n                            setUser(null);\n                            setUserFromToken(null);\n                        }\n                    } catch (error) {\n                        console.error(\"Auth initialization error:\", error);\n                        // Check if it's a network error and we should retry\n                        if (retryCount < 2 && error && typeof error === \"object\" && \"message\" in error) {\n                            const errorMessage = error.message;\n                            if (errorMessage.includes(\"Network Error\") || errorMessage.includes(\"ECONNREFUSED\") || errorMessage.includes(\"ENOTFOUND\")) {\n                                console.log(`Network error during auth init, retrying... (${retryCount + 1}/3)`);\n                                setTimeout({\n                                    \"AuthProvider.useEffect.initializeAuth\": ()=>initializeAuth(retryCount + 1)\n                                }[\"AuthProvider.useEffect.initializeAuth\"], 2000); // Retry after 2 seconds\n                                return;\n                            }\n                        }\n                        // If not a network error or max retries reached, clear auth\n                        _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.clearAuth();\n                        setUser(null);\n                        setUserFromToken(null);\n                    } finally{\n                        setIsLoading(false);\n                        setIsInitialized(true);\n                    }\n                }\n            }[\"AuthProvider.useEffect.initializeAuth\"];\n            initializeAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Refresh user data\n    const refreshUser = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[refreshUser]\": async ()=>{\n            try {\n                // Debug statements removed for production\n                // Force check auth state first\n                const currentAuthState = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isAuthenticated();\n                setIsAuthenticated(currentAuthState);\n                if (!currentAuthState) {\n                    //\n                    return;\n                }\n                // Try to get user profile, but don't fail if it doesn't work\n                try {\n                    const userProfile = await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUser();\n                    setUser(userProfile);\n                } catch (profileError) {\n                    console.warn(\"Failed to refresh user profile:\", profileError);\n                // Keep existing user data if profile fetch fails\n                }\n                // Update token user as well\n                const tokenUser = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUserFromToken();\n                setUserFromToken(tokenUser);\n            //\n            } catch (error) {\n                console.error(\"Failed to refresh user:\", error);\n                console.error(\"Error details:\", {\n                    message: error instanceof Error ? error.message : \"Unknown error\",\n                    status: error && typeof error === \"object\" && \"response\" in error ? error.response?.status : \"No status\"\n                });\n                // If refresh fails, user might be logged out\n                logout();\n            }\n        }\n    }[\"AuthProvider.useCallback[refreshUser]\"], [\n        logout\n    ]);\n    // Login function\n    const login = async (credentials)=>{\n        try {\n            setIsLoading(true);\n            await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.login(credentials);\n            // Get user profile - handle gracefully if it fails\n            try {\n                const userProfile = await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUser();\n                setUser(userProfile);\n            } catch (profileError) {\n                console.warn(\"Failed to get user profile after login:\", profileError);\n            // Don't fail the login if profile fetch fails\n            // The user can still access the app with token-based auth\n            }\n            const tokenUser = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUserFromToken();\n            setUserFromToken(tokenUser);\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Login successful!\");\n            // Redirect to dashboard - role-based rendering will be handled by the dashboard page\n            router.push(\"/dashboard\");\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            const errorMessage = error instanceof Error ? error.message : \"Login failed\";\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(errorMessage);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Register function\n    const register = async (userData)=>{\n        try {\n            setIsLoading(true);\n            const response = await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.register(userData);\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success(response.message || \"Registration successful! Please check your email to verify your account.\");\n            // Redirect to login page\n            router.push(\"/login\");\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            const errorMessage = error instanceof Error ? error.message : \"Registration failed\";\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(errorMessage);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Clear authentication data\n    const clearAuth = ()=>{\n        setUser(null);\n        setUserFromToken(null);\n        _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.clearAuth();\n    };\n    // Check if user has OAuth provider linked\n    const hasOAuthProvider = (provider)=>{\n        return _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.hasOAuthProvider(provider);\n    };\n    // Get OAuth providers list\n    const getOAuthProviders = async ()=>{\n        try {\n            return await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getOAuthProviders();\n        } catch (error) {\n            console.error(\"Failed to get OAuth providers:\", error);\n            return {};\n        }\n    };\n    // Update authentication state when tokens change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const updateAuthState = {\n                \"AuthProvider.useEffect.updateAuthState\": ()=>{\n                    const authState = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isAuthenticated();\n                    //\n                    setIsAuthenticated(authState);\n                }\n            }[\"AuthProvider.useEffect.updateAuthState\"];\n            // Initial check\n            updateAuthState();\n            // Listen for storage changes (when tokens are added/removed)\n            const handleStorageChange = {\n                \"AuthProvider.useEffect.handleStorageChange\": (e)=>{\n                    if (e.key === \"access_token\" || e.key === \"refresh_token\") {\n                        //\n                        updateAuthState();\n                    }\n                }\n            }[\"AuthProvider.useEffect.handleStorageChange\"];\n            window.addEventListener(\"storage\", handleStorageChange);\n            // Also check periodically for token expiration\n            const interval = setInterval(updateAuthState, 5000);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    window.removeEventListener(\"storage\", handleStorageChange);\n                    clearInterval(interval);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Context value\n    const value = {\n        user,\n        userFromToken,\n        isAuthenticated,\n        isLoading,\n        isInitialized,\n        login,\n        register,\n        logout,\n        refreshUser,\n        clearAuth,\n        hasOAuthProvider,\n        getOAuthProviders\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\contexts\\\\auth-context.tsx\",\n        lineNumber: 468,\n        columnNumber: 10\n    }, this);\n}\n// Custom hook to use auth context\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// Export the context for advanced usage\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29udGV4dHMvYXV0aC1jb250ZXh0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQVNlO0FBQzZCO0FBQ2I7QUFPSDtBQXVCNUIsaUJBQWlCO0FBQ2pCLE1BQU1TLDRCQUFjUixvREFBYUEsQ0FBOEJTO0FBTy9ELHlFQUF5RTtBQUN6RSxNQUFNQyxzQkFBc0JDLFNBQzFCQyxJQUEyQyxJQUFJLENBQUksRUFDbkQ7QUFFRixNQUFNRyxpQkFBaUJMLHNCQUFzQjtBQUU3QyxvQ0FBb0M7QUFDN0IsU0FBU00sYUFBYSxFQUFFQyxRQUFRLEVBQXFCO0lBQzFELE1BQU1DLFNBQVNiLDBEQUFTQTtJQUV4QixRQUFRO0lBQ1IsTUFBTSxDQUFDYyxNQUFNQyxRQUFRLEdBQUdqQiwrQ0FBUUEsQ0FBcUI7SUFDckQsTUFBTSxDQUFDa0IsZUFBZUMsaUJBQWlCLEdBQUduQiwrQ0FBUUEsQ0FBb0I7SUFDdEUsTUFBTSxDQUFDb0IsV0FBV0MsYUFBYSxHQUFHckIsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDc0IsZUFBZUMsaUJBQWlCLEdBQUd2QiwrQ0FBUUEsQ0FBQztJQUVuRCxnQ0FBZ0M7SUFDaEMsTUFBTSxDQUFDd0IsaUJBQWlCQyxtQkFBbUIsR0FBR3pCLCtDQUFRQSxDQUFDO0lBRXZELHNCQUFzQjtJQUN0Qix3Q0FBd0M7SUFDeEMsc0JBQXNCO0lBQ3RCLE1BQU0wQixvQkFBb0I5QixtREFBWSxDQUNwQztJQUVGLE1BQU1nQyxpQkFBaUJoQyxtREFBWSxDQUNqQztJQUdGLE1BQU1pQyxjQUFjO1FBQ2xCLElBQUlILGtCQUFrQkksT0FBTyxFQUFFO1lBQzdCQyxhQUFhTCxrQkFBa0JJLE9BQU87WUFDdENKLGtCQUFrQkksT0FBTyxHQUFHO1FBQzlCO1FBQ0EsSUFBSUYsZUFBZUUsT0FBTyxFQUFFO1lBQzFCQyxhQUFhSCxlQUFlRSxPQUFPO1lBQ25DRixlQUFlRSxPQUFPLEdBQUc7UUFDM0I7SUFDRjtJQUVBLHlFQUF5RTtJQUN6RSxNQUFNRSxTQUFTcEMsd0RBQWlCOzRDQUM5QixPQUFPcUMsV0FBb0JDLFlBQXFCLEtBQUs7WUFDbkQsZ0VBQWdFO1lBQ2hFTDtZQUVBLHVFQUF1RTtZQUN2RUosbUJBQW1CO1lBQ25CUixRQUFRO1lBQ1JFLGlCQUFpQjtZQUVqQixzQkFBc0I7WUFDdEJmLDBEQUFXQSxDQUFDK0IsU0FBUztZQUVyQix3RUFBd0U7WUFDeEUsTUFBTUMsc0JBQ0poQywwREFBV0EsQ0FBQ29CLGVBQWUsTUFBTSxDQUFDcEIsMERBQVdBLENBQUNpQyxjQUFjO1lBRTlELElBQUlELHFCQUFxQjtnQkFDdkJoQywwREFBV0EsQ0FBQzRCLE1BQU0sQ0FBQ0MsV0FBV0MsV0FBV0ksS0FBSyxDQUFDQyxRQUFRQyxJQUFJO1lBQzdEO1lBRUEsNkVBQTZFO1lBQzdFekIsT0FBTzBCLE9BQU8sQ0FBQztRQUNqQjsyQ0FDQTtRQUFDMUI7S0FBTztJQUdWLE1BQU0yQix1QkFBdUI5Qyx3REFBaUI7MERBQUM7WUFDN0MsSUFBSSxDQUFDUSwwREFBV0EsQ0FBQ29CLGVBQWUsSUFBSTtZQUNwQyxNQUFNbUIsVUFBVXZDLDBEQUFXQSxDQUFDd0Msa0JBQWtCO1lBQzlDLElBQUksQ0FBQ0QsU0FBUztZQUNkLE1BQU1FLE1BQU0sSUFBSUM7WUFDaEIsc0VBQXNFO1lBQ3RFLE1BQU1DLGlCQUFpQkMsS0FBS0MsR0FBRyxDQUM3Qk4sUUFBUU8sT0FBTyxLQUFLTCxJQUFJSyxPQUFPLEtBQUssT0FDcEM7WUFFRixJQUFJeEIsa0JBQWtCSSxPQUFPLEVBQUU7Z0JBQzdCQyxhQUFhTCxrQkFBa0JJLE9BQU87WUFDeEM7WUFDQUosa0JBQWtCSSxPQUFPLEdBQUdxQjtrRUFBVztvQkFDckMsSUFBSTt3QkFDRixNQUFNL0MsMERBQVdBLENBQUNnRCxZQUFZO3dCQUM5QlYsd0JBQXdCLDZCQUE2QjtvQkFDdkQsRUFBRSxPQUFPVyxLQUFLO3dCQUNaZCxRQUFRZSxLQUFLLENBQUMsd0JBQXdCRDt3QkFDdEMsTUFBTXJCO29CQUNSO2dCQUNGO2lFQUFHZTtRQUNMO3lEQUFHO1FBQUNmO0tBQU87SUFFWCxNQUFNdUIsaUJBQWlCM0Qsd0RBQWlCO29EQUFDO1lBQ3ZDLElBQUlnQyxlQUFlRSxPQUFPLEVBQUU7Z0JBQzFCQyxhQUFhSCxlQUFlRSxPQUFPO1lBQ3JDO1lBQ0FGLGVBQWVFLE9BQU8sR0FBR3FCOzREQUFXO29CQUNsQ25CO2dCQUNGOzJEQUFHcEI7UUFDTDttREFBRztRQUFDb0I7S0FBTztJQUVYLGlEQUFpRDtJQUNqRCx3REFBd0Q7SUFDeEQsaURBQWlEO0lBQ2pEcEMsc0RBQWU7a0NBQUM7WUFDZCxJQUFJLENBQUM0QixpQkFBaUI7Z0JBQ3BCLG1EQUFtRDtnQkFDbkRLO2dCQUNBO1lBQ0Y7WUFFQSx5REFBeUQ7WUFDekRhO1lBQ0FhO1lBRUEseURBQXlEO1lBQ3pELE1BQU1DLFNBQVM7Z0JBQ2I7Z0JBQ0E7Z0JBQ0E7Z0JBQ0EsK0RBQStEO2dCQUMvRCxXQUFXO2dCQUNYO2FBQ0Q7WUFFREEsT0FBT0MsT0FBTzswQ0FBQyxDQUFDQyxJQUFNQyxPQUFPQyxnQkFBZ0IsQ0FBQ0YsR0FBR0g7O1lBRWpELHdEQUF3RDtZQUN4RDswQ0FBTztvQkFDTEMsT0FBT0MsT0FBTztrREFBQyxDQUFDQyxJQUFNQyxPQUFPRSxtQkFBbUIsQ0FBQ0gsR0FBR0g7O29CQUNwRDFCO2dCQUNGOztRQUNGO2lDQUFHO1FBQUNMO1FBQWlCa0I7UUFBc0JhO0tBQWU7SUFFMUQsa0NBQWtDO0lBQ2xDeEQsZ0RBQVNBO2tDQUFDO1lBQ1IsTUFBTStEO3lEQUFpQixPQUFPQyxhQUFhLENBQUM7b0JBQzFDLElBQUk7d0JBQ0YxQyxhQUFhO3dCQUViLDRDQUE0Qzt3QkFDNUMsSUFBSWpCLDBEQUFXQSxDQUFDb0IsZUFBZSxJQUFJOzRCQUNqQyxNQUFNd0MsWUFBWTVELDBEQUFXQSxDQUFDNkQsdUJBQXVCOzRCQUNyRDlDLGlCQUFpQjZDOzRCQUVqQiwyREFBMkQ7NEJBQzNELElBQUk7Z0NBQ0YscURBQXFEO2dDQUNyRCxNQUFNNUQsMERBQVdBLENBQUM4RCxjQUFjOzRCQUNsQyxFQUFFLE9BQU9DLGlCQUFpQjtnQ0FDeEI1QixRQUFRQyxJQUFJLENBQ1Y7Z0NBRUYsbURBQW1EO2dDQUNuRDs0QkFDRjs0QkFFQSxpQ0FBaUM7NEJBQ2pDLElBQUk7Z0NBQ0YsTUFBTTRCLGNBQWMsTUFBTWhFLDBEQUFXQSxDQUFDaUUsY0FBYztnQ0FDcERwRCxRQUFRbUQ7NEJBQ1YsRUFBRSxPQUFPZCxPQUFPO2dDQUNkZixRQUFRQyxJQUFJLENBQ1YsdURBQ0FjO2dDQUdGLG1DQUFtQztnQ0FDbkMsSUFBSUEsU0FBUyxPQUFPQSxVQUFVLFVBQVU7b0NBQ3RDLGlDQUFpQztvQ0FDakMsSUFBSSxjQUFjQSxPQUFPO3dDQUN2QixNQUFNZ0IsU0FBUyxNQUNaQyxRQUFRLEVBQUVEO3dDQUNiLElBQUlBLFdBQVcsT0FBT0EsV0FBVyxLQUFLOzRDQUNwQyw2QkFBNkI7NENBQzdCL0IsUUFBUWlDLEdBQUcsQ0FBQzs0Q0FDWnBFLDBEQUFXQSxDQUFDK0IsU0FBUzs0Q0FDckJsQixRQUFROzRDQUNSRSxpQkFBaUI7d0NBQ25CO29DQUNBLGdFQUFnRTtvQ0FDbEUsT0FBTyxJQUFJLGFBQWFtQyxPQUFPO3dDQUM3QixNQUFNbUIsZUFBZSxNQUErQkMsT0FBTzt3Q0FDM0QsSUFDRUQsYUFBYUUsUUFBUSxDQUFDLG9CQUN0QkYsYUFBYUUsUUFBUSxDQUFDLG1CQUN0QkYsYUFBYUUsUUFBUSxDQUFDLGNBQ3RCOzRDQUNBLHVFQUF1RTs0Q0FDdkVwQyxRQUFRQyxJQUFJLENBQ1Y7d0NBRUosT0FBTzs0Q0FDTCwwQ0FBMEM7NENBQzFDRCxRQUFRQyxJQUFJLENBQ1YsdUNBQ0FpQzt3Q0FFSjtvQ0FDRjtnQ0FDRjs0QkFDRjt3QkFDRixPQUFPOzRCQUNMLHVCQUF1Qjs0QkFDdkJyRSwwREFBV0EsQ0FBQytCLFNBQVM7NEJBQ3JCbEIsUUFBUTs0QkFDUkUsaUJBQWlCO3dCQUNuQjtvQkFDRixFQUFFLE9BQU9tQyxPQUFPO3dCQUNkZixRQUFRZSxLQUFLLENBQUMsOEJBQThCQTt3QkFFNUMsb0RBQW9EO3dCQUNwRCxJQUNFUyxhQUFhLEtBQ2JULFNBQ0EsT0FBT0EsVUFBVSxZQUNqQixhQUFhQSxPQUNiOzRCQUNBLE1BQU1tQixlQUFlLE1BQStCQyxPQUFPOzRCQUMzRCxJQUNFRCxhQUFhRSxRQUFRLENBQUMsb0JBQ3RCRixhQUFhRSxRQUFRLENBQUMsbUJBQ3RCRixhQUFhRSxRQUFRLENBQUMsY0FDdEI7Z0NBQ0FwQyxRQUFRaUMsR0FBRyxDQUNULENBQUMsNkNBQTZDLEVBQzVDVCxhQUFhLEVBQ2QsR0FBRyxDQUFDO2dDQUVQWjs2RUFBVyxJQUFNVyxlQUFlQyxhQUFhOzRFQUFJLE9BQU8sd0JBQXdCO2dDQUNoRjs0QkFDRjt3QkFDRjt3QkFFQSw0REFBNEQ7d0JBQzVEM0QsMERBQVdBLENBQUMrQixTQUFTO3dCQUNyQmxCLFFBQVE7d0JBQ1JFLGlCQUFpQjtvQkFDbkIsU0FBVTt3QkFDUkUsYUFBYTt3QkFDYkUsaUJBQWlCO29CQUNuQjtnQkFDRjs7WUFFQXVDO1FBQ0Y7aUNBQUcsRUFBRTtJQUVMLG9CQUFvQjtJQUNwQixNQUFNYyxjQUFjM0Usa0RBQVdBO2lEQUFDO1lBQzlCLElBQUk7Z0JBQ0YsMENBQTBDO2dCQUUxQywrQkFBK0I7Z0JBQy9CLE1BQU00RSxtQkFBbUJ6RSwwREFBV0EsQ0FBQ29CLGVBQWU7Z0JBQ3BEQyxtQkFBbUJvRDtnQkFFbkIsSUFBSSxDQUFDQSxrQkFBa0I7b0JBQ3JCLEVBQUU7b0JBQ0Y7Z0JBQ0Y7Z0JBRUEsNkRBQTZEO2dCQUM3RCxJQUFJO29CQUNGLE1BQU1ULGNBQWMsTUFBTWhFLDBEQUFXQSxDQUFDaUUsY0FBYztvQkFDcERwRCxRQUFRbUQ7Z0JBQ1YsRUFBRSxPQUFPVSxjQUFjO29CQUNyQnZDLFFBQVFDLElBQUksQ0FBQyxtQ0FBbUNzQztnQkFDaEQsaURBQWlEO2dCQUNuRDtnQkFFQSw0QkFBNEI7Z0JBQzVCLE1BQU1kLFlBQVk1RCwwREFBV0EsQ0FBQzZELHVCQUF1QjtnQkFDckQ5QyxpQkFBaUI2QztZQUVqQixFQUFFO1lBQ0osRUFBRSxPQUFPVixPQUFPO2dCQUNkZixRQUFRZSxLQUFLLENBQUMsMkJBQTJCQTtnQkFDekNmLFFBQVFlLEtBQUssQ0FBQyxrQkFBa0I7b0JBQzlCb0IsU0FBU3BCLGlCQUFpQnlCLFFBQVF6QixNQUFNb0IsT0FBTyxHQUFHO29CQUNsREosUUFDRWhCLFNBQVMsT0FBT0EsVUFBVSxZQUFZLGNBQWNBLFFBQ2hELE1BQTZDaUIsUUFBUSxFQUFFRCxTQUN2RDtnQkFDUjtnQkFDQSw2Q0FBNkM7Z0JBQzdDdEM7WUFDRjtRQUNGO2dEQUFHO1FBQUNBO0tBQU87SUFFWCxpQkFBaUI7SUFDakIsTUFBTWdELFFBQVEsT0FBT0M7UUFDbkIsSUFBSTtZQUNGNUQsYUFBYTtZQUViLE1BQU1qQiwwREFBV0EsQ0FBQzRFLEtBQUssQ0FBQ0M7WUFFeEIsbURBQW1EO1lBQ25ELElBQUk7Z0JBQ0YsTUFBTWIsY0FBYyxNQUFNaEUsMERBQVdBLENBQUNpRSxjQUFjO2dCQUNwRHBELFFBQVFtRDtZQUNWLEVBQUUsT0FBT1UsY0FBYztnQkFDckJ2QyxRQUFRQyxJQUFJLENBQUMsMkNBQTJDc0M7WUFDeEQsOENBQThDO1lBQzlDLDBEQUEwRDtZQUM1RDtZQUVBLE1BQU1kLFlBQVk1RCwwREFBV0EsQ0FBQzZELHVCQUF1QjtZQUNyRDlDLGlCQUFpQjZDO1lBRWpCN0QseUNBQUtBLENBQUMrRSxPQUFPLENBQUM7WUFFZCxxRkFBcUY7WUFDckZuRSxPQUFPb0UsSUFBSSxDQUFDO1FBQ2QsRUFBRSxPQUFPN0IsT0FBZ0I7WUFDdkJmLFFBQVFlLEtBQUssQ0FBQyxnQkFBZ0JBO1lBQzlCLE1BQU1tQixlQUNKbkIsaUJBQWlCeUIsUUFBUXpCLE1BQU1vQixPQUFPLEdBQUc7WUFDM0N2RSx5Q0FBS0EsQ0FBQ21ELEtBQUssQ0FBQ21CO1lBQ1osTUFBTW5CO1FBQ1IsU0FBVTtZQUNSakMsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxvQkFBb0I7SUFDcEIsTUFBTStELFdBQVcsT0FBT0M7UUFDdEIsSUFBSTtZQUNGaEUsYUFBYTtZQUViLE1BQU1rRCxXQUFXLE1BQU1uRSwwREFBV0EsQ0FBQ2dGLFFBQVEsQ0FBQ0M7WUFFNUNsRix5Q0FBS0EsQ0FBQytFLE9BQU8sQ0FDWFgsU0FBU0csT0FBTyxJQUNkO1lBR0oseUJBQXlCO1lBQ3pCM0QsT0FBT29FLElBQUksQ0FBQztRQUNkLEVBQUUsT0FBTzdCLE9BQWdCO1lBQ3ZCZixRQUFRZSxLQUFLLENBQUMsdUJBQXVCQTtZQUNyQyxNQUFNbUIsZUFDSm5CLGlCQUFpQnlCLFFBQVF6QixNQUFNb0IsT0FBTyxHQUFHO1lBQzNDdkUseUNBQUtBLENBQUNtRCxLQUFLLENBQUNtQjtZQUNaLE1BQU1uQjtRQUNSLFNBQVU7WUFDUmpDLGFBQWE7UUFDZjtJQUNGO0lBRUEsNEJBQTRCO0lBQzVCLE1BQU1jLFlBQVk7UUFDaEJsQixRQUFRO1FBQ1JFLGlCQUFpQjtRQUNqQmYsMERBQVdBLENBQUMrQixTQUFTO0lBQ3ZCO0lBRUEsMENBQTBDO0lBQzFDLE1BQU1tRCxtQkFBbUIsQ0FBQ0M7UUFDeEIsT0FBT25GLDBEQUFXQSxDQUFDa0YsZ0JBQWdCLENBQUNDO0lBQ3RDO0lBRUEsMkJBQTJCO0lBQzNCLE1BQU1DLG9CQUFvQjtRQUN4QixJQUFJO1lBQ0YsT0FBTyxNQUFNcEYsMERBQVdBLENBQUNvRixpQkFBaUI7UUFDNUMsRUFBRSxPQUFPbEMsT0FBTztZQUNkZixRQUFRZSxLQUFLLENBQUMsa0NBQWtDQTtZQUNoRCxPQUFPLENBQUM7UUFDVjtJQUNGO0lBRUEsaURBQWlEO0lBQ2pEdkQsZ0RBQVNBO2tDQUFDO1lBQ1IsTUFBTTBGOzBEQUFrQjtvQkFDdEIsTUFBTUMsWUFBWXRGLDBEQUFXQSxDQUFDb0IsZUFBZTtvQkFDN0MsRUFBRTtvQkFDRkMsbUJBQW1CaUU7Z0JBQ3JCOztZQUVBLGdCQUFnQjtZQUNoQkQ7WUFFQSw2REFBNkQ7WUFDN0QsTUFBTUU7OERBQXNCLENBQUNqQztvQkFDM0IsSUFBSUEsRUFBRWtDLEdBQUcsS0FBSyxrQkFBa0JsQyxFQUFFa0MsR0FBRyxLQUFLLGlCQUFpQjt3QkFDekQsRUFBRTt3QkFDRkg7b0JBQ0Y7Z0JBQ0Y7O1lBRUE5QixPQUFPQyxnQkFBZ0IsQ0FBQyxXQUFXK0I7WUFFbkMsK0NBQStDO1lBQy9DLE1BQU1FLFdBQVdDLFlBQVlMLGlCQUFpQjtZQUU5QzswQ0FBTztvQkFDTDlCLE9BQU9FLG1CQUFtQixDQUFDLFdBQVc4QjtvQkFDdENJLGNBQWNGO2dCQUNoQjs7UUFDRjtpQ0FBRyxFQUFFO0lBRUwsZ0JBQWdCO0lBQ2hCLE1BQU1HLFFBQXlCO1FBQzdCaEY7UUFDQUU7UUFDQU07UUFDQUo7UUFDQUU7UUFDQTBEO1FBQ0FJO1FBQ0FwRDtRQUNBNEM7UUFDQXpDO1FBQ0FtRDtRQUNBRTtJQUNGO0lBRUEscUJBQU8sOERBQUNuRixZQUFZNEYsUUFBUTtRQUFDRCxPQUFPQTtrQkFBUWxGOzs7Ozs7QUFDOUM7QUFFQSxrQ0FBa0M7QUFDM0IsU0FBU29GO0lBQ2QsTUFBTUMsVUFBVXJHLGlEQUFVQSxDQUFDTztJQUUzQixJQUFJOEYsWUFBWTdGLFdBQVc7UUFDekIsTUFBTSxJQUFJeUUsTUFBTTtJQUNsQjtJQUVBLE9BQU9vQjtBQUNUO0FBRUEsd0NBQXdDO0FBQ2pCIiwic291cmNlcyI6WyJEOlxcbmV4dGpzXFx3ZWJhcHBcXGZyb250ZW5kXFxzcmNcXGNvbnRleHRzXFxhdXRoLWNvbnRleHQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgUmVhY3QsIHtcbiAgY3JlYXRlQ29udGV4dCxcbiAgdXNlQ29udGV4dCxcbiAgdXNlRWZmZWN0LFxuICB1c2VTdGF0ZSxcbiAgUmVhY3ROb2RlLFxuICB1c2VDYWxsYmFjayxcbn0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCI7XG5pbXBvcnQgeyB0b2FzdCB9IGZyb20gXCJzb25uZXJcIjtcbmltcG9ydCB7XG4gIGF1dGhTZXJ2aWNlLFxuICBVc2VyUHJvZmlsZSxcbiAgSldUUGF5bG9hZCxcbiAgTG9naW5SZXF1ZXN0LFxuICBSZWdpc3RlclJlcXVlc3QsXG59IGZyb20gXCJAL2xpYi9hdXRoLXNlcnZpY2VcIjtcblxuLy8gQXV0aGVudGljYXRpb24gQ29udGV4dCBUeXBlc1xuaW50ZXJmYWNlIEF1dGhDb250ZXh0VHlwZSB7XG4gIC8vIFN0YXRlXG4gIHVzZXI6IFVzZXJQcm9maWxlIHwgbnVsbDtcbiAgdXNlckZyb21Ub2tlbjogSldUUGF5bG9hZCB8IG51bGw7XG4gIGlzQXV0aGVudGljYXRlZDogYm9vbGVhbjtcbiAgaXNMb2FkaW5nOiBib29sZWFuO1xuICBpc0luaXRpYWxpemVkOiBib29sZWFuO1xuXG4gIC8vIEFjdGlvbnNcbiAgbG9naW46IChjcmVkZW50aWFsczogTG9naW5SZXF1ZXN0KSA9PiBQcm9taXNlPHZvaWQ+O1xuICByZWdpc3RlcjogKHVzZXJEYXRhOiBSZWdpc3RlclJlcXVlc3QpID0+IFByb21pc2U8dm9pZD47XG4gIGxvZ291dDogKHNlc3Npb25JZD86IHN0cmluZywgbG9nb3V0QWxsPzogYm9vbGVhbikgPT4gUHJvbWlzZTx2b2lkPjtcbiAgcmVmcmVzaFVzZXI6ICgpID0+IFByb21pc2U8dm9pZD47XG4gIGNsZWFyQXV0aDogKCkgPT4gdm9pZDtcblxuICAvLyBPQXV0aFxuICBoYXNPQXV0aFByb3ZpZGVyOiAocHJvdmlkZXI6IHN0cmluZykgPT4gYm9vbGVhbjtcbiAgZ2V0T0F1dGhQcm92aWRlcnM6ICgpID0+IFByb21pc2U8UmVjb3JkPHN0cmluZywgc3RyaW5nPj47XG59XG5cbi8vIENyZWF0ZSBjb250ZXh0XG5jb25zdCBBdXRoQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8QXV0aENvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuXG4vLyBQcm92aWRlciBwcm9wc1xuaW50ZXJmYWNlIEF1dGhQcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcbn1cblxuLy8gSWRsZSBsb2dvdXQgdGltZW91dCDigJMgY29uZmlndXJhYmxlIHZpYSBlbnYgdmFyLCBkZWZhdWx0cyB0byAzMCBtaW51dGVzXG5jb25zdCBJRExFX0xPR09VVF9NSU5VVEVTID0gcGFyc2VJbnQoXG4gIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0lETEVfTE9HT1VUX01JTlVURVMgPz8gXCIzMFwiLFxuICAxMFxuKTtcbmNvbnN0IElETEVfTE9HT1VUX01TID0gSURMRV9MT0dPVVRfTUlOVVRFUyAqIDYwXzAwMDtcblxuLy8gQXV0aGVudGljYXRpb24gUHJvdmlkZXIgQ29tcG9uZW50XG5leHBvcnQgZnVuY3Rpb24gQXV0aFByb3ZpZGVyKHsgY2hpbGRyZW4gfTogQXV0aFByb3ZpZGVyUHJvcHMpIHtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG5cbiAgLy8gU3RhdGVcbiAgY29uc3QgW3VzZXIsIHNldFVzZXJdID0gdXNlU3RhdGU8VXNlclByb2ZpbGUgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3VzZXJGcm9tVG9rZW4sIHNldFVzZXJGcm9tVG9rZW5dID0gdXNlU3RhdGU8SldUUGF5bG9hZCB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtpc0luaXRpYWxpemVkLCBzZXRJc0luaXRpYWxpemVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICAvLyBSZWFjdGl2ZSBhdXRoZW50aWNhdGlvbiBzdGF0ZVxuICBjb25zdCBbaXNBdXRoZW50aWNhdGVkLCBzZXRJc0F1dGhlbnRpY2F0ZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS1cbiAgLy8gUHJvYWN0aXZlIHRva2VuIHJlZnJlc2ggKyBpZGxlIGxvZ291dFxuICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tXG4gIGNvbnN0IHJlZnJlc2hUaW1lb3V0UmVmID0gUmVhY3QudXNlUmVmPFJldHVyblR5cGU8dHlwZW9mIHNldFRpbWVvdXQ+IHwgbnVsbD4oXG4gICAgbnVsbFxuICApO1xuICBjb25zdCBpZGxlVGltZW91dFJlZiA9IFJlYWN0LnVzZVJlZjxSZXR1cm5UeXBlPHR5cGVvZiBzZXRUaW1lb3V0PiB8IG51bGw+KFxuICAgIG51bGxcbiAgKTtcblxuICBjb25zdCBjbGVhclRpbWVycyA9ICgpID0+IHtcbiAgICBpZiAocmVmcmVzaFRpbWVvdXRSZWYuY3VycmVudCkge1xuICAgICAgY2xlYXJUaW1lb3V0KHJlZnJlc2hUaW1lb3V0UmVmLmN1cnJlbnQpO1xuICAgICAgcmVmcmVzaFRpbWVvdXRSZWYuY3VycmVudCA9IG51bGw7XG4gICAgfVxuICAgIGlmIChpZGxlVGltZW91dFJlZi5jdXJyZW50KSB7XG4gICAgICBjbGVhclRpbWVvdXQoaWRsZVRpbWVvdXRSZWYuY3VycmVudCk7XG4gICAgICBpZGxlVGltZW91dFJlZi5jdXJyZW50ID0gbnVsbDtcbiAgICB9XG4gIH07XG5cbiAgLy8gTG9nb3V0IGZ1bmN0aW9uIChtb3ZlZCBoZXJlIHNvIGl0J3MgZGVmaW5lZCBiZWZvcmUgdXNlIGluIG90aGVyIGhvb2tzKVxuICBjb25zdCBsb2dvdXQgPSBSZWFjdC51c2VDYWxsYmFjayhcbiAgICBhc3luYyAoc2Vzc2lvbklkPzogc3RyaW5nLCBsb2dvdXRBbGw6IGJvb2xlYW4gPSBmYWxzZSkgPT4ge1xuICAgICAgLy8gRW5zdXJlIGFueSBzY2hlZHVsZWQgdGFza3MgYXJlIGNhbmNlbGxlZCBCRUZPUkUgc3RhdGUgdXBkYXRlc1xuICAgICAgY2xlYXJUaW1lcnMoKTtcblxuICAgICAgLy8gQ2xlYXIgYXV0aGVudGljYXRpb24gc3RhdGUgc3luY2hyb25vdXNseSBzbyBVSSBjYW4gcmVhY3QgaW1tZWRpYXRlbHlcbiAgICAgIHNldElzQXV0aGVudGljYXRlZChmYWxzZSk7XG4gICAgICBzZXRVc2VyKG51bGwpO1xuICAgICAgc2V0VXNlckZyb21Ub2tlbihudWxsKTtcblxuICAgICAgLy8gUGVyc2lzdGVkIGF1dGggZGF0YVxuICAgICAgYXV0aFNlcnZpY2UuY2xlYXJBdXRoKCk7XG5cbiAgICAgIC8vIEF0dGVtcHQgc2VydmVyLXNpZGUgbG9nb3V0ICphZnRlciogbG9jYWwgY2xlYW51cCBzbyB3ZSBkb24ndCBibG9jayBVSVxuICAgICAgY29uc3Qgc2hvdWxkQ2FsbEFwaUxvZ291dCA9XG4gICAgICAgIGF1dGhTZXJ2aWNlLmlzQXV0aGVudGljYXRlZCgpICYmICFhdXRoU2VydmljZS5pc1Rva2VuRXhwaXJlZCgpO1xuXG4gICAgICBpZiAoc2hvdWxkQ2FsbEFwaUxvZ291dCkge1xuICAgICAgICBhdXRoU2VydmljZS5sb2dvdXQoc2Vzc2lvbklkLCBsb2dvdXRBbGwpLmNhdGNoKGNvbnNvbGUud2Fybik7XG4gICAgICB9XG5cbiAgICAgIC8vIFVzZSBOZXh0LmpzIHJvdXRlciBuYXZpZ2F0aW9uIHJhdGhlciB0aGFuIGZ1bGwgcGFnZSByZWxvYWQgZm9yIHNtb290aGVyIFVYXG4gICAgICByb3V0ZXIucmVwbGFjZShcIi9sb2dpblwiKTtcbiAgICB9LFxuICAgIFtyb3V0ZXJdXG4gICk7XG5cbiAgY29uc3Qgc2NoZWR1bGVUb2tlblJlZnJlc2ggPSBSZWFjdC51c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKCFhdXRoU2VydmljZS5pc0F1dGhlbnRpY2F0ZWQoKSkgcmV0dXJuO1xuICAgIGNvbnN0IGV4cERhdGUgPSBhdXRoU2VydmljZS5nZXRUb2tlbkV4cGlyYXRpb24oKTtcbiAgICBpZiAoIWV4cERhdGUpIHJldHVybjtcbiAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpO1xuICAgIC8vIFJlZnJlc2ggNjAgc2Vjb25kcyBiZWZvcmUgZXhwaXJ5IChvciBpbW1lZGlhdGVseSBpZiBhbHJlYWR5IDwxIG1pbilcbiAgICBjb25zdCBtc1VudGlsUmVmcmVzaCA9IE1hdGgubWF4KFxuICAgICAgZXhwRGF0ZS5nZXRUaW1lKCkgLSBub3cuZ2V0VGltZSgpIC0gNjBfMDAwLFxuICAgICAgMFxuICAgICk7XG4gICAgaWYgKHJlZnJlc2hUaW1lb3V0UmVmLmN1cnJlbnQpIHtcbiAgICAgIGNsZWFyVGltZW91dChyZWZyZXNoVGltZW91dFJlZi5jdXJyZW50KTtcbiAgICB9XG4gICAgcmVmcmVzaFRpbWVvdXRSZWYuY3VycmVudCA9IHNldFRpbWVvdXQoYXN5bmMgKCkgPT4ge1xuICAgICAgdHJ5IHtcbiAgICAgICAgYXdhaXQgYXV0aFNlcnZpY2UucmVmcmVzaFRva2VuKCk7XG4gICAgICAgIHNjaGVkdWxlVG9rZW5SZWZyZXNoKCk7IC8vIHJlc2NoZWR1bGUgZm9yIG5leHQgZXhwaXJ5XG4gICAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcihcIlRva2VuIHJlZnJlc2ggZmFpbGVkXCIsIGVycik7XG4gICAgICAgIGF3YWl0IGxvZ291dCgpO1xuICAgICAgfVxuICAgIH0sIG1zVW50aWxSZWZyZXNoKTtcbiAgfSwgW2xvZ291dF0pO1xuXG4gIGNvbnN0IHJlc2V0SWRsZVRpbWVyID0gUmVhY3QudXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGlmIChpZGxlVGltZW91dFJlZi5jdXJyZW50KSB7XG4gICAgICBjbGVhclRpbWVvdXQoaWRsZVRpbWVvdXRSZWYuY3VycmVudCk7XG4gICAgfVxuICAgIGlkbGVUaW1lb3V0UmVmLmN1cnJlbnQgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIGxvZ291dCgpO1xuICAgIH0sIElETEVfTE9HT1VUX01TKTtcbiAgfSwgW2xvZ291dF0pO1xuXG4gIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAgLy8gU2V0dXAgdGltZXJzIG9ubHkgd2hlbiBhdXRoZW50aWNhdGlvbiAqc3RhdGUqIGNoYW5nZXNcbiAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghaXNBdXRoZW50aWNhdGVkKSB7XG4gICAgICAvLyBVc2VyIGp1c3QgbG9nZ2VkIG91dCDigJMgZW5zdXJlIHRpbWVycyBhcmUgY2xlYXJlZFxuICAgICAgY2xlYXJUaW1lcnMoKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICAvLyBVc2VyIGlzIGF1dGhlbnRpY2F0ZWQg4oCTIHN0YXJ0IC8gcmVzdW1lIHRpbWVycyAqKm9uY2UqKlxuICAgIHNjaGVkdWxlVG9rZW5SZWZyZXNoKCk7XG4gICAgcmVzZXRJZGxlVGltZXIoKTtcblxuICAgIC8vIEF0dGFjaCB1c2VyLWFjdGl2aXR5IGxpc3RlbmVycyB0byByZXNldCB0aGUgaWRsZSB0aW1lclxuICAgIGNvbnN0IGV2ZW50cyA9IFtcbiAgICAgIFwibW91c2Vtb3ZlXCIsXG4gICAgICBcImtleWRvd25cIixcbiAgICAgIFwiY2xpY2tcIixcbiAgICAgIC8vIFJlbW92aW5nIHByb2dyYW1tYXRpYyBzY3JvbGwgZXZlbnRzIGF2b2lkcyBhY2NpZGVudGFsIHJlc2V0c1xuICAgICAgLy8nc2Nyb2xsJyxcbiAgICAgIFwidG91Y2hzdGFydFwiLFxuICAgIF0gYXMgY29uc3Q7XG5cbiAgICBldmVudHMuZm9yRWFjaCgoZSkgPT4gd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoZSwgcmVzZXRJZGxlVGltZXIpKTtcblxuICAgIC8vIENsZWFudXAgd2hlbiBjb21wb25lbnQgdW5tb3VudHMgb3IgYXV0aCBzdGF0ZSBjaGFuZ2VzXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGV2ZW50cy5mb3JFYWNoKChlKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcihlLCByZXNldElkbGVUaW1lcikpO1xuICAgICAgY2xlYXJUaW1lcnMoKTtcbiAgICB9O1xuICB9LCBbaXNBdXRoZW50aWNhdGVkLCBzY2hlZHVsZVRva2VuUmVmcmVzaCwgcmVzZXRJZGxlVGltZXJdKTtcblxuICAvLyBJbml0aWFsaXplIGF1dGhlbnRpY2F0aW9uIHN0YXRlXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaW5pdGlhbGl6ZUF1dGggPSBhc3luYyAocmV0cnlDb3VudCA9IDApID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIHNldElzTG9hZGluZyh0cnVlKTtcblxuICAgICAgICAvLyBDaGVjayBpZiB1c2VyIGlzIGF1dGhlbnRpY2F0ZWQgZnJvbSB0b2tlblxuICAgICAgICBpZiAoYXV0aFNlcnZpY2UuaXNBdXRoZW50aWNhdGVkKCkpIHtcbiAgICAgICAgICBjb25zdCB0b2tlblVzZXIgPSBhdXRoU2VydmljZS5nZXRDdXJyZW50VXNlckZyb21Ub2tlbigpO1xuICAgICAgICAgIHNldFVzZXJGcm9tVG9rZW4odG9rZW5Vc2VyKTtcblxuICAgICAgICAgIC8vIFRlc3QgYmFja2VuZCBjb25uZWN0aXZpdHkgYmVmb3JlIHRyeWluZyB0byBmZXRjaCBwcm9maWxlXG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIC8vIFF1aWNrIGhlYWx0aCBjaGVjayB0byB2ZXJpZnkgYmFja2VuZCBpcyBhY2Nlc3NpYmxlXG4gICAgICAgICAgICBhd2FpdCBhdXRoU2VydmljZS50ZXN0Q29ubmVjdGlvbigpO1xuICAgICAgICAgIH0gY2F0Y2ggKGNvbm5lY3Rpb25FcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS53YXJuKFxuICAgICAgICAgICAgICBcIkJhY2tlbmQgbm90IGFjY2Vzc2libGUgZHVyaW5nIGF1dGggaW5pdCwgc2tpcHBpbmcgcHJvZmlsZSBmZXRjaFwiXG4gICAgICAgICAgICApO1xuICAgICAgICAgICAgLy8gU3RpbGwgc2V0IHRva2VuLWJhc2VkIGF1dGgsIHVzZXIgY2FuIHJldHJ5IGxhdGVyXG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gVHJ5IHRvIGZldGNoIGZ1bGwgdXNlciBwcm9maWxlXG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IHVzZXJQcm9maWxlID0gYXdhaXQgYXV0aFNlcnZpY2UuZ2V0Q3VycmVudFVzZXIoKTtcbiAgICAgICAgICAgIHNldFVzZXIodXNlclByb2ZpbGUpO1xuICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLndhcm4oXG4gICAgICAgICAgICAgIFwiRmFpbGVkIHRvIGZldGNoIHVzZXIgcHJvZmlsZSBkdXJpbmcgaW5pdGlhbGl6YXRpb246XCIsXG4gICAgICAgICAgICAgIGVycm9yXG4gICAgICAgICAgICApO1xuXG4gICAgICAgICAgICAvLyBIYW5kbGUgZGlmZmVyZW50IHR5cGVzIG9mIGVycm9yc1xuICAgICAgICAgICAgaWYgKGVycm9yICYmIHR5cGVvZiBlcnJvciA9PT0gXCJvYmplY3RcIikge1xuICAgICAgICAgICAgICAvLyBDaGVjayBmb3IgSFRUUCByZXNwb25zZSBlcnJvcnNcbiAgICAgICAgICAgICAgaWYgKFwicmVzcG9uc2VcIiBpbiBlcnJvcikge1xuICAgICAgICAgICAgICAgIGNvbnN0IHN0YXR1cyA9IChlcnJvciBhcyB7IHJlc3BvbnNlPzogeyBzdGF0dXM6IG51bWJlciB9IH0pXG4gICAgICAgICAgICAgICAgICAucmVzcG9uc2U/LnN0YXR1cztcbiAgICAgICAgICAgICAgICBpZiAoc3RhdHVzID09PSA0MDEgfHwgc3RhdHVzID09PSA0MDMpIHtcbiAgICAgICAgICAgICAgICAgIC8vIEludmFsaWQgdG9rZW4gLSBjbGVhciBhdXRoXG4gICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhcIlRva2VuIGlzIGludmFsaWQsIGNsZWFyaW5nIGF1dGhcIik7XG4gICAgICAgICAgICAgICAgICBhdXRoU2VydmljZS5jbGVhckF1dGgoKTtcbiAgICAgICAgICAgICAgICAgIHNldFVzZXIobnVsbCk7XG4gICAgICAgICAgICAgICAgICBzZXRVc2VyRnJvbVRva2VuKG51bGwpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAvLyBGb3Igb3RoZXIgSFRUUCBlcnJvcnMgKDQwNCwgNTAwLCBldGMuKSwga2VlcCB0b2tlbi1iYXNlZCBhdXRoXG4gICAgICAgICAgICAgIH0gZWxzZSBpZiAoXCJtZXNzYWdlXCIgaW4gZXJyb3IpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSAoZXJyb3IgYXMgeyBtZXNzYWdlOiBzdHJpbmcgfSkubWVzc2FnZTtcbiAgICAgICAgICAgICAgICBpZiAoXG4gICAgICAgICAgICAgICAgICBlcnJvck1lc3NhZ2UuaW5jbHVkZXMoXCJOZXR3b3JrIEVycm9yXCIpIHx8XG4gICAgICAgICAgICAgICAgICBlcnJvck1lc3NhZ2UuaW5jbHVkZXMoXCJFQ09OTlJFRlVTRURcIikgfHxcbiAgICAgICAgICAgICAgICAgIGVycm9yTWVzc2FnZS5pbmNsdWRlcyhcIkVOT1RGT1VORFwiKVxuICAgICAgICAgICAgICAgICkge1xuICAgICAgICAgICAgICAgICAgLy8gTmV0d29yayBjb25uZWN0aXZpdHkgaXNzdWVzIC0gZG9uJ3QgY2xlYXIgYXV0aCwgdXNlciBjYW4gcmV0cnkgbGF0ZXJcbiAgICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybihcbiAgICAgICAgICAgICAgICAgICAgXCJOZXR3b3JrIGVycm9yIGR1cmluZyBwcm9maWxlIGZldGNoLCBrZWVwaW5nIHRva2VuLWJhc2VkIGF1dGhcIlxuICAgICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgLy8gT3RoZXIgZXJyb3JzIC0gbG9nIGJ1dCBkb24ndCBjbGVhciBhdXRoXG4gICAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oXG4gICAgICAgICAgICAgICAgICAgIFwiVW5rbm93biBlcnJvciBkdXJpbmcgcHJvZmlsZSBmZXRjaDpcIixcbiAgICAgICAgICAgICAgICAgICAgZXJyb3JNZXNzYWdlXG4gICAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAvLyBDbGVhciBhbnkgc3RhbGUgZGF0YVxuICAgICAgICAgIGF1dGhTZXJ2aWNlLmNsZWFyQXV0aCgpO1xuICAgICAgICAgIHNldFVzZXIobnVsbCk7XG4gICAgICAgICAgc2V0VXNlckZyb21Ub2tlbihudWxsKTtcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkF1dGggaW5pdGlhbGl6YXRpb24gZXJyb3I6XCIsIGVycm9yKTtcblxuICAgICAgICAvLyBDaGVjayBpZiBpdCdzIGEgbmV0d29yayBlcnJvciBhbmQgd2Ugc2hvdWxkIHJldHJ5XG4gICAgICAgIGlmIChcbiAgICAgICAgICByZXRyeUNvdW50IDwgMiAmJlxuICAgICAgICAgIGVycm9yICYmXG4gICAgICAgICAgdHlwZW9mIGVycm9yID09PSBcIm9iamVjdFwiICYmXG4gICAgICAgICAgXCJtZXNzYWdlXCIgaW4gZXJyb3JcbiAgICAgICAgKSB7XG4gICAgICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gKGVycm9yIGFzIHsgbWVzc2FnZTogc3RyaW5nIH0pLm1lc3NhZ2U7XG4gICAgICAgICAgaWYgKFxuICAgICAgICAgICAgZXJyb3JNZXNzYWdlLmluY2x1ZGVzKFwiTmV0d29yayBFcnJvclwiKSB8fFxuICAgICAgICAgICAgZXJyb3JNZXNzYWdlLmluY2x1ZGVzKFwiRUNPTk5SRUZVU0VEXCIpIHx8XG4gICAgICAgICAgICBlcnJvck1lc3NhZ2UuaW5jbHVkZXMoXCJFTk9URk9VTkRcIilcbiAgICAgICAgICApIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFxuICAgICAgICAgICAgICBgTmV0d29yayBlcnJvciBkdXJpbmcgYXV0aCBpbml0LCByZXRyeWluZy4uLiAoJHtcbiAgICAgICAgICAgICAgICByZXRyeUNvdW50ICsgMVxuICAgICAgICAgICAgICB9LzMpYFxuICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gaW5pdGlhbGl6ZUF1dGgocmV0cnlDb3VudCArIDEpLCAyMDAwKTsgLy8gUmV0cnkgYWZ0ZXIgMiBzZWNvbmRzXG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgLy8gSWYgbm90IGEgbmV0d29yayBlcnJvciBvciBtYXggcmV0cmllcyByZWFjaGVkLCBjbGVhciBhdXRoXG4gICAgICAgIGF1dGhTZXJ2aWNlLmNsZWFyQXV0aCgpO1xuICAgICAgICBzZXRVc2VyKG51bGwpO1xuICAgICAgICBzZXRVc2VyRnJvbVRva2VuKG51bGwpO1xuICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICAgICAgc2V0SXNJbml0aWFsaXplZCh0cnVlKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgaW5pdGlhbGl6ZUF1dGgoKTtcbiAgfSwgW10pO1xuXG4gIC8vIFJlZnJlc2ggdXNlciBkYXRhXG4gIGNvbnN0IHJlZnJlc2hVc2VyID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBEZWJ1ZyBzdGF0ZW1lbnRzIHJlbW92ZWQgZm9yIHByb2R1Y3Rpb25cblxuICAgICAgLy8gRm9yY2UgY2hlY2sgYXV0aCBzdGF0ZSBmaXJzdFxuICAgICAgY29uc3QgY3VycmVudEF1dGhTdGF0ZSA9IGF1dGhTZXJ2aWNlLmlzQXV0aGVudGljYXRlZCgpO1xuICAgICAgc2V0SXNBdXRoZW50aWNhdGVkKGN1cnJlbnRBdXRoU3RhdGUpO1xuXG4gICAgICBpZiAoIWN1cnJlbnRBdXRoU3RhdGUpIHtcbiAgICAgICAgLy9cbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICAvLyBUcnkgdG8gZ2V0IHVzZXIgcHJvZmlsZSwgYnV0IGRvbid0IGZhaWwgaWYgaXQgZG9lc24ndCB3b3JrXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCB1c2VyUHJvZmlsZSA9IGF3YWl0IGF1dGhTZXJ2aWNlLmdldEN1cnJlbnRVc2VyKCk7XG4gICAgICAgIHNldFVzZXIodXNlclByb2ZpbGUpO1xuICAgICAgfSBjYXRjaCAocHJvZmlsZUVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUud2FybihcIkZhaWxlZCB0byByZWZyZXNoIHVzZXIgcHJvZmlsZTpcIiwgcHJvZmlsZUVycm9yKTtcbiAgICAgICAgLy8gS2VlcCBleGlzdGluZyB1c2VyIGRhdGEgaWYgcHJvZmlsZSBmZXRjaCBmYWlsc1xuICAgICAgfVxuXG4gICAgICAvLyBVcGRhdGUgdG9rZW4gdXNlciBhcyB3ZWxsXG4gICAgICBjb25zdCB0b2tlblVzZXIgPSBhdXRoU2VydmljZS5nZXRDdXJyZW50VXNlckZyb21Ub2tlbigpO1xuICAgICAgc2V0VXNlckZyb21Ub2tlbih0b2tlblVzZXIpO1xuXG4gICAgICAvL1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiRmFpbGVkIHRvIHJlZnJlc2ggdXNlcjpcIiwgZXJyb3IpO1xuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGRldGFpbHM6XCIsIHtcbiAgICAgICAgbWVzc2FnZTogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiBcIlVua25vd24gZXJyb3JcIixcbiAgICAgICAgc3RhdHVzOlxuICAgICAgICAgIGVycm9yICYmIHR5cGVvZiBlcnJvciA9PT0gXCJvYmplY3RcIiAmJiBcInJlc3BvbnNlXCIgaW4gZXJyb3JcbiAgICAgICAgICAgID8gKGVycm9yIGFzIHsgcmVzcG9uc2U/OiB7IHN0YXR1czogbnVtYmVyIH0gfSkucmVzcG9uc2U/LnN0YXR1c1xuICAgICAgICAgICAgOiBcIk5vIHN0YXR1c1wiLFxuICAgICAgfSk7XG4gICAgICAvLyBJZiByZWZyZXNoIGZhaWxzLCB1c2VyIG1pZ2h0IGJlIGxvZ2dlZCBvdXRcbiAgICAgIGxvZ291dCgpO1xuICAgIH1cbiAgfSwgW2xvZ291dF0pO1xuXG4gIC8vIExvZ2luIGZ1bmN0aW9uXG4gIGNvbnN0IGxvZ2luID0gYXN5bmMgKGNyZWRlbnRpYWxzOiBMb2dpblJlcXVlc3QpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuXG4gICAgICBhd2FpdCBhdXRoU2VydmljZS5sb2dpbihjcmVkZW50aWFscyk7XG5cbiAgICAgIC8vIEdldCB1c2VyIHByb2ZpbGUgLSBoYW5kbGUgZ3JhY2VmdWxseSBpZiBpdCBmYWlsc1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgdXNlclByb2ZpbGUgPSBhd2FpdCBhdXRoU2VydmljZS5nZXRDdXJyZW50VXNlcigpO1xuICAgICAgICBzZXRVc2VyKHVzZXJQcm9maWxlKTtcbiAgICAgIH0gY2F0Y2ggKHByb2ZpbGVFcnJvcikge1xuICAgICAgICBjb25zb2xlLndhcm4oXCJGYWlsZWQgdG8gZ2V0IHVzZXIgcHJvZmlsZSBhZnRlciBsb2dpbjpcIiwgcHJvZmlsZUVycm9yKTtcbiAgICAgICAgLy8gRG9uJ3QgZmFpbCB0aGUgbG9naW4gaWYgcHJvZmlsZSBmZXRjaCBmYWlsc1xuICAgICAgICAvLyBUaGUgdXNlciBjYW4gc3RpbGwgYWNjZXNzIHRoZSBhcHAgd2l0aCB0b2tlbi1iYXNlZCBhdXRoXG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHRva2VuVXNlciA9IGF1dGhTZXJ2aWNlLmdldEN1cnJlbnRVc2VyRnJvbVRva2VuKCk7XG4gICAgICBzZXRVc2VyRnJvbVRva2VuKHRva2VuVXNlcik7XG5cbiAgICAgIHRvYXN0LnN1Y2Nlc3MoXCJMb2dpbiBzdWNjZXNzZnVsIVwiKTtcblxuICAgICAgLy8gUmVkaXJlY3QgdG8gZGFzaGJvYXJkIC0gcm9sZS1iYXNlZCByZW5kZXJpbmcgd2lsbCBiZSBoYW5kbGVkIGJ5IHRoZSBkYXNoYm9hcmQgcGFnZVxuICAgICAgcm91dGVyLnB1c2goXCIvZGFzaGJvYXJkXCIpO1xuICAgIH0gY2F0Y2ggKGVycm9yOiB1bmtub3duKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiTG9naW4gZXJyb3I6XCIsIGVycm9yKTtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9XG4gICAgICAgIGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogXCJMb2dpbiBmYWlsZWRcIjtcbiAgICAgIHRvYXN0LmVycm9yKGVycm9yTWVzc2FnZSk7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gUmVnaXN0ZXIgZnVuY3Rpb25cbiAgY29uc3QgcmVnaXN0ZXIgPSBhc3luYyAodXNlckRhdGE6IFJlZ2lzdGVyUmVxdWVzdCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXV0aFNlcnZpY2UucmVnaXN0ZXIodXNlckRhdGEpO1xuXG4gICAgICB0b2FzdC5zdWNjZXNzKFxuICAgICAgICByZXNwb25zZS5tZXNzYWdlIHx8XG4gICAgICAgICAgXCJSZWdpc3RyYXRpb24gc3VjY2Vzc2Z1bCEgUGxlYXNlIGNoZWNrIHlvdXIgZW1haWwgdG8gdmVyaWZ5IHlvdXIgYWNjb3VudC5cIlxuICAgICAgKTtcblxuICAgICAgLy8gUmVkaXJlY3QgdG8gbG9naW4gcGFnZVxuICAgICAgcm91dGVyLnB1c2goXCIvbG9naW5cIik7XG4gICAgfSBjYXRjaCAoZXJyb3I6IHVua25vd24pIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJSZWdpc3RyYXRpb24gZXJyb3I6XCIsIGVycm9yKTtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9XG4gICAgICAgIGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogXCJSZWdpc3RyYXRpb24gZmFpbGVkXCI7XG4gICAgICB0b2FzdC5lcnJvcihlcnJvck1lc3NhZ2UpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIENsZWFyIGF1dGhlbnRpY2F0aW9uIGRhdGFcbiAgY29uc3QgY2xlYXJBdXRoID0gKCkgPT4ge1xuICAgIHNldFVzZXIobnVsbCk7XG4gICAgc2V0VXNlckZyb21Ub2tlbihudWxsKTtcbiAgICBhdXRoU2VydmljZS5jbGVhckF1dGgoKTtcbiAgfTtcblxuICAvLyBDaGVjayBpZiB1c2VyIGhhcyBPQXV0aCBwcm92aWRlciBsaW5rZWRcbiAgY29uc3QgaGFzT0F1dGhQcm92aWRlciA9IChwcm92aWRlcjogc3RyaW5nKTogYm9vbGVhbiA9PiB7XG4gICAgcmV0dXJuIGF1dGhTZXJ2aWNlLmhhc09BdXRoUHJvdmlkZXIocHJvdmlkZXIpO1xuICB9O1xuXG4gIC8vIEdldCBPQXV0aCBwcm92aWRlcnMgbGlzdFxuICBjb25zdCBnZXRPQXV0aFByb3ZpZGVycyA9IGFzeW5jICgpOiBQcm9taXNlPFJlY29yZDxzdHJpbmcsIHN0cmluZz4+ID0+IHtcbiAgICB0cnkge1xuICAgICAgcmV0dXJuIGF3YWl0IGF1dGhTZXJ2aWNlLmdldE9BdXRoUHJvdmlkZXJzKCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJGYWlsZWQgdG8gZ2V0IE9BdXRoIHByb3ZpZGVyczpcIiwgZXJyb3IpO1xuICAgICAgcmV0dXJuIHt9O1xuICAgIH1cbiAgfTtcblxuICAvLyBVcGRhdGUgYXV0aGVudGljYXRpb24gc3RhdGUgd2hlbiB0b2tlbnMgY2hhbmdlXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgdXBkYXRlQXV0aFN0YXRlID0gKCkgPT4ge1xuICAgICAgY29uc3QgYXV0aFN0YXRlID0gYXV0aFNlcnZpY2UuaXNBdXRoZW50aWNhdGVkKCk7XG4gICAgICAvL1xuICAgICAgc2V0SXNBdXRoZW50aWNhdGVkKGF1dGhTdGF0ZSk7XG4gICAgfTtcblxuICAgIC8vIEluaXRpYWwgY2hlY2tcbiAgICB1cGRhdGVBdXRoU3RhdGUoKTtcblxuICAgIC8vIExpc3RlbiBmb3Igc3RvcmFnZSBjaGFuZ2VzICh3aGVuIHRva2VucyBhcmUgYWRkZWQvcmVtb3ZlZClcbiAgICBjb25zdCBoYW5kbGVTdG9yYWdlQ2hhbmdlID0gKGU6IFN0b3JhZ2VFdmVudCkgPT4ge1xuICAgICAgaWYgKGUua2V5ID09PSBcImFjY2Vzc190b2tlblwiIHx8IGUua2V5ID09PSBcInJlZnJlc2hfdG9rZW5cIikge1xuICAgICAgICAvL1xuICAgICAgICB1cGRhdGVBdXRoU3RhdGUoKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoXCJzdG9yYWdlXCIsIGhhbmRsZVN0b3JhZ2VDaGFuZ2UpO1xuXG4gICAgLy8gQWxzbyBjaGVjayBwZXJpb2RpY2FsbHkgZm9yIHRva2VuIGV4cGlyYXRpb25cbiAgICBjb25zdCBpbnRlcnZhbCA9IHNldEludGVydmFsKHVwZGF0ZUF1dGhTdGF0ZSwgNTAwMCk7XG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJzdG9yYWdlXCIsIGhhbmRsZVN0b3JhZ2VDaGFuZ2UpO1xuICAgICAgY2xlYXJJbnRlcnZhbChpbnRlcnZhbCk7XG4gICAgfTtcbiAgfSwgW10pO1xuXG4gIC8vIENvbnRleHQgdmFsdWVcbiAgY29uc3QgdmFsdWU6IEF1dGhDb250ZXh0VHlwZSA9IHtcbiAgICB1c2VyLFxuICAgIHVzZXJGcm9tVG9rZW4sXG4gICAgaXNBdXRoZW50aWNhdGVkLFxuICAgIGlzTG9hZGluZyxcbiAgICBpc0luaXRpYWxpemVkLFxuICAgIGxvZ2luLFxuICAgIHJlZ2lzdGVyLFxuICAgIGxvZ291dCxcbiAgICByZWZyZXNoVXNlcixcbiAgICBjbGVhckF1dGgsXG4gICAgaGFzT0F1dGhQcm92aWRlcixcbiAgICBnZXRPQXV0aFByb3ZpZGVycyxcbiAgfTtcblxuICByZXR1cm4gPEF1dGhDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+e2NoaWxkcmVufTwvQXV0aENvbnRleHQuUHJvdmlkZXI+O1xufVxuXG4vLyBDdXN0b20gaG9vayB0byB1c2UgYXV0aCBjb250ZXh0XG5leHBvcnQgZnVuY3Rpb24gdXNlQXV0aCgpIHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoQXV0aENvbnRleHQpO1xuXG4gIGlmIChjb250ZXh0ID09PSB1bmRlZmluZWQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJ1c2VBdXRoIG11c3QgYmUgdXNlZCB3aXRoaW4gYW4gQXV0aFByb3ZpZGVyXCIpO1xuICB9XG5cbiAgcmV0dXJuIGNvbnRleHQ7XG59XG5cbi8vIEV4cG9ydCB0aGUgY29udGV4dCBmb3IgYWR2YW5jZWQgdXNhZ2VcbmV4cG9ydCB7IEF1dGhDb250ZXh0IH07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJ1c2VSb3V0ZXIiLCJ0b2FzdCIsImF1dGhTZXJ2aWNlIiwiQXV0aENvbnRleHQiLCJ1bmRlZmluZWQiLCJJRExFX0xPR09VVF9NSU5VVEVTIiwicGFyc2VJbnQiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfSURMRV9MT0dPVVRfTUlOVVRFUyIsIklETEVfTE9HT1VUX01TIiwiQXV0aFByb3ZpZGVyIiwiY2hpbGRyZW4iLCJyb3V0ZXIiLCJ1c2VyIiwic2V0VXNlciIsInVzZXJGcm9tVG9rZW4iLCJzZXRVc2VyRnJvbVRva2VuIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiaXNJbml0aWFsaXplZCIsInNldElzSW5pdGlhbGl6ZWQiLCJpc0F1dGhlbnRpY2F0ZWQiLCJzZXRJc0F1dGhlbnRpY2F0ZWQiLCJyZWZyZXNoVGltZW91dFJlZiIsInVzZVJlZiIsImlkbGVUaW1lb3V0UmVmIiwiY2xlYXJUaW1lcnMiLCJjdXJyZW50IiwiY2xlYXJUaW1lb3V0IiwibG9nb3V0Iiwic2Vzc2lvbklkIiwibG9nb3V0QWxsIiwiY2xlYXJBdXRoIiwic2hvdWxkQ2FsbEFwaUxvZ291dCIsImlzVG9rZW5FeHBpcmVkIiwiY2F0Y2giLCJjb25zb2xlIiwid2FybiIsInJlcGxhY2UiLCJzY2hlZHVsZVRva2VuUmVmcmVzaCIsImV4cERhdGUiLCJnZXRUb2tlbkV4cGlyYXRpb24iLCJub3ciLCJEYXRlIiwibXNVbnRpbFJlZnJlc2giLCJNYXRoIiwibWF4IiwiZ2V0VGltZSIsInNldFRpbWVvdXQiLCJyZWZyZXNoVG9rZW4iLCJlcnIiLCJlcnJvciIsInJlc2V0SWRsZVRpbWVyIiwiZXZlbnRzIiwiZm9yRWFjaCIsImUiLCJ3aW5kb3ciLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImluaXRpYWxpemVBdXRoIiwicmV0cnlDb3VudCIsInRva2VuVXNlciIsImdldEN1cnJlbnRVc2VyRnJvbVRva2VuIiwidGVzdENvbm5lY3Rpb24iLCJjb25uZWN0aW9uRXJyb3IiLCJ1c2VyUHJvZmlsZSIsImdldEN1cnJlbnRVc2VyIiwic3RhdHVzIiwicmVzcG9uc2UiLCJsb2ciLCJlcnJvck1lc3NhZ2UiLCJtZXNzYWdlIiwiaW5jbHVkZXMiLCJyZWZyZXNoVXNlciIsImN1cnJlbnRBdXRoU3RhdGUiLCJwcm9maWxlRXJyb3IiLCJFcnJvciIsImxvZ2luIiwiY3JlZGVudGlhbHMiLCJzdWNjZXNzIiwicHVzaCIsInJlZ2lzdGVyIiwidXNlckRhdGEiLCJoYXNPQXV0aFByb3ZpZGVyIiwicHJvdmlkZXIiLCJnZXRPQXV0aFByb3ZpZGVycyIsInVwZGF0ZUF1dGhTdGF0ZSIsImF1dGhTdGF0ZSIsImhhbmRsZVN0b3JhZ2VDaGFuZ2UiLCJrZXkiLCJpbnRlcnZhbCIsInNldEludGVydmFsIiwiY2xlYXJJbnRlcnZhbCIsInZhbHVlIiwiUHJvdmlkZXIiLCJ1c2VBdXRoIiwiY29udGV4dCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/auth-context.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/use-client-side.ts":
/*!**************************************!*\
  !*** ./src/hooks/use-client-side.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useClientSide: () => (/* binding */ useClientSide),\n/* harmony export */   useCurrentOrigin: () => (/* binding */ useCurrentOrigin),\n/* harmony export */   useCurrentUrl: () => (/* binding */ useCurrentUrl),\n/* harmony export */   useLocalStorage: () => (/* binding */ useLocalStorage),\n/* harmony export */   useWindow: () => (/* binding */ useWindow)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useClientSide,useWindow,useLocalStorage,useCurrentUrl,useCurrentOrigin auto */ \n/**\n * Hook to handle client-side only values and prevent hydration mismatches.\n * \n * This hook returns false during SSR and true after hydration on the client.\n * Use this to conditionally render content that depends on browser APIs\n * like window, localStorage, etc.\n * \n * @returns boolean - true if running on client, false during SSR\n * \n * @example\n * ```tsx\n * function MyComponent() {\n *   const isClient = useClientSide();\n *   \n *   return (\n *     <div>\n *       <p>Server and client see this</p>\n *       {isClient && (\n *         <p>Only client sees this: {window.location.href}</p>\n *       )}\n *       <p>URL: {isClient ? window.location.href : 'Loading...'}</p>\n *     </div>\n *   );\n * }\n * ```\n */ function useClientSide() {\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useClientSide.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"useClientSide.useEffect\"], []);\n    return isClient;\n}\n/**\n * Hook to safely access window object and prevent hydration mismatches.\n * \n * @returns Window object if on client, null during SSR\n */ function useWindow() {\n    const isClient = useClientSide();\n    return isClient ? window : null;\n}\n/**\n * Hook to safely access localStorage and prevent hydration mismatches.\n * \n * @returns localStorage object if on client, null during SSR\n */ function useLocalStorage() {\n    const isClient = useClientSide();\n    return isClient ? localStorage : null;\n}\n/**\n * Hook to get current URL safely without hydration mismatches.\n * \n * @param fallback - Fallback URL to show during SSR\n * @returns Current URL or fallback during SSR\n */ function useCurrentUrl(fallback = 'Loading...') {\n    const isClient = useClientSide();\n    return isClient ? window.location.href : fallback;\n}\n/**\n * Hook to get current origin safely without hydration mismatches.\n * \n * @param fallback - Fallback origin to show during SSR\n * @returns Current origin or fallback during SSR\n */ function useCurrentOrigin(fallback = 'Loading...') {\n    const isClient = useClientSide();\n    return isClient ? window.location.origin : fallback;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/use-client-side.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api-client.ts":
/*!*******************************!*\
  !*** ./src/lib/api-client.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   apiClientInstance: () => (/* binding */ apiClientInstance),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// API Configuration\nconst API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\nconst API_VERSION = '/api/v1';\n// Cache configuration\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds\nconst CACHE_PREFIX = 'api_cache_';\n// Cache utility functions\nclass CacheManager {\n    static getInstance() {\n        if (!CacheManager.instance) {\n            CacheManager.instance = new CacheManager();\n        }\n        return CacheManager.instance;\n    }\n    set(key, data, duration = CACHE_DURATION) {\n        const expiresAt = Date.now() + duration;\n        this.cache.set(key, {\n            data,\n            timestamp: Date.now(),\n            expiresAt\n        });\n        // Also store in localStorage for persistence across page reloads\n        if (false) {}\n    }\n    get(key) {\n        // Check memory cache first\n        const memoryEntry = this.cache.get(key);\n        if (memoryEntry && memoryEntry.expiresAt > Date.now()) {\n            return memoryEntry.data;\n        }\n        // Check localStorage\n        if (false) {}\n        return null;\n    }\n    delete(key) {\n        this.cache.delete(key);\n        if (false) {}\n    }\n    clear(pattern) {\n        if (pattern) {\n            // Clear specific pattern\n            for (const key of this.cache.keys()){\n                if (key.includes(pattern)) {\n                    this.cache.delete(key);\n                }\n            }\n            if (false) {}\n        } else {\n            // Clear all cache\n            this.cache.clear();\n            if (false) {}\n        }\n    }\n    generateKey(config) {\n        const { method, url, params, data } = config;\n        return `${method?.toUpperCase()}_${url}_${JSON.stringify(params)}_${JSON.stringify(data)}`;\n    }\n    constructor(){\n        this.cache = new Map();\n    }\n}\n// Create axios instance\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: `${API_BASE_URL}${API_VERSION}`,\n    timeout: 30000,\n    withCredentials: false,\n    headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n    }\n});\n// Request interceptor to add auth token and debug logging\napiClient.interceptors.request.use((config)=>{\n    // Debug logging\n    if (true) {\n        console.log('API Request:', {\n            method: config.method?.toUpperCase(),\n            url: config.url,\n            baseURL: config.baseURL,\n            fullURL: `${config.baseURL}${config.url}`,\n            headers: config.headers,\n            data: config.data\n        });\n    }\n    // Get token from localStorage or cookies\n    const token =  false ? 0 : null;\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    console.error('API Request Error:', error);\n    return Promise.reject(error);\n});\n// Response interceptor for token refresh and error handling\napiClient.interceptors.response.use((response)=>{\n    // Debug logging\n    if (true) {\n        console.log('API Response:', {\n            status: response.status,\n            statusText: response.statusText,\n            url: response.config.url,\n            data: response.data\n        });\n    }\n    return response;\n}, async (error)=>{\n    const originalRequest = error.config;\n    // Handle 401 Unauthorized - try to refresh token\n    if (error.response?.status === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        try {\n            const refreshToken =  false ? 0 : null;\n            if (refreshToken) {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${API_BASE_URL}${API_VERSION}/auth/refresh`, {\n                    refresh_token: refreshToken\n                });\n                const { access_token, refresh_token } = response.data;\n                // Store new tokens\n                if (false) {}\n                // Retry original request with new token\n                originalRequest.headers.Authorization = `Bearer ${access_token}`;\n                return apiClient(originalRequest);\n            }\n        } catch  {\n            // Refresh failed - clear tokens and redirect to login\n            if (false) {}\n        }\n    }\n    return Promise.reject(error);\n});\n// API Client class with typed methods and caching\nclass ApiClient {\n    constructor(){\n        this.client = apiClient;\n        this.cache = CacheManager.getInstance();\n    }\n    // Generic request method with caching\n    async request(config, useCache = false, cacheDuration) {\n        try {\n            // Check cache for GET requests\n            if (useCache && config.method?.toLowerCase() === 'get') {\n                const cacheKey = this.cache.generateKey(config);\n                const cachedData = this.cache.get(cacheKey);\n                if (cachedData) {\n                    return cachedData;\n                }\n            }\n            const response = await this.client.request(config);\n            // Cache successful GET responses\n            if (useCache && config.method?.toLowerCase() === 'get' && response.status === 200) {\n                const cacheKey = this.cache.generateKey(config);\n                this.cache.set(cacheKey, response.data, cacheDuration);\n            }\n            return response.data;\n        } catch (error) {\n            throw this.handleError(error);\n        }\n    }\n    // Cached GET request\n    async get(url, params, useCache = true, cacheDuration) {\n        return this.request({\n            method: 'GET',\n            url,\n            params\n        }, useCache, cacheDuration);\n    }\n    // POST request (no caching)\n    async post(url, data) {\n        return this.request({\n            method: 'POST',\n            url,\n            data\n        }, false);\n    }\n    // PUT request (no caching)\n    async put(url, data) {\n        return this.request({\n            method: 'PUT',\n            url,\n            data\n        }, false);\n    }\n    // DELETE request (no caching)\n    async delete(url) {\n        return this.request({\n            method: 'DELETE',\n            url\n        }, false);\n    }\n    // Clear cache\n    clearCache(pattern) {\n        this.cache.clear(pattern);\n    }\n    // Test connection to backend\n    async testConnection() {\n        try {\n            console.log('Testing connection to:', `${this.client.defaults.baseURL}/health`);\n            const response = await this.client.get('/health', {\n                timeout: 5000,\n                validateStatus: ()=>true // Accept any status code\n            });\n            if (response.status === 200) {\n                return {\n                    success: true,\n                    message: 'Backend connection successful',\n                    details: response.data\n                };\n            } else {\n                return {\n                    success: false,\n                    message: `Backend responded with status ${response.status}`,\n                    details: response.data\n                };\n            }\n        } catch (error) {\n            console.error('Connection test failed:', error);\n            if (error.code === 'ECONNREFUSED') {\n                return {\n                    success: false,\n                    message: 'Backend server is not running. Please start the backend server on http://localhost:8000',\n                    details: {\n                        error: error.code,\n                        message: error.message\n                    }\n                };\n            } else if (error.code === 'ENOTFOUND') {\n                return {\n                    success: false,\n                    message: 'Backend server not found. Please check the API URL configuration.',\n                    details: {\n                        error: error.code,\n                        message: error.message\n                    }\n                };\n            } else {\n                return {\n                    success: false,\n                    message: `Connection failed: ${error.message}`,\n                    details: {\n                        error: error.code,\n                        message: error.message\n                    }\n                };\n            }\n        }\n    }\n    // Handle API errors with enhanced diagnostics\n    handleError(error) {\n        console.error('API Client Error:', error);\n        if (error && typeof error === 'object' && 'response' in error) {\n            // Server responded with error status\n            const { status, data } = error.response;\n            // Prefer server-provided detail when available so the UI can show precise feedback\n            const serverMessage = data?.detail;\n            switch(status){\n                case 400:\n                    return new Error(serverMessage || 'Bad request');\n                case 401:\n                    // Pass through messages such as \"Account is locked\" when supplied\n                    return new Error(serverMessage || 'Unauthorized - please login again');\n                case 403:\n                    return new Error(serverMessage || 'Access denied - insufficient permissions');\n                case 404:\n                    return new Error(serverMessage || 'Resource not found');\n                case 422:\n                    return new Error(serverMessage || 'Validation error');\n                case 429:\n                    return new Error(serverMessage || 'Too many requests - please try again later');\n                case 500:\n                    return new Error(serverMessage || 'Internal server error');\n                default:\n                    return new Error(serverMessage || 'An error occurred');\n            }\n        } else if (error && typeof error === 'object' && 'request' in error) {\n            // Network error - provide more detailed diagnostics\n            const axiosError = error;\n            console.error('Network Error Details:', {\n                code: axiosError.code,\n                message: axiosError.message,\n                config: {\n                    url: axiosError.config?.url,\n                    method: axiosError.config?.method,\n                    baseURL: axiosError.config?.baseURL,\n                    timeout: axiosError.config?.timeout\n                }\n            });\n            // Provide specific error messages based on error code\n            if (axiosError.code === 'ECONNREFUSED') {\n                return new Error('Connection refused - Backend server may not be running. Please check if the server is started on http://localhost:8000');\n            } else if (axiosError.code === 'ENOTFOUND') {\n                return new Error('Server not found - Please check the API URL configuration');\n            } else if (axiosError.code === 'ETIMEDOUT') {\n                return new Error('Request timeout - Server is taking too long to respond');\n            } else if (axiosError.code === 'ECONNABORTED') {\n                return new Error('Request aborted - Connection was terminated');\n            } else {\n                return new Error(`Network error (${axiosError.code || 'UNKNOWN'}) - Please check your connection and ensure the backend server is running`);\n            }\n        } else {\n            // Other error\n            const message = error && typeof error === 'object' && 'message' in error ? String(error.message) : 'An unexpected error occurred';\n            return new Error(message);\n        }\n    }\n    // Token management\n    setTokens(accessToken, refreshToken) {\n        if (false) {}\n    }\n    clearTokens() {\n        if (false) {}\n    }\n    getAccessToken() {\n        if (false) {}\n        return null;\n    }\n    getRefreshToken() {\n        if (false) {}\n        return null;\n    }\n}\n// Export singleton instance\nconst apiClientInstance = new ApiClient();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClientInstance);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api-client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth-service.ts":
/*!*********************************!*\
  !*** ./src/lib/auth-service.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: () => (/* binding */ AuthService),\n/* harmony export */   authService: () => (/* binding */ authService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _api_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-client */ \"(ssr)/./src/lib/api-client.ts\");\n/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jwt-decode */ \"(ssr)/./node_modules/jwt-decode/build/esm/index.js\");\n\n\n// Authentication Service Class\nclass AuthService {\n    // Login user\n    async login(credentials) {\n        const response = await this.apiClient.request({\n            method: 'POST',\n            url: '/auth/login',\n            data: credentials\n        });\n        // Store tokens\n        this.apiClient.setTokens(response.access_token, response.refresh_token);\n        return response;\n    }\n    // Register user\n    async register(userData) {\n        const response = await this.apiClient.request({\n            method: 'POST',\n            url: '/auth/register',\n            data: userData\n        });\n        return response;\n    }\n    // Logout user\n    async logout(sessionId, logoutAll = false) {\n        const response = await this.apiClient.request({\n            method: 'POST',\n            url: '/auth/logout',\n            data: {\n                session_id: sessionId,\n                logout_all: logoutAll\n            }\n        });\n        // Clear tokens\n        this.apiClient.clearTokens();\n        return response;\n    }\n    // Refresh access token\n    async refreshToken() {\n        const refreshToken = this.apiClient.getRefreshToken();\n        if (!refreshToken) {\n            throw new Error('No refresh token available');\n        }\n        const response = await this.apiClient.request({\n            method: 'POST',\n            url: '/auth/refresh',\n            data: {\n                refresh_token: refreshToken\n            }\n        });\n        // Update stored tokens\n        this.apiClient.setTokens(response.access_token, response.refresh_token);\n        return response;\n    }\n    // Get current user profile\n    async getCurrentUser() {\n        const response = await this.apiClient.request({\n            method: 'GET',\n            url: '/auth/me'\n        });\n        return response;\n    }\n    // Get user sessions\n    async getUserSessions() {\n        const response = await this.apiClient.request({\n            method: 'GET',\n            url: '/auth/sessions'\n        });\n        return response;\n    }\n    // Revoke specific session\n    async revokeSession(sessionId) {\n        const response = await this.apiClient.request({\n            method: 'DELETE',\n            url: `/auth/sessions/${sessionId}`\n        });\n        return response;\n    }\n    // Request password reset\n    async requestPasswordReset(email) {\n        const response = await this.apiClient.request({\n            method: 'POST',\n            url: '/auth/forgot-password',\n            data: {\n                email\n            }\n        });\n        return response;\n    }\n    // Complete password reset\n    async completePasswordReset(token, newPassword) {\n        const response = await this.apiClient.request({\n            method: 'POST',\n            url: '/auth/reset-password',\n            data: {\n                token,\n                new_password: newPassword\n            }\n        });\n        return response;\n    }\n    // Check if user is authenticated\n    isAuthenticated() {\n        const token = this.apiClient.getAccessToken();\n        if (!token) return false;\n        try {\n            const decoded = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_1__.jwtDecode)(token);\n            const currentTime = Math.floor(Date.now() / 1000);\n            return decoded.exp > currentTime;\n        } catch  {\n            return false;\n        }\n    }\n    // Get current user from token\n    getCurrentUserFromToken() {\n        const token = this.apiClient.getAccessToken();\n        if (!token) return null;\n        try {\n            return (0,jwt_decode__WEBPACK_IMPORTED_MODULE_1__.jwtDecode)(token);\n        } catch  {\n            return null;\n        }\n    }\n    // Check if token is expired\n    isTokenExpired() {\n        const token = this.apiClient.getAccessToken();\n        if (!token) return true;\n        try {\n            const decoded = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_1__.jwtDecode)(token);\n            const currentTime = Math.floor(Date.now() / 1000);\n            return decoded.exp <= currentTime;\n        } catch  {\n            return true;\n        }\n    }\n    // Get token expiration time\n    getTokenExpiration() {\n        const token = this.apiClient.getAccessToken();\n        if (!token) return null;\n        try {\n            const decoded = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_1__.jwtDecode)(token);\n            return new Date(decoded.exp * 1000);\n        } catch  {\n            return null;\n        }\n    }\n    // Clear all authentication data\n    clearAuth() {\n        this.apiClient.clearTokens();\n    }\n    // Verify email\n    async verifyEmail(token) {\n        const response = await this.apiClient.request({\n            method: 'POST',\n            url: '/auth/verify-email',\n            data: {\n                token\n            }\n        });\n        return response;\n    }\n    // Resend verification email\n    async resendVerification(email) {\n        const response = await this.apiClient.request({\n            method: 'POST',\n            url: '/auth/resend-verification',\n            data: {\n                email\n            }\n        });\n        return response;\n    }\n    // OAuth-related methods\n    async handleOAuthCallback(provider, code, state) {\n        const response = await this.apiClient.request({\n            method: 'GET',\n            url: `/oauth/${provider}/callback`,\n            params: {\n                code,\n                state\n            }\n        });\n        return response;\n    }\n    // Get OAuth providers list\n    async getOAuthProviders() {\n        const response = await this.apiClient.request({\n            method: 'GET',\n            url: '/oauth/providers'\n        });\n        return response;\n    }\n    // Check if user has OAuth provider linked\n    hasOAuthProvider(provider) {\n        const user = this.getCurrentUserFromToken();\n        return user?.oauth_provider === provider;\n    }\n    // Test backend connection\n    async testConnection() {\n        return await this.apiClient.testConnection();\n    }\n    constructor(){\n        this.apiClient = _api_client__WEBPACK_IMPORTED_MODULE_0__.apiClientInstance;\n    }\n}\n// Export singleton instance\nconst authService = new AuthService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (authService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth-service.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiRDpcXG5leHRqc1xcd2ViYXBwXFxmcm9udGVuZFxcc3JjXFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/tailwind-merge","vendor-chunks/@radix-ui","vendor-chunks/sonner","vendor-chunks/follow-redirects","vendor-chunks/lucide-react","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/class-variance-authority","vendor-chunks/next-themes","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/jwt-decode","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/@swc","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/clsx","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ftest-connection%2Fpage&page=%2Ftest-connection%2Fpage&appPaths=%2Ftest-connection%2Fpage&pagePath=private-next-app-dir%2Ftest-connection%2Fpage.tsx&appDir=D%3A%5Cnextjs%5Cwebapp%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cnextjs%5Cwebapp%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();