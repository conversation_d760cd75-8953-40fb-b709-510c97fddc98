#!/bin/bash

# Aixiate Webapp Service Startup Script
# This script starts both backend and frontend services with proper error handling

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKEND_DIR="$PROJECT_ROOT/backend"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
BACKEND_PORT=8000
FRONTEND_PORT=3000

# Check if a port is in use
check_port() {
    local port=$1
    local service=$2
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "$service is already running on port $port"
        return 0
    else
        return 1
    fi
}

# Wait for service to be ready
wait_for_service() {
    local url=$1
    local service=$2
    local max_attempts=30
    local attempt=1
    
    log_info "Waiting for $service to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$url" >/dev/null 2>&1; then
            log_success "$service is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_error "$service failed to start within $((max_attempts * 2)) seconds"
    return 1
}

# Start backend service
start_backend() {
    log_info "Starting backend service..."
    
    # Check if backend is already running
    if check_port $BACKEND_PORT "Backend"; then
        return 0
    fi
    
    # Check if backend directory exists
    if [ ! -d "$BACKEND_DIR" ]; then
        log_error "Backend directory not found: $BACKEND_DIR"
        return 1
    fi
    
    cd "$BACKEND_DIR"
    
    # Check if virtual environment exists
    if [ ! -d ".venv" ]; then
        log_info "Creating Python virtual environment..."
        python -m venv .venv
    fi
    
    # Activate virtual environment
    source .venv/bin/activate || source .venv/Scripts/activate
    
    # Install/update dependencies
    if command -v uv >/dev/null 2>&1; then
        log_info "Installing dependencies with uv..."
        uv sync
    else
        log_info "Installing uv and dependencies..."
        pip install uv
        uv sync
    fi
    
    # Check if .env file exists
    if [ ! -f ".env" ]; then
        log_warning "Backend .env file not found. Creating from example..."
        if [ -f ".env.example" ]; then
            cp .env.example .env
        else
            log_error "No .env.example file found. Please create .env manually."
            return 1
        fi
    fi
    
    # Run database migrations
    log_info "Running database migrations..."
    alembic upgrade head
    
    # Start backend server
    log_info "Starting FastAPI server..."
    uvicorn app.main:app --host 0.0.0.0 --port $BACKEND_PORT --reload &
    BACKEND_PID=$!
    
    # Wait for backend to be ready
    if wait_for_service "http://localhost:$BACKEND_PORT/health" "Backend"; then
        log_success "Backend started successfully (PID: $BACKEND_PID)"
        echo $BACKEND_PID > "$PROJECT_ROOT/.backend.pid"
        return 0
    else
        log_error "Backend failed to start"
        kill $BACKEND_PID 2>/dev/null || true
        return 1
    fi
}

# Start frontend service
start_frontend() {
    log_info "Starting frontend service..."
    
    # Check if frontend is already running
    if check_port $FRONTEND_PORT "Frontend"; then
        return 0
    fi
    
    # Check if frontend directory exists
    if [ ! -d "$FRONTEND_DIR" ]; then
        log_error "Frontend directory not found: $FRONTEND_DIR"
        return 1
    fi
    
    cd "$FRONTEND_DIR"
    
    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        log_info "Installing Node.js dependencies..."
        npm install
    fi
    
    # Check if .env.local file exists
    if [ ! -f ".env.local" ]; then
        log_warning "Frontend .env.local file not found. Creating from example..."
        if [ -f ".env.example" ]; then
            cp .env.example .env.local
        else
            log_error "No .env.example file found. Please create .env.local manually."
            return 1
        fi
    fi
    
    # Start frontend server
    log_info "Starting Next.js development server..."
    npm run dev &
    FRONTEND_PID=$!
    
    # Wait for frontend to be ready
    if wait_for_service "http://localhost:$FRONTEND_PORT" "Frontend"; then
        log_success "Frontend started successfully (PID: $FRONTEND_PID)"
        echo $FRONTEND_PID > "$PROJECT_ROOT/.frontend.pid"
        return 0
    else
        log_error "Frontend failed to start"
        kill $FRONTEND_PID 2>/dev/null || true
        return 1
    fi
}

# Stop services
stop_services() {
    log_info "Stopping services..."
    
    # Stop backend
    if [ -f "$PROJECT_ROOT/.backend.pid" ]; then
        BACKEND_PID=$(cat "$PROJECT_ROOT/.backend.pid")
        if kill -0 $BACKEND_PID 2>/dev/null; then
            log_info "Stopping backend (PID: $BACKEND_PID)..."
            kill $BACKEND_PID
            rm "$PROJECT_ROOT/.backend.pid"
        fi
    fi
    
    # Stop frontend
    if [ -f "$PROJECT_ROOT/.frontend.pid" ]; then
        FRONTEND_PID=$(cat "$PROJECT_ROOT/.frontend.pid")
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            log_info "Stopping frontend (PID: $FRONTEND_PID)..."
            kill $FRONTEND_PID
            rm "$PROJECT_ROOT/.frontend.pid"
        fi
    fi
    
    # Kill any remaining processes on the ports
    lsof -ti:$BACKEND_PORT | xargs kill -9 2>/dev/null || true
    lsof -ti:$FRONTEND_PORT | xargs kill -9 2>/dev/null || true
    
    log_success "Services stopped"
}

# Check system requirements
check_requirements() {
    log_info "Checking system requirements..."
    
    # Check Python
    if ! command -v python >/dev/null 2>&1 && ! command -v python3 >/dev/null 2>&1; then
        log_error "Python is not installed"
        return 1
    fi
    
    # Check Node.js
    if ! command -v node >/dev/null 2>&1; then
        log_error "Node.js is not installed"
        return 1
    fi
    
    # Check npm
    if ! command -v npm >/dev/null 2>&1; then
        log_error "npm is not installed"
        return 1
    fi
    
    log_success "System requirements check passed"
    return 0
}

# Main function
main() {
    case "${1:-start}" in
        start)
            log_info "Starting Aixiate Webapp services..."
            
            if ! check_requirements; then
                exit 1
            fi
            
            # Start backend first
            if start_backend; then
                log_success "Backend is running"
            else
                log_error "Failed to start backend"
                exit 1
            fi
            
            # Start frontend
            if start_frontend; then
                log_success "Frontend is running"
            else
                log_error "Failed to start frontend"
                stop_services
                exit 1
            fi
            
            log_success "All services started successfully!"
            log_info "Backend: http://localhost:$BACKEND_PORT"
            log_info "Frontend: http://localhost:$FRONTEND_PORT"
            log_info "API Docs: http://localhost:$BACKEND_PORT/docs"
            log_info ""
            log_info "Press Ctrl+C to stop all services"
            
            # Wait for interrupt signal
            trap stop_services INT TERM
            wait
            ;;
        stop)
            stop_services
            ;;
        restart)
            stop_services
            sleep 2
            main start
            ;;
        status)
            log_info "Checking service status..."
            if check_port $BACKEND_PORT "Backend"; then
                log_success "Backend is running on port $BACKEND_PORT"
            else
                log_warning "Backend is not running"
            fi
            
            if check_port $FRONTEND_PORT "Frontend"; then
                log_success "Frontend is running on port $FRONTEND_PORT"
            else
                log_warning "Frontend is not running"
            fi
            ;;
        *)
            echo "Usage: $0 {start|stop|restart|status}"
            echo ""
            echo "Commands:"
            echo "  start   - Start both backend and frontend services"
            echo "  stop    - Stop all services"
            echo "  restart - Restart all services"
            echo "  status  - Check service status"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
