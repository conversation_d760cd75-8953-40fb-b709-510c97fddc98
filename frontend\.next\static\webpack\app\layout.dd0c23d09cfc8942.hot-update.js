"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/lib/api-client.ts":
/*!*******************************!*\
  !*** ./src/lib/api-client.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   apiClientInstance: () => (/* binding */ apiClientInstance),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\n// API Configuration\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\nconst API_VERSION = '/api/v1';\n// Cache configuration\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds\nconst CACHE_PREFIX = 'api_cache_';\n// Cache utility functions\nclass CacheManager {\n    static getInstance() {\n        if (!CacheManager.instance) {\n            CacheManager.instance = new CacheManager();\n        }\n        return CacheManager.instance;\n    }\n    set(key, data) {\n        let duration = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : CACHE_DURATION;\n        const expiresAt = Date.now() + duration;\n        this.cache.set(key, {\n            data,\n            timestamp: Date.now(),\n            expiresAt\n        });\n        // Also store in localStorage for persistence across page reloads\n        if (true) {\n            try {\n                localStorage.setItem(\"\".concat(CACHE_PREFIX).concat(key), JSON.stringify({\n                    data,\n                    timestamp: Date.now(),\n                    expiresAt\n                }));\n            } catch (error) {\n                console.warn('Failed to store cache in localStorage:', error);\n            }\n        }\n    }\n    get(key) {\n        // Check memory cache first\n        const memoryEntry = this.cache.get(key);\n        if (memoryEntry && memoryEntry.expiresAt > Date.now()) {\n            return memoryEntry.data;\n        }\n        // Check localStorage\n        if (true) {\n            try {\n                const stored = localStorage.getItem(\"\".concat(CACHE_PREFIX).concat(key));\n                if (stored) {\n                    const entry = JSON.parse(stored);\n                    if (entry.expiresAt > Date.now()) {\n                        // Update memory cache\n                        this.cache.set(key, entry);\n                        return entry.data;\n                    } else {\n                        // Remove expired entry\n                        localStorage.removeItem(\"\".concat(CACHE_PREFIX).concat(key));\n                    }\n                }\n            } catch (error) {\n                console.warn('Failed to retrieve cache from localStorage:', error);\n            }\n        }\n        return null;\n    }\n    delete(key) {\n        this.cache.delete(key);\n        if (true) {\n            localStorage.removeItem(\"\".concat(CACHE_PREFIX).concat(key));\n        }\n    }\n    clear(pattern) {\n        if (pattern) {\n            // Clear specific pattern\n            for (const key of this.cache.keys()){\n                if (key.includes(pattern)) {\n                    this.cache.delete(key);\n                }\n            }\n            if (true) {\n                for(let i = localStorage.length - 1; i >= 0; i--){\n                    const key = localStorage.key(i);\n                    if (key && key.startsWith(CACHE_PREFIX) && key.includes(pattern)) {\n                        localStorage.removeItem(key);\n                    }\n                }\n            }\n        } else {\n            // Clear all cache\n            this.cache.clear();\n            if (true) {\n                for(let i = localStorage.length - 1; i >= 0; i--){\n                    const key = localStorage.key(i);\n                    if (key && key.startsWith(CACHE_PREFIX)) {\n                        localStorage.removeItem(key);\n                    }\n                }\n            }\n        }\n    }\n    generateKey(config) {\n        const { method, url, params, data } = config;\n        return \"\".concat(method === null || method === void 0 ? void 0 : method.toUpperCase(), \"_\").concat(url, \"_\").concat(JSON.stringify(params), \"_\").concat(JSON.stringify(data));\n    }\n    constructor(){\n        this.cache = new Map();\n    }\n}\n// Create axios instance\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: \"\".concat(API_BASE_URL).concat(API_VERSION),\n    timeout: 30000,\n    withCredentials: false,\n    headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n    }\n});\n// Request interceptor to add auth token\napiClient.interceptors.request.use((config)=>{\n    // Get token from localStorage or cookies\n    const token =  true ? localStorage.getItem('access_token') : 0;\n    if (token) {\n        config.headers.Authorization = \"Bearer \".concat(token);\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor for token refresh and error handling\napiClient.interceptors.response.use((response)=>{\n    return response;\n}, async (error)=>{\n    var _error_response;\n    const originalRequest = error.config;\n    // Handle 401 Unauthorized - try to refresh token\n    if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        try {\n            const refreshToken =  true ? localStorage.getItem('refresh_token') : 0;\n            if (refreshToken) {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(API_BASE_URL).concat(API_VERSION, \"/auth/refresh\"), {\n                    refresh_token: refreshToken\n                });\n                const { access_token, refresh_token } = response.data;\n                // Store new tokens\n                if (true) {\n                    localStorage.setItem('access_token', access_token);\n                    localStorage.setItem('refresh_token', refresh_token);\n                }\n                // Retry original request with new token\n                originalRequest.headers.Authorization = \"Bearer \".concat(access_token);\n                return apiClient(originalRequest);\n            }\n        } catch (e) {\n            // Refresh failed - clear tokens and redirect to login\n            if (true) {\n                localStorage.removeItem('access_token');\n                localStorage.removeItem('refresh_token');\n                window.location.href = '/login';\n            }\n        }\n    }\n    return Promise.reject(error);\n});\n// API Client class with typed methods and caching\nclass ApiClient {\n    // Generic request method with caching\n    async request(config) {\n        let useCache = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false, cacheDuration = arguments.length > 2 ? arguments[2] : void 0;\n        try {\n            var _config_method, _config_method1;\n            // Check cache for GET requests\n            if (useCache && ((_config_method = config.method) === null || _config_method === void 0 ? void 0 : _config_method.toLowerCase()) === 'get') {\n                const cacheKey = this.cache.generateKey(config);\n                const cachedData = this.cache.get(cacheKey);\n                if (cachedData) {\n                    return cachedData;\n                }\n            }\n            const response = await this.client.request(config);\n            // Cache successful GET responses\n            if (useCache && ((_config_method1 = config.method) === null || _config_method1 === void 0 ? void 0 : _config_method1.toLowerCase()) === 'get' && response.status === 200) {\n                const cacheKey = this.cache.generateKey(config);\n                this.cache.set(cacheKey, response.data, cacheDuration);\n            }\n            return response.data;\n        } catch (error) {\n            throw this.handleError(error);\n        }\n    }\n    // Cached GET request\n    async get(url, params) {\n        let useCache = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true, cacheDuration = arguments.length > 3 ? arguments[3] : void 0;\n        return this.request({\n            method: 'GET',\n            url,\n            params\n        }, useCache, cacheDuration);\n    }\n    // POST request (no caching)\n    async post(url, data) {\n        return this.request({\n            method: 'POST',\n            url,\n            data\n        }, false);\n    }\n    // PUT request (no caching)\n    async put(url, data) {\n        return this.request({\n            method: 'PUT',\n            url,\n            data\n        }, false);\n    }\n    // DELETE request (no caching)\n    async delete(url) {\n        return this.request({\n            method: 'DELETE',\n            url\n        }, false);\n    }\n    // Clear cache\n    clearCache(pattern) {\n        this.cache.clear(pattern);\n    }\n    // Test connection to backend\n    async testConnection() {\n        try {\n            console.log('Testing connection to:', \"\".concat(this.client.defaults.baseURL, \"/health\"));\n            const response = await this.client.get('/health', {\n                timeout: 10000,\n                validateStatus: ()=>true // Accept any status code\n            });\n            if (response.status === 200) {\n                return {\n                    success: true,\n                    message: 'Backend connection successful',\n                    details: response.data\n                };\n            } else {\n                return {\n                    success: false,\n                    message: \"Backend responded with status \".concat(response.status),\n                    details: response.data\n                };\n            }\n        } catch (error) {\n            console.error('Connection test failed:', error);\n            if (error.code === 'ECONNREFUSED') {\n                return {\n                    success: false,\n                    message: 'Backend server is not running. Please start the backend server on http://localhost:8000',\n                    details: {\n                        error: error.code,\n                        message: error.message\n                    }\n                };\n            } else if (error.code === 'ENOTFOUND') {\n                return {\n                    success: false,\n                    message: 'Backend server not found. Please check the API URL configuration.',\n                    details: {\n                        error: error.code,\n                        message: error.message\n                    }\n                };\n            } else {\n                return {\n                    success: false,\n                    message: \"Connection failed: \".concat(error.message),\n                    details: {\n                        error: error.code,\n                        message: error.message\n                    }\n                };\n            }\n        }\n    }\n    // Handle API errors with enhanced diagnostics\n    handleError(error) {\n        console.error('API Client Error:', error);\n        if (error && typeof error === 'object' && 'response' in error) {\n            // Server responded with error status\n            const { status, data } = error.response;\n            // Prefer server-provided detail when available so the UI can show precise feedback\n            const serverMessage = data === null || data === void 0 ? void 0 : data.detail;\n            switch(status){\n                case 400:\n                    return new Error(serverMessage || 'Bad request');\n                case 401:\n                    // Pass through messages such as \"Account is locked\" when supplied\n                    return new Error(serverMessage || 'Unauthorized - please login again');\n                case 403:\n                    return new Error(serverMessage || 'Access denied - insufficient permissions');\n                case 404:\n                    return new Error(serverMessage || 'Resource not found');\n                case 422:\n                    return new Error(serverMessage || 'Validation error');\n                case 429:\n                    return new Error(serverMessage || 'Too many requests - please try again later');\n                case 500:\n                    return new Error(serverMessage || 'Internal server error');\n                default:\n                    return new Error(serverMessage || 'An error occurred');\n            }\n        } else if (error && typeof error === 'object' && 'request' in error) {\n            var _axiosError_config, _axiosError_config1, _axiosError_config2, _axiosError_config3;\n            // Network error - provide more detailed diagnostics\n            const axiosError = error;\n            console.error('Network Error Details:', {\n                code: axiosError.code,\n                message: axiosError.message,\n                config: {\n                    url: (_axiosError_config = axiosError.config) === null || _axiosError_config === void 0 ? void 0 : _axiosError_config.url,\n                    method: (_axiosError_config1 = axiosError.config) === null || _axiosError_config1 === void 0 ? void 0 : _axiosError_config1.method,\n                    baseURL: (_axiosError_config2 = axiosError.config) === null || _axiosError_config2 === void 0 ? void 0 : _axiosError_config2.baseURL,\n                    timeout: (_axiosError_config3 = axiosError.config) === null || _axiosError_config3 === void 0 ? void 0 : _axiosError_config3.timeout\n                }\n            });\n            // Provide specific error messages based on error code\n            if (axiosError.code === 'ECONNREFUSED') {\n                return new Error('Connection refused - Backend server may not be running. Please check if the server is started on http://localhost:8000');\n            } else if (axiosError.code === 'ENOTFOUND') {\n                return new Error('Server not found - Please check the API URL configuration');\n            } else if (axiosError.code === 'ETIMEDOUT') {\n                return new Error('Request timeout - Server is taking too long to respond');\n            } else if (axiosError.code === 'ECONNABORTED') {\n                return new Error('Request aborted - Connection was terminated');\n            } else {\n                return new Error(\"Network error (\".concat(axiosError.code || 'UNKNOWN', \") - Please check your connection and ensure the backend server is running\"));\n            }\n        } else {\n            // Other error\n            const message = error && typeof error === 'object' && 'message' in error ? String(error.message) : 'An unexpected error occurred';\n            return new Error(message);\n        }\n    }\n    // Token management\n    setTokens(accessToken, refreshToken) {\n        if (true) {\n            localStorage.setItem('access_token', accessToken);\n            localStorage.setItem('refresh_token', refreshToken);\n        }\n    }\n    clearTokens() {\n        if (true) {\n            localStorage.removeItem('access_token');\n            localStorage.removeItem('refresh_token');\n        }\n    }\n    getAccessToken() {\n        if (true) {\n            return localStorage.getItem('access_token');\n        }\n        return null;\n    }\n    getRefreshToken() {\n        if (true) {\n            return localStorage.getItem('refresh_token');\n        }\n        return null;\n    }\n    constructor(){\n        this.client = apiClient;\n        this.cache = CacheManager.getInstance();\n    }\n}\n// Export singleton instance\nconst apiClientInstance = new ApiClient();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClientInstance);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api-client.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d8f4be2d6359\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcbmV4dGpzXFx3ZWJhcHBcXGZyb250ZW5kXFxzcmNcXHN0eWxlc1xcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkOGY0YmUyZDYzNTlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});