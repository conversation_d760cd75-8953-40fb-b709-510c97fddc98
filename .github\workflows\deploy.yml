name: Deploy to Staging/Production

on:
  workflow_run:
    workflows: ["CI Pipeline"]
    types:
      - completed
    branches: [develop, main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # ============================================================================
  # Deploy to Staging
  # ============================================================================
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    if: github.event.workflow_run.conclusion == 'success' && github.event.workflow_run.head_branch == 'develop'
    environment:
      name: staging
      url: https://staging.aixiate.com

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Deploy to ECS Staging
        run: |
          # Update ECS service with new image
          aws ecs update-service \
            --cluster aixiate-staging \
            --service aixiate-backend-staging \
            --force-new-deployment

          aws ecs update-service \
            --cluster aixiate-staging \
            --service aixiate-frontend-staging \
            --force-new-deployment

      - name: Wait for deployment
        run: |
          aws ecs wait services-stable \
            --cluster aixiate-staging \
            --services aixiate-backend-staging aixiate-frontend-staging

      - name: Run database migrations
        run: |
          aws ecs run-task \
            --cluster aixiate-staging \
            --task-definition aixiate-migration-staging \
            --launch-type FARGATE \
            --network-configuration "awsvpcConfiguration={subnets=[${{ secrets.STAGING_SUBNET_IDS }}],securityGroups=[${{ secrets.STAGING_SECURITY_GROUP }}],assignPublicIp=ENABLED}"

      - name: Health check
        run: |
          # Wait for services to be healthy
          sleep 30
          
          # Check backend health
          curl -f https://api-staging.aixiate.com/health || exit 1
          
          # Check frontend
          curl -f https://staging.aixiate.com || exit 1

      - name: Run smoke tests
        run: |
          # Run critical path tests
          cd frontend
          npm ci
          npx playwright test --grep "@smoke"

      - name: Notify deployment status
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          text: 'Staging deployment completed successfully! 🚀'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # ============================================================================
  # Deploy to Production
  # ============================================================================
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    if: github.event.workflow_run.conclusion == 'success' && github.event.workflow_run.head_branch == 'main'
    environment:
      name: production
      url: https://aixiate.com
    needs: []  # Can run independently, but requires manual approval

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Create deployment backup
        run: |
          # Create database backup before deployment
          aws rds create-db-snapshot \
            --db-instance-identifier aixiate-prod \
            --db-snapshot-identifier "pre-deploy-$(date +%Y%m%d-%H%M%S)"

      - name: Blue/Green Deployment - Backend
        run: |
          # Get current task definition
          TASK_DEF=$(aws ecs describe-task-definition --task-definition aixiate-backend-prod --query 'taskDefinition')
          
          # Update image in task definition
          NEW_TASK_DEF=$(echo $TASK_DEF | jq --arg IMAGE "${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-backend:main" '.containerDefinitions[0].image = $IMAGE')
          
          # Register new task definition
          aws ecs register-task-definition --cli-input-json "$NEW_TASK_DEF"
          
          # Update service with new task definition
          aws ecs update-service \
            --cluster aixiate-prod \
            --service aixiate-backend-prod \
            --task-definition aixiate-backend-prod

      - name: Blue/Green Deployment - Frontend
        run: |
          # Similar process for frontend
          TASK_DEF=$(aws ecs describe-task-definition --task-definition aixiate-frontend-prod --query 'taskDefinition')
          NEW_TASK_DEF=$(echo $TASK_DEF | jq --arg IMAGE "${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-frontend:main" '.containerDefinitions[0].image = $IMAGE')
          aws ecs register-task-definition --cli-input-json "$NEW_TASK_DEF"
          aws ecs update-service \
            --cluster aixiate-prod \
            --service aixiate-frontend-prod \
            --task-definition aixiate-frontend-prod

      - name: Wait for deployment
        run: |
          aws ecs wait services-stable \
            --cluster aixiate-prod \
            --services aixiate-backend-prod aixiate-frontend-prod

      - name: Run database migrations
        run: |
          aws ecs run-task \
            --cluster aixiate-prod \
            --task-definition aixiate-migration-prod \
            --launch-type FARGATE \
            --network-configuration "awsvpcConfiguration={subnets=[${{ secrets.PROD_SUBNET_IDS }}],securityGroups=[${{ secrets.PROD_SECURITY_GROUP }}],assignPublicIp=ENABLED}"

      - name: Health check and validation
        run: |
          # Wait for services to stabilize
          sleep 60
          
          # Comprehensive health checks
          curl -f https://api.aixiate.com/health || exit 1
          curl -f https://api.aixiate.com/health/database || exit 1
          curl -f https://aixiate.com || exit 1
          
          # Check response times
          RESPONSE_TIME=$(curl -o /dev/null -s -w '%{time_total}' https://api.aixiate.com/health)
          if (( $(echo "$RESPONSE_TIME > 1.0" | bc -l) )); then
            echo "Response time too high: $RESPONSE_TIME seconds"
            exit 1
          fi

      - name: Run production smoke tests
        run: |
          cd frontend
          npm ci
          PLAYWRIGHT_BASE_URL=https://aixiate.com npx playwright test --grep "@smoke"

      - name: Update monitoring and alerts
        run: |
          # Update deployment markers in monitoring systems
          curl -X POST "https://api.datadoghq.com/api/v1/events" \
            -H "Content-Type: application/json" \
            -H "DD-API-KEY: ${{ secrets.DATADOG_API_KEY }}" \
            -d '{
              "title": "Production Deployment",
              "text": "Successfully deployed to production",
              "tags": ["deployment", "production"],
              "alert_type": "success"
            }'

      - name: Notify deployment success
        uses: 8398a7/action-slack@v3
        with:
          status: success
          channel: '#deployments'
          text: 'Production deployment completed successfully! 🎉'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # ============================================================================
  # Rollback Job (Manual Trigger)
  # ============================================================================
  rollback:
    name: Rollback Deployment
    runs-on: ubuntu-latest
    if: failure() || github.event.inputs.rollback == 'true'
    environment:
      name: production

    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Rollback to previous version
        run: |
          # Get previous task definition revision
          PREV_REVISION=$(aws ecs describe-services \
            --cluster aixiate-prod \
            --services aixiate-backend-prod \
            --query 'services[0].deployments[1].taskDefinition' \
            --output text)
          
          # Update service to previous revision
          aws ecs update-service \
            --cluster aixiate-prod \
            --service aixiate-backend-prod \
            --task-definition $PREV_REVISION

      - name: Restore database if needed
        run: |
          # Only if database migration rollback is needed
          if [ "${{ github.event.inputs.rollback_db }}" == "true" ]; then
            echo "Database rollback requires manual intervention"
            echo "Please check the database migration rollback procedures"
          fi

      - name: Notify rollback
        uses: 8398a7/action-slack@v3
        with:
          status: custom
          custom_payload: |
            {
              channel: '#alerts',
              text: '🚨 Production rollback initiated',
              color: 'danger'
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # ============================================================================
  # Performance Testing (Post-Deployment)
  # ============================================================================
  performance-test:
    name: Performance Testing
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: success()

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Lighthouse CI
        uses: treosh/lighthouse-ci-action@v10
        with:
          urls: |
            https://aixiate.com
            https://aixiate.com/dashboard
            https://aixiate.com/auth/login
          configPath: './lighthouse.config.js'
          uploadArtifacts: true

      - name: Load testing with Artillery
        run: |
          npm install -g artillery
          artillery run performance-tests/load-test.yml

      - name: API performance testing
        run: |
          # Test critical API endpoints
          curl -w "@curl-format.txt" -o /dev/null -s https://api.aixiate.com/api/v1/auth/me
          curl -w "@curl-format.txt" -o /dev/null -s https://api.aixiate.com/health
