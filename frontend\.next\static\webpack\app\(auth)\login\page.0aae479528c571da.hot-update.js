"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(auth)/login/page",{

/***/ "(app-pages-browser)/./src/contexts/auth-context.tsx":
/*!***************************************!*\
  !*** ./src/contexts/auth-context.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthContext: () => (/* binding */ AuthContext),\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth-service */ \"(app-pages-browser)/./src/lib/auth-service.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,AuthContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n// Create context\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nvar _process_env_NEXT_PUBLIC_IDLE_LOGOUT_MINUTES;\n// Idle logout timeout – configurable via env var, defaults to 30 minutes\nconst IDLE_LOGOUT_MINUTES = parseInt((_process_env_NEXT_PUBLIC_IDLE_LOGOUT_MINUTES = \"30\") !== null && _process_env_NEXT_PUBLIC_IDLE_LOGOUT_MINUTES !== void 0 ? _process_env_NEXT_PUBLIC_IDLE_LOGOUT_MINUTES : \"30\", 10);\nconst IDLE_LOGOUT_MS = IDLE_LOGOUT_MINUTES * 60000;\n// Authentication Provider Component\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // State\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userFromToken, setUserFromToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Reactive authentication state\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // -------------------\n    // Proactive token refresh + idle logout\n    // -------------------\n    const refreshTimeoutRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(null);\n    const idleTimeoutRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(null);\n    const clearTimers = ()=>{\n        if (refreshTimeoutRef.current) {\n            clearTimeout(refreshTimeoutRef.current);\n            refreshTimeoutRef.current = null;\n        }\n        if (idleTimeoutRef.current) {\n            clearTimeout(idleTimeoutRef.current);\n            idleTimeoutRef.current = null;\n        }\n    };\n    // Logout function (moved here so it's defined before use in other hooks)\n    const logout = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"AuthProvider.useCallback[logout]\": async function(sessionId) {\n            let logoutAll = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            // Ensure any scheduled tasks are cancelled BEFORE state updates\n            clearTimers();\n            // Clear authentication state synchronously so UI can react immediately\n            setIsAuthenticated(false);\n            setUser(null);\n            setUserFromToken(null);\n            // Persisted auth data\n            _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.clearAuth();\n            // Attempt server-side logout *after* local cleanup so we don't block UI\n            const shouldCallApiLogout = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isAuthenticated() && !_lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isTokenExpired();\n            if (shouldCallApiLogout) {\n                _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.logout(sessionId, logoutAll).catch(console.warn);\n            }\n            // Use Next.js router navigation rather than full page reload for smoother UX\n            router.replace(\"/login\");\n        }\n    }[\"AuthProvider.useCallback[logout]\"], [\n        router\n    ]);\n    const scheduleTokenRefresh = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"AuthProvider.useCallback[scheduleTokenRefresh]\": ()=>{\n            if (!_lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isAuthenticated()) return;\n            const expDate = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getTokenExpiration();\n            if (!expDate) return;\n            const now = new Date();\n            // Refresh 60 seconds before expiry (or immediately if already <1 min)\n            const msUntilRefresh = Math.max(expDate.getTime() - now.getTime() - 60000, 0);\n            if (refreshTimeoutRef.current) {\n                clearTimeout(refreshTimeoutRef.current);\n            }\n            refreshTimeoutRef.current = setTimeout({\n                \"AuthProvider.useCallback[scheduleTokenRefresh]\": async ()=>{\n                    try {\n                        await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.refreshToken();\n                        scheduleTokenRefresh(); // reschedule for next expiry\n                    } catch (err) {\n                        console.error(\"Token refresh failed\", err);\n                        await logout();\n                    }\n                }\n            }[\"AuthProvider.useCallback[scheduleTokenRefresh]\"], msUntilRefresh);\n        }\n    }[\"AuthProvider.useCallback[scheduleTokenRefresh]\"], [\n        logout\n    ]);\n    const resetIdleTimer = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"AuthProvider.useCallback[resetIdleTimer]\": ()=>{\n            if (idleTimeoutRef.current) {\n                clearTimeout(idleTimeoutRef.current);\n            }\n            idleTimeoutRef.current = setTimeout({\n                \"AuthProvider.useCallback[resetIdleTimer]\": ()=>{\n                    logout();\n                }\n            }[\"AuthProvider.useCallback[resetIdleTimer]\"], IDLE_LOGOUT_MS);\n        }\n    }[\"AuthProvider.useCallback[resetIdleTimer]\"], [\n        logout\n    ]);\n    // ----------------------------------------------\n    // Setup timers only when authentication *state* changes\n    // ----------------------------------------------\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!isAuthenticated) {\n                // User just logged out – ensure timers are cleared\n                clearTimers();\n                return;\n            }\n            // User is authenticated – start / resume timers **once**\n            scheduleTokenRefresh();\n            resetIdleTimer();\n            // Attach user-activity listeners to reset the idle timer\n            const events = [\n                \"mousemove\",\n                \"keydown\",\n                \"click\",\n                // Removing programmatic scroll events avoids accidental resets\n                //'scroll',\n                \"touchstart\"\n            ];\n            events.forEach({\n                \"AuthProvider.useEffect\": (e)=>window.addEventListener(e, resetIdleTimer)\n            }[\"AuthProvider.useEffect\"]);\n            // Cleanup when component unmounts or auth state changes\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    events.forEach({\n                        \"AuthProvider.useEffect\": (e)=>window.removeEventListener(e, resetIdleTimer)\n                    }[\"AuthProvider.useEffect\"]);\n                    clearTimers();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        isAuthenticated,\n        scheduleTokenRefresh,\n        resetIdleTimer\n    ]);\n    // Initialize authentication state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initializeAuth = {\n                \"AuthProvider.useEffect.initializeAuth\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        // Check if user is authenticated from token\n                        if (_lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isAuthenticated()) {\n                            const tokenUser = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUserFromToken();\n                            setUserFromToken(tokenUser);\n                            // Test backend connectivity before trying to fetch profile\n                            try {\n                                // Quick health check to verify backend is accessible\n                                await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.apiClient.testConnection();\n                            } catch (connectionError) {\n                                console.warn(\"Backend not accessible during auth init, skipping profile fetch\");\n                                // Still set token-based auth, user can retry later\n                                return;\n                            }\n                            // Try to fetch full user profile\n                            try {\n                                const userProfile = await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUser();\n                                setUser(userProfile);\n                            } catch (error) {\n                                console.warn(\"Failed to fetch user profile during initialization:\", error);\n                                // Handle different types of errors\n                                if (error && typeof error === \"object\") {\n                                    // Check for HTTP response errors\n                                    if (\"response\" in error) {\n                                        var _error_response;\n                                        const status = (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status;\n                                        if (status === 401 || status === 403) {\n                                            // Invalid token - clear auth\n                                            console.log(\"Token is invalid, clearing auth\");\n                                            _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.clearAuth();\n                                            setUser(null);\n                                            setUserFromToken(null);\n                                        }\n                                    // For other HTTP errors (404, 500, etc.), keep token-based auth\n                                    } else if (\"message\" in error) {\n                                        const errorMessage = error.message;\n                                        if (errorMessage.includes(\"Network Error\") || errorMessage.includes(\"ECONNREFUSED\") || errorMessage.includes(\"ENOTFOUND\")) {\n                                            // Network connectivity issues - don't clear auth, user can retry later\n                                            console.warn(\"Network error during profile fetch, keeping token-based auth\");\n                                        } else {\n                                            // Other errors - log but don't clear auth\n                                            console.warn(\"Unknown error during profile fetch:\", errorMessage);\n                                        }\n                                    }\n                                }\n                            }\n                        } else {\n                            // Clear any stale data\n                            _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.clearAuth();\n                            setUser(null);\n                            setUserFromToken(null);\n                        }\n                    } catch (error) {\n                        console.error(\"Auth initialization error:\", error);\n                        _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.clearAuth();\n                        setUser(null);\n                        setUserFromToken(null);\n                    } finally{\n                        setIsLoading(false);\n                        setIsInitialized(true);\n                    }\n                }\n            }[\"AuthProvider.useEffect.initializeAuth\"];\n            initializeAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Refresh user data\n    const refreshUser = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[refreshUser]\": async ()=>{\n            try {\n                // Debug statements removed for production\n                // Force check auth state first\n                const currentAuthState = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isAuthenticated();\n                setIsAuthenticated(currentAuthState);\n                if (!currentAuthState) {\n                    //\n                    return;\n                }\n                // Try to get user profile, but don't fail if it doesn't work\n                try {\n                    const userProfile = await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUser();\n                    setUser(userProfile);\n                } catch (profileError) {\n                    console.warn(\"Failed to refresh user profile:\", profileError);\n                // Keep existing user data if profile fetch fails\n                }\n                // Update token user as well\n                const tokenUser = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUserFromToken();\n                setUserFromToken(tokenUser);\n            //\n            } catch (error) {\n                var _error_response;\n                console.error(\"Failed to refresh user:\", error);\n                console.error(\"Error details:\", {\n                    message: error instanceof Error ? error.message : \"Unknown error\",\n                    status: error && typeof error === \"object\" && \"response\" in error ? (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status : \"No status\"\n                });\n                // If refresh fails, user might be logged out\n                logout();\n            }\n        }\n    }[\"AuthProvider.useCallback[refreshUser]\"], [\n        logout\n    ]);\n    // Login function\n    const login = async (credentials)=>{\n        try {\n            setIsLoading(true);\n            await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.login(credentials);\n            // Get user profile - handle gracefully if it fails\n            try {\n                const userProfile = await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUser();\n                setUser(userProfile);\n            } catch (profileError) {\n                console.warn(\"Failed to get user profile after login:\", profileError);\n            // Don't fail the login if profile fetch fails\n            // The user can still access the app with token-based auth\n            }\n            const tokenUser = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUserFromToken();\n            setUserFromToken(tokenUser);\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Login successful!\");\n            // Redirect to dashboard - role-based rendering will be handled by the dashboard page\n            router.push(\"/dashboard\");\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            const errorMessage = error instanceof Error ? error.message : \"Login failed\";\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(errorMessage);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Register function\n    const register = async (userData)=>{\n        try {\n            setIsLoading(true);\n            const response = await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.register(userData);\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success(response.message || \"Registration successful! Please check your email to verify your account.\");\n            // Redirect to login page\n            router.push(\"/login\");\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            const errorMessage = error instanceof Error ? error.message : \"Registration failed\";\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(errorMessage);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Clear authentication data\n    const clearAuth = ()=>{\n        setUser(null);\n        setUserFromToken(null);\n        _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.clearAuth();\n    };\n    // Check if user has OAuth provider linked\n    const hasOAuthProvider = (provider)=>{\n        return _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.hasOAuthProvider(provider);\n    };\n    // Get OAuth providers list\n    const getOAuthProviders = async ()=>{\n        try {\n            return await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getOAuthProviders();\n        } catch (error) {\n            console.error(\"Failed to get OAuth providers:\", error);\n            return {};\n        }\n    };\n    // Update authentication state when tokens change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const updateAuthState = {\n                \"AuthProvider.useEffect.updateAuthState\": ()=>{\n                    const authState = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isAuthenticated();\n                    //\n                    setIsAuthenticated(authState);\n                }\n            }[\"AuthProvider.useEffect.updateAuthState\"];\n            // Initial check\n            updateAuthState();\n            // Listen for storage changes (when tokens are added/removed)\n            const handleStorageChange = {\n                \"AuthProvider.useEffect.handleStorageChange\": (e)=>{\n                    if (e.key === \"access_token\" || e.key === \"refresh_token\") {\n                        //\n                        updateAuthState();\n                    }\n                }\n            }[\"AuthProvider.useEffect.handleStorageChange\"];\n            window.addEventListener(\"storage\", handleStorageChange);\n            // Also check periodically for token expiration\n            const interval = setInterval(updateAuthState, 5000);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    window.removeEventListener(\"storage\", handleStorageChange);\n                    clearInterval(interval);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Context value\n    const value = {\n        user,\n        userFromToken,\n        isAuthenticated,\n        isLoading,\n        isInitialized,\n        login,\n        register,\n        logout,\n        refreshUser,\n        clearAuth,\n        hasOAuthProvider,\n        getOAuthProviders\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\contexts\\\\auth-context.tsx\",\n        lineNumber: 443,\n        columnNumber: 10\n    }, this);\n}\n_s(AuthProvider, \"vHRPUYhL0MK6zoFbLb0c6cGc8Pg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthProvider;\n// Custom hook to use auth context\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Export the context for advanced usage\n\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/auth-context.tsx\n"));

/***/ })

});