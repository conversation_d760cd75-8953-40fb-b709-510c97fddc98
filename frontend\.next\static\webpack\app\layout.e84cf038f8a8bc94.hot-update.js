"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/lib/auth-service.ts":
/*!*********************************!*\
  !*** ./src/lib/auth-service.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: () => (/* binding */ AuthService),\n/* harmony export */   authService: () => (/* binding */ authService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _api_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jwt-decode */ \"(app-pages-browser)/./node_modules/jwt-decode/build/esm/index.js\");\n\n\n// Authentication Service Class\nclass AuthService {\n    // Login user\n    async login(credentials) {\n        const response = await this.apiClient.request({\n            method: 'POST',\n            url: '/auth/login',\n            data: credentials\n        });\n        // Store tokens\n        this.apiClient.setTokens(response.access_token, response.refresh_token);\n        return response;\n    }\n    // Register user\n    async register(userData) {\n        const response = await this.apiClient.request({\n            method: 'POST',\n            url: '/auth/register',\n            data: userData\n        });\n        return response;\n    }\n    // Logout user\n    async logout(sessionId) {\n        let logoutAll = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        const response = await this.apiClient.request({\n            method: 'POST',\n            url: '/auth/logout',\n            data: {\n                session_id: sessionId,\n                logout_all: logoutAll\n            }\n        });\n        // Clear tokens\n        this.apiClient.clearTokens();\n        return response;\n    }\n    // Refresh access token\n    async refreshToken() {\n        const refreshToken = this.apiClient.getRefreshToken();\n        if (!refreshToken) {\n            throw new Error('No refresh token available');\n        }\n        const response = await this.apiClient.request({\n            method: 'POST',\n            url: '/auth/refresh',\n            data: {\n                refresh_token: refreshToken\n            }\n        });\n        // Update stored tokens\n        this.apiClient.setTokens(response.access_token, response.refresh_token);\n        return response;\n    }\n    // Get current user profile\n    async getCurrentUser() {\n        const response = await this.apiClient.request({\n            method: 'GET',\n            url: '/auth/me'\n        });\n        return response;\n    }\n    // Get user sessions\n    async getUserSessions() {\n        const response = await this.apiClient.request({\n            method: 'GET',\n            url: '/auth/sessions'\n        });\n        return response;\n    }\n    // Revoke specific session\n    async revokeSession(sessionId) {\n        const response = await this.apiClient.request({\n            method: 'DELETE',\n            url: \"/auth/sessions/\".concat(sessionId)\n        });\n        return response;\n    }\n    // Request password reset\n    async requestPasswordReset(email) {\n        const response = await this.apiClient.request({\n            method: 'POST',\n            url: '/auth/forgot-password',\n            data: {\n                email\n            }\n        });\n        return response;\n    }\n    // Complete password reset\n    async completePasswordReset(token, newPassword) {\n        const response = await this.apiClient.request({\n            method: 'POST',\n            url: '/auth/reset-password',\n            data: {\n                token,\n                new_password: newPassword\n            }\n        });\n        return response;\n    }\n    // Check if user is authenticated\n    isAuthenticated() {\n        const token = this.apiClient.getAccessToken();\n        if (!token) return false;\n        try {\n            const decoded = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_1__.jwtDecode)(token);\n            const currentTime = Math.floor(Date.now() / 1000);\n            return decoded.exp > currentTime;\n        } catch (e) {\n            return false;\n        }\n    }\n    // Get current user from token\n    getCurrentUserFromToken() {\n        const token = this.apiClient.getAccessToken();\n        if (!token) return null;\n        try {\n            return (0,jwt_decode__WEBPACK_IMPORTED_MODULE_1__.jwtDecode)(token);\n        } catch (e) {\n            return null;\n        }\n    }\n    // Check if token is expired\n    isTokenExpired() {\n        const token = this.apiClient.getAccessToken();\n        if (!token) return true;\n        try {\n            const decoded = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_1__.jwtDecode)(token);\n            const currentTime = Math.floor(Date.now() / 1000);\n            return decoded.exp <= currentTime;\n        } catch (e) {\n            return true;\n        }\n    }\n    // Get token expiration time\n    getTokenExpiration() {\n        const token = this.apiClient.getAccessToken();\n        if (!token) return null;\n        try {\n            const decoded = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_1__.jwtDecode)(token);\n            return new Date(decoded.exp * 1000);\n        } catch (e) {\n            return null;\n        }\n    }\n    // Clear all authentication data\n    clearAuth() {\n        this.apiClient.clearTokens();\n    }\n    // Verify email\n    async verifyEmail(token) {\n        const response = await this.apiClient.request({\n            method: 'POST',\n            url: '/auth/verify-email',\n            data: {\n                token\n            }\n        });\n        return response;\n    }\n    // Resend verification email\n    async resendVerification(email) {\n        const response = await this.apiClient.request({\n            method: 'POST',\n            url: '/auth/resend-verification',\n            data: {\n                email\n            }\n        });\n        return response;\n    }\n    // OAuth-related methods\n    async handleOAuthCallback(provider, code, state) {\n        const response = await this.apiClient.request({\n            method: 'GET',\n            url: \"/oauth/\".concat(provider, \"/callback\"),\n            params: {\n                code,\n                state\n            }\n        });\n        return response;\n    }\n    // Get OAuth providers list\n    async getOAuthProviders() {\n        const response = await this.apiClient.request({\n            method: 'GET',\n            url: '/oauth/providers'\n        });\n        return response;\n    }\n    // Check if user has OAuth provider linked\n    hasOAuthProvider(provider) {\n        const user = this.getCurrentUserFromToken();\n        return (user === null || user === void 0 ? void 0 : user.oauth_provider) === provider;\n    }\n    // Test backend connection\n    async testConnection() {\n        return await this.apiClient.testConnection();\n    }\n    constructor(){\n        this.apiClient = _api_client__WEBPACK_IMPORTED_MODULE_0__.apiClientInstance;\n    }\n}\n// Export singleton instance\nconst authService = new AuthService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (authService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/auth-service.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"802f699de88a\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcbmV4dGpzXFx3ZWJhcHBcXGZyb250ZW5kXFxzcmNcXHN0eWxlc1xcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4MDJmNjk5ZGU4OGFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});