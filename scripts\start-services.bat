@echo off
setlocal enabledelayedexpansion

REM Aixiate Webapp Service Startup Script for Windows
REM This script starts both backend and frontend services with proper error handling

set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%.."
set "BACKEND_DIR=%PROJECT_ROOT%\backend"
set "FRONTEND_DIR=%PROJECT_ROOT%\frontend"
set "BACKEND_PORT=8000"
set "FRONTEND_PORT=3000"

REM Color codes (limited support in Windows)
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM Logging functions
:log_info
echo %BLUE%[INFO]%NC% %~1
goto :eof

:log_success
echo %GREEN%[SUCCESS]%NC% %~1
goto :eof

:log_warning
echo %YELLOW%[WARNING]%NC% %~1
goto :eof

:log_error
echo %RED%[ERROR]%NC% %~1
goto :eof

REM Check if a port is in use
:check_port
set "port=%~1"
set "service=%~2"
netstat -an | findstr ":%port%" | findstr "LISTENING" >nul 2>&1
if !errorlevel! equ 0 (
    call :log_warning "%service% is already running on port %port%"
    exit /b 0
) else (
    exit /b 1
)

REM Wait for service to be ready
:wait_for_service
set "url=%~1"
set "service=%~2"
set "max_attempts=30"
set "attempt=1"

call :log_info "Waiting for %service% to be ready..."

:wait_loop
if !attempt! gtr !max_attempts! (
    call :log_error "%service% failed to start within 60 seconds"
    exit /b 1
)

curl -s -f "%url%" >nul 2>&1
if !errorlevel! equ 0 (
    call :log_success "%service% is ready!"
    exit /b 0
)

echo|set /p="."
timeout /t 2 /nobreak >nul
set /a attempt+=1
goto wait_loop

REM Start backend service
:start_backend
call :log_info "Starting backend service..."

REM Check if backend is already running
call :check_port %BACKEND_PORT% "Backend"
if !errorlevel! equ 0 exit /b 0

REM Check if backend directory exists
if not exist "%BACKEND_DIR%" (
    call :log_error "Backend directory not found: %BACKEND_DIR%"
    exit /b 1
)

cd /d "%BACKEND_DIR%"

REM Check if virtual environment exists
if not exist ".venv" (
    call :log_info "Creating Python virtual environment..."
    python -m venv .venv
    if !errorlevel! neq 0 (
        call :log_error "Failed to create virtual environment"
        exit /b 1
    )
)

REM Activate virtual environment
call .venv\Scripts\activate.bat
if !errorlevel! neq 0 (
    call :log_error "Failed to activate virtual environment"
    exit /b 1
)

REM Install/update dependencies
where uv >nul 2>&1
if !errorlevel! equ 0 (
    call :log_info "Installing dependencies with uv..."
    uv sync
) else (
    call :log_info "Installing uv and dependencies..."
    pip install uv
    uv sync
)

REM Check if .env file exists
if not exist ".env" (
    call :log_warning "Backend .env file not found. Creating from example..."
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
    ) else (
        call :log_error "No .env.example file found. Please create .env manually."
        exit /b 1
    )
)

REM Run database migrations
call :log_info "Running database migrations..."
alembic upgrade head
if !errorlevel! neq 0 (
    call :log_error "Database migration failed"
    exit /b 1
)

REM Start backend server
call :log_info "Starting FastAPI server..."
start "Backend Server" cmd /c "call .venv\Scripts\activate.bat && uvicorn app.main:app --host 0.0.0.0 --port %BACKEND_PORT% --reload"

REM Wait for backend to be ready
call :wait_for_service "http://localhost:%BACKEND_PORT%/health" "Backend"
if !errorlevel! equ 0 (
    call :log_success "Backend started successfully"
    exit /b 0
) else (
    call :log_error "Backend failed to start"
    exit /b 1
)

REM Start frontend service
:start_frontend
call :log_info "Starting frontend service..."

REM Check if frontend is already running
call :check_port %FRONTEND_PORT% "Frontend"
if !errorlevel! equ 0 exit /b 0

REM Check if frontend directory exists
if not exist "%FRONTEND_DIR%" (
    call :log_error "Frontend directory not found: %FRONTEND_DIR%"
    exit /b 1
)

cd /d "%FRONTEND_DIR%"

REM Check if node_modules exists
if not exist "node_modules" (
    call :log_info "Installing Node.js dependencies..."
    npm install
    if !errorlevel! neq 0 (
        call :log_error "Failed to install Node.js dependencies"
        exit /b 1
    )
)

REM Check if .env.local file exists
if not exist ".env.local" (
    call :log_warning "Frontend .env.local file not found. Creating from example..."
    if exist ".env.example" (
        copy ".env.example" ".env.local" >nul
    ) else (
        call :log_error "No .env.example file found. Please create .env.local manually."
        exit /b 1
    )
)

REM Start frontend server
call :log_info "Starting Next.js development server..."
start "Frontend Server" cmd /c "npm run dev"

REM Wait for frontend to be ready
call :wait_for_service "http://localhost:%FRONTEND_PORT%" "Frontend"
if !errorlevel! equ 0 (
    call :log_success "Frontend started successfully"
    exit /b 0
) else (
    call :log_error "Frontend failed to start"
    exit /b 1
)

REM Stop services
:stop_services
call :log_info "Stopping services..."

REM Kill processes on the ports
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":%BACKEND_PORT%"') do (
    taskkill /PID %%a /F >nul 2>&1
)

for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":%FRONTEND_PORT%"') do (
    taskkill /PID %%a /F >nul 2>&1
)

call :log_success "Services stopped"
exit /b 0

REM Check system requirements
:check_requirements
call :log_info "Checking system requirements..."

REM Check Python
python --version >nul 2>&1
if !errorlevel! neq 0 (
    call :log_error "Python is not installed or not in PATH"
    exit /b 1
)

REM Check Node.js
node --version >nul 2>&1
if !errorlevel! neq 0 (
    call :log_error "Node.js is not installed or not in PATH"
    exit /b 1
)

REM Check npm
npm --version >nul 2>&1
if !errorlevel! neq 0 (
    call :log_error "npm is not installed or not in PATH"
    exit /b 1
)

call :log_success "System requirements check passed"
exit /b 0

REM Main function
:main
set "command=%~1"
if "%command%"=="" set "command=start"

if "%command%"=="start" (
    call :log_info "Starting Aixiate Webapp services..."
    
    call :check_requirements
    if !errorlevel! neq 0 exit /b 1
    
    REM Start backend first
    call :start_backend
    if !errorlevel! equ 0 (
        call :log_success "Backend is running"
    ) else (
        call :log_error "Failed to start backend"
        exit /b 1
    )
    
    REM Start frontend
    call :start_frontend
    if !errorlevel! equ 0 (
        call :log_success "Frontend is running"
    ) else (
        call :log_error "Failed to start frontend"
        call :stop_services
        exit /b 1
    )
    
    call :log_success "All services started successfully!"
    call :log_info "Backend: http://localhost:%BACKEND_PORT%"
    call :log_info "Frontend: http://localhost:%FRONTEND_PORT%"
    call :log_info "API Docs: http://localhost:%BACKEND_PORT%/docs"
    echo.
    call :log_info "Press Ctrl+C to stop all services"
    
    REM Keep the script running
    pause >nul
    
) else if "%command%"=="stop" (
    call :stop_services
    
) else if "%command%"=="restart" (
    call :stop_services
    timeout /t 2 /nobreak >nul
    call :main start
    
) else if "%command%"=="status" (
    call :log_info "Checking service status..."
    call :check_port %BACKEND_PORT% "Backend"
    if !errorlevel! equ 0 (
        call :log_success "Backend is running on port %BACKEND_PORT%"
    ) else (
        call :log_warning "Backend is not running"
    )
    
    call :check_port %FRONTEND_PORT% "Frontend"
    if !errorlevel! equ 0 (
        call :log_success "Frontend is running on port %FRONTEND_PORT%"
    ) else (
        call :log_warning "Frontend is not running"
    )
    
) else (
    echo Usage: %0 {start^|stop^|restart^|status}
    echo.
    echo Commands:
    echo   start   - Start both backend and frontend services
    echo   stop    - Stop all services
    echo   restart - Restart all services
    echo   status  - Check service status
    exit /b 1
)

exit /b 0

REM Call main function with all arguments
call :main %*
