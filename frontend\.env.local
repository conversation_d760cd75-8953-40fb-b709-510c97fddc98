###############################################
# Next.js Frontend Environment Configuration   #
###############################################

# API Configuration
NEXT_PUBLIC_API_URL=http://127.0.0.1:8000

# Development Settings
NEXT_PUBLIC_DEBUG=true 

# Theme Settings
NEXT_PUBLIC_THEME=system

# Authentication Settings
NEXT_PUBLIC_AUTH_ENABLED=true
NEXT_PUBLIC_AUTH_URL=http://127.0.0.1:8000/api/auth
NEXT_PUBLIC_AUTH_CLIENT_ID=your-client-id
NEXT_PUBLIC_AUTH_CLIENT_SECRET=your-client-secret

# Automatic idle logout period (minutes)
# When the user is inactive for this many minutes, they will be logged out automatically.
NEXT_PUBLIC_IDLE_LOGOUT_MINUTES=30

# OAuth Provider Configuration
NEXT_PUBLIC_GOOGLE_CLIENT_ID=800677877142-o0ph7a2nv3bg4q6bus0599f7vbd61vig.apps.googleusercontent.com
NEXT_PUBLIC_GITHUB_CLIENT_ID=Ov23liz09XT3FAXNJixQ

# OAuth callback URLs (for reference)
NEXT_PUBLIC_OAUTH_GOOGLE_CALLBACK_URL=http://127.0.0.1:3000/auth/oauth/google/callback
NEXT_PUBLIC_OAUTH_GITHUB_CALLBACK_URL=http://127.0.0.1:3000/auth/oauth/github/callback