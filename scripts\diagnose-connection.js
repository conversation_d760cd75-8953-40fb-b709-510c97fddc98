#!/usr/bin/env node

/**
 * Connection Diagnostic Script
 * 
 * This script diagnoses and fixes common connection issues between
 * the frontend and backend services.
 */

const axios = require('axios');
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:8000';
const FRONTEND_URL = process.env.FRONTEND_URL || 'http://localhost:3000';
const API_ENDPOINT = `${BACKEND_URL}/api/v1`;

// Colors for console output
const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logError(message) {
    log(`❌ ${message}`, 'red');
}

function logSuccess(message) {
    log(`✅ ${message}`, 'green');
}

function logWarning(message) {
    log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
    log(`ℹ️  ${message}`, 'blue');
}

// Check if a service is running on a specific port
async function checkPort(port, serviceName) {
    try {
        const response = await axios.get(`http://localhost:${port}`, {
            timeout: 5000,
            validateStatus: () => true // Accept any status code
        });
        logSuccess(`${serviceName} is running on port ${port}`);
        return true;
    } catch (error) {
        if (error.code === 'ECONNREFUSED') {
            logError(`${serviceName} is not running on port ${port}`);
        } else {
            logWarning(`${serviceName} on port ${port}: ${error.message}`);
        }
        return false;
    }
}

// Check backend health endpoint
async function checkBackendHealth() {
    try {
        const response = await axios.get(`${API_ENDPOINT}/health`, {
            timeout: 10000
        });
        logSuccess(`Backend health check passed: ${response.status}`);
        logInfo(`Backend response: ${JSON.stringify(response.data, null, 2)}`);
        return true;
    } catch (error) {
        logError(`Backend health check failed: ${error.message}`);
        if (error.response) {
            logError(`Status: ${error.response.status}`);
            logError(`Data: ${JSON.stringify(error.response.data, null, 2)}`);
        }
        return false;
    }
}

// Test authentication endpoint
async function testAuthEndpoint() {
    try {
        const response = await axios.post(`${API_ENDPOINT}/auth/login`, {
            email: '<EMAIL>',
            password: 'testpassword'
        }, {
            timeout: 10000,
            validateStatus: () => true // Accept any status code
        });
        
        if (response.status === 422 || response.status === 401) {
            logSuccess(`Auth endpoint is accessible (expected validation error)`);
            return true;
        } else {
            logInfo(`Auth endpoint response: ${response.status} - ${JSON.stringify(response.data, null, 2)}`);
            return true;
        }
    } catch (error) {
        logError(`Auth endpoint test failed: ${error.message}`);
        return false;
    }
}

// Check CORS configuration
async function checkCORS() {
    try {
        const response = await axios.options(`${API_ENDPOINT}/auth/login`, {
            headers: {
                'Origin': FRONTEND_URL,
                'Access-Control-Request-Method': 'POST',
                'Access-Control-Request-Headers': 'Content-Type'
            },
            timeout: 5000
        });
        
        const corsHeaders = {
            'Access-Control-Allow-Origin': response.headers['access-control-allow-origin'],
            'Access-Control-Allow-Methods': response.headers['access-control-allow-methods'],
            'Access-Control-Allow-Headers': response.headers['access-control-allow-headers']
        };
        
        logSuccess('CORS preflight check passed');
        logInfo(`CORS headers: ${JSON.stringify(corsHeaders, null, 2)}`);
        return true;
    } catch (error) {
        logError(`CORS check failed: ${error.message}`);
        return false;
    }
}

// Check environment configuration
function checkEnvironmentConfig() {
    logInfo('Checking environment configuration...');
    
    // Check frontend .env.local
    const frontendEnvPath = path.join(process.cwd(), 'frontend', '.env.local');
    if (fs.existsSync(frontendEnvPath)) {
        const envContent = fs.readFileSync(frontendEnvPath, 'utf8');
        const apiUrl = envContent.match(/NEXT_PUBLIC_API_URL=(.+)/);
        if (apiUrl) {
            logSuccess(`Frontend API URL configured: ${apiUrl[1]}`);
        } else {
            logError('NEXT_PUBLIC_API_URL not found in frontend/.env.local');
        }
    } else {
        logError('frontend/.env.local file not found');
    }
    
    // Check backend .env
    const backendEnvPath = path.join(process.cwd(), 'backend', '.env');
    if (fs.existsSync(backendEnvPath)) {
        logSuccess('Backend .env file found');
    } else {
        logWarning('backend/.env file not found (using defaults)');
    }
}

// Start backend service
function startBackend() {
    return new Promise((resolve) => {
        logInfo('Starting backend service...');
        const backend = spawn('python', ['main.py'], {
            cwd: path.join(process.cwd(), 'backend'),
            stdio: 'pipe'
        });
        
        backend.stdout.on('data', (data) => {
            const output = data.toString();
            if (output.includes('Uvicorn running on')) {
                logSuccess('Backend started successfully');
                resolve(true);
            }
        });
        
        backend.stderr.on('data', (data) => {
            logError(`Backend error: ${data.toString()}`);
        });
        
        // Timeout after 30 seconds
        setTimeout(() => {
            logWarning('Backend startup timeout');
            resolve(false);
        }, 30000);
    });
}

// Start frontend service
function startFrontend() {
    return new Promise((resolve) => {
        logInfo('Starting frontend service...');
        const frontend = spawn('npm', ['run', 'dev'], {
            cwd: path.join(process.cwd(), 'frontend'),
            stdio: 'pipe'
        });
        
        frontend.stdout.on('data', (data) => {
            const output = data.toString();
            if (output.includes('Ready') || output.includes('Local:')) {
                logSuccess('Frontend started successfully');
                resolve(true);
            }
        });
        
        frontend.stderr.on('data', (data) => {
            const output = data.toString();
            if (output.includes('Ready') || output.includes('Local:')) {
                logSuccess('Frontend started successfully');
                resolve(true);
            } else {
                logError(`Frontend error: ${output}`);
            }
        });
        
        // Timeout after 30 seconds
        setTimeout(() => {
            logWarning('Frontend startup timeout');
            resolve(false);
        }, 30000);
    });
}

// Main diagnostic function
async function runDiagnostics() {
    log('🔍 Starting Connection Diagnostics...', 'blue');
    log('=====================================', 'blue');
    
    // 1. Check environment configuration
    checkEnvironmentConfig();
    
    // 2. Check if services are running
    const backendRunning = await checkPort(8000, 'Backend');
    const frontendRunning = await checkPort(3000, 'Frontend');
    
    // 3. If backend is not running, try to start it
    if (!backendRunning) {
        logWarning('Backend is not running. Attempting to start...');
        const started = await startBackend();
        if (!started) {
            logError('Failed to start backend automatically');
            return false;
        }
        // Wait a moment for the backend to fully start
        await new Promise(resolve => setTimeout(resolve, 5000));
    }
    
    // 4. Check backend health
    const healthOk = await checkBackendHealth();
    if (!healthOk) {
        logError('Backend health check failed');
        return false;
    }
    
    // 5. Test authentication endpoint
    const authOk = await testAuthEndpoint();
    if (!authOk) {
        logError('Authentication endpoint test failed');
        return false;
    }
    
    // 6. Check CORS configuration
    const corsOk = await checkCORS();
    if (!corsOk) {
        logWarning('CORS check failed - this might cause frontend issues');
    }
    
    // 7. If frontend is not running, try to start it
    if (!frontendRunning) {
        logWarning('Frontend is not running. Attempting to start...');
        const started = await startFrontend();
        if (!started) {
            logError('Failed to start frontend automatically');
            return false;
        }
    }
    
    log('=====================================', 'green');
    logSuccess('Diagnostics completed successfully!');
    logInfo('You can now try logging in again.');
    
    return true;
}

// Run diagnostics if this script is executed directly
if (require.main === module) {
    runDiagnostics().catch(error => {
        logError(`Diagnostic script failed: ${error.message}`);
        process.exit(1);
    });
}

module.exports = { runDiagnostics };
