"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(client)/layout",{

/***/ "(app-pages-browser)/./src/lib/api-client.ts":
/*!*******************************!*\
  !*** ./src/lib/api-client.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   apiClientInstance: () => (/* binding */ apiClientInstance),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\n// API Configuration\nconst API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\nconst API_VERSION = '/api/v1';\n// Debug logging for API configuration\nif (true) {\n    console.log('API Client Configuration:', {\n        API_BASE_URL,\n        API_VERSION,\n        FULL_URL: \"\".concat(API_BASE_URL).concat(API_VERSION),\n        ENV_VAR: \"http://127.0.0.1:8000\"\n    });\n}\n// Cache configuration\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds\nconst CACHE_PREFIX = 'api_cache_';\n// Cache utility functions\nclass CacheManager {\n    static getInstance() {\n        if (!CacheManager.instance) {\n            CacheManager.instance = new CacheManager();\n        }\n        return CacheManager.instance;\n    }\n    set(key, data) {\n        let duration = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : CACHE_DURATION;\n        const expiresAt = Date.now() + duration;\n        this.cache.set(key, {\n            data,\n            timestamp: Date.now(),\n            expiresAt\n        });\n        // Also store in localStorage for persistence across page reloads\n        if (true) {\n            try {\n                localStorage.setItem(\"\".concat(CACHE_PREFIX).concat(key), JSON.stringify({\n                    data,\n                    timestamp: Date.now(),\n                    expiresAt\n                }));\n            } catch (error) {\n                console.warn('Failed to store cache in localStorage:', error);\n            }\n        }\n    }\n    get(key) {\n        // Check memory cache first\n        const memoryEntry = this.cache.get(key);\n        if (memoryEntry && memoryEntry.expiresAt > Date.now()) {\n            return memoryEntry.data;\n        }\n        // Check localStorage\n        if (true) {\n            try {\n                const stored = localStorage.getItem(\"\".concat(CACHE_PREFIX).concat(key));\n                if (stored) {\n                    const entry = JSON.parse(stored);\n                    if (entry.expiresAt > Date.now()) {\n                        // Update memory cache\n                        this.cache.set(key, entry);\n                        return entry.data;\n                    } else {\n                        // Remove expired entry\n                        localStorage.removeItem(\"\".concat(CACHE_PREFIX).concat(key));\n                    }\n                }\n            } catch (error) {\n                console.warn('Failed to retrieve cache from localStorage:', error);\n            }\n        }\n        return null;\n    }\n    delete(key) {\n        this.cache.delete(key);\n        if (true) {\n            localStorage.removeItem(\"\".concat(CACHE_PREFIX).concat(key));\n        }\n    }\n    clear(pattern) {\n        if (pattern) {\n            // Clear specific pattern\n            for (const key of this.cache.keys()){\n                if (key.includes(pattern)) {\n                    this.cache.delete(key);\n                }\n            }\n            if (true) {\n                for(let i = localStorage.length - 1; i >= 0; i--){\n                    const key = localStorage.key(i);\n                    if (key && key.startsWith(CACHE_PREFIX) && key.includes(pattern)) {\n                        localStorage.removeItem(key);\n                    }\n                }\n            }\n        } else {\n            // Clear all cache\n            this.cache.clear();\n            if (true) {\n                for(let i = localStorage.length - 1; i >= 0; i--){\n                    const key = localStorage.key(i);\n                    if (key && key.startsWith(CACHE_PREFIX)) {\n                        localStorage.removeItem(key);\n                    }\n                }\n            }\n        }\n    }\n    generateKey(config) {\n        const { method, url, params, data } = config;\n        return \"\".concat(method === null || method === void 0 ? void 0 : method.toUpperCase(), \"_\").concat(url, \"_\").concat(JSON.stringify(params), \"_\").concat(JSON.stringify(data));\n    }\n    constructor(){\n        this.cache = new Map();\n    }\n}\n// Create axios instance\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: \"\".concat(API_BASE_URL).concat(API_VERSION),\n    timeout: 30000,\n    withCredentials: false,\n    headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n    }\n});\n// Request interceptor to add auth token and debug logging\napiClient.interceptors.request.use((config)=>{\n    // Debug logging\n    if (true) {\n        var _config_method;\n        console.log('API Request:', {\n            method: (_config_method = config.method) === null || _config_method === void 0 ? void 0 : _config_method.toUpperCase(),\n            url: config.url,\n            baseURL: config.baseURL,\n            fullURL: \"\".concat(config.baseURL).concat(config.url),\n            headers: config.headers,\n            data: config.data\n        });\n    }\n    // Get token from localStorage or cookies\n    const token =  true ? localStorage.getItem('access_token') : 0;\n    if (token) {\n        config.headers.Authorization = \"Bearer \".concat(token);\n    }\n    return config;\n}, (error)=>{\n    console.error('API Request Error:', error);\n    return Promise.reject(error);\n});\n// Response interceptor for token refresh and error handling\napiClient.interceptors.response.use((response)=>{\n    // Debug logging\n    if (true) {\n        console.log('API Response:', {\n            status: response.status,\n            statusText: response.statusText,\n            url: response.config.url,\n            data: response.data\n        });\n    }\n    return response;\n}, async (error)=>{\n    var _error_response;\n    const originalRequest = error.config;\n    // Handle 401 Unauthorized - try to refresh token\n    if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        try {\n            const refreshToken =  true ? localStorage.getItem('refresh_token') : 0;\n            if (refreshToken) {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(API_BASE_URL).concat(API_VERSION, \"/auth/refresh\"), {\n                    refresh_token: refreshToken\n                });\n                const { access_token, refresh_token } = response.data;\n                // Store new tokens\n                if (true) {\n                    localStorage.setItem('access_token', access_token);\n                    localStorage.setItem('refresh_token', refresh_token);\n                }\n                // Retry original request with new token\n                originalRequest.headers.Authorization = \"Bearer \".concat(access_token);\n                return apiClient(originalRequest);\n            }\n        } catch (e) {\n            // Refresh failed - clear tokens and redirect to login\n            if (true) {\n                localStorage.removeItem('access_token');\n                localStorage.removeItem('refresh_token');\n                window.location.href = '/login';\n            }\n        }\n    }\n    return Promise.reject(error);\n});\n// API Client class with typed methods and caching\nclass ApiClient {\n    // Generic request method with caching\n    async request(config) {\n        let useCache = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false, cacheDuration = arguments.length > 2 ? arguments[2] : void 0;\n        try {\n            var _config_method, _config_method1;\n            // Check cache for GET requests\n            if (useCache && ((_config_method = config.method) === null || _config_method === void 0 ? void 0 : _config_method.toLowerCase()) === 'get') {\n                const cacheKey = this.cache.generateKey(config);\n                const cachedData = this.cache.get(cacheKey);\n                if (cachedData) {\n                    return cachedData;\n                }\n            }\n            const response = await this.client.request(config);\n            // Cache successful GET responses\n            if (useCache && ((_config_method1 = config.method) === null || _config_method1 === void 0 ? void 0 : _config_method1.toLowerCase()) === 'get' && response.status === 200) {\n                const cacheKey = this.cache.generateKey(config);\n                this.cache.set(cacheKey, response.data, cacheDuration);\n            }\n            return response.data;\n        } catch (error) {\n            throw this.handleError(error);\n        }\n    }\n    // Cached GET request\n    async get(url, params) {\n        let useCache = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true, cacheDuration = arguments.length > 3 ? arguments[3] : void 0;\n        return this.request({\n            method: 'GET',\n            url,\n            params\n        }, useCache, cacheDuration);\n    }\n    // POST request (no caching)\n    async post(url, data) {\n        return this.request({\n            method: 'POST',\n            url,\n            data\n        }, false);\n    }\n    // PUT request (no caching)\n    async put(url, data) {\n        return this.request({\n            method: 'PUT',\n            url,\n            data\n        }, false);\n    }\n    // DELETE request (no caching)\n    async delete(url) {\n        return this.request({\n            method: 'DELETE',\n            url\n        }, false);\n    }\n    // Clear cache\n    clearCache(pattern) {\n        this.cache.clear(pattern);\n    }\n    // Test connection to backend\n    async testConnection() {\n        try {\n            console.log('Testing connection to:', \"\".concat(this.client.defaults.baseURL, \"/health\"));\n            const response = await this.client.get('/health', {\n                timeout: 5000,\n                validateStatus: ()=>true // Accept any status code\n            });\n            if (response.status === 200) {\n                return {\n                    success: true,\n                    message: 'Backend connection successful',\n                    details: response.data\n                };\n            } else {\n                return {\n                    success: false,\n                    message: \"Backend responded with status \".concat(response.status),\n                    details: response.data\n                };\n            }\n        } catch (error) {\n            console.error('Connection test failed:', error);\n            if (error.code === 'ECONNREFUSED') {\n                return {\n                    success: false,\n                    message: 'Backend server is not running. Please start the backend server on http://localhost:8000',\n                    details: {\n                        error: error.code,\n                        message: error.message\n                    }\n                };\n            } else if (error.code === 'ENOTFOUND') {\n                return {\n                    success: false,\n                    message: 'Backend server not found. Please check the API URL configuration.',\n                    details: {\n                        error: error.code,\n                        message: error.message\n                    }\n                };\n            } else {\n                return {\n                    success: false,\n                    message: \"Connection failed: \".concat(error.message),\n                    details: {\n                        error: error.code,\n                        message: error.message\n                    }\n                };\n            }\n        }\n    }\n    // Handle API errors with enhanced diagnostics\n    handleError(error) {\n        console.error('API Client Error:', error);\n        if (error && typeof error === 'object' && 'response' in error) {\n            // Server responded with error status\n            const { status, data } = error.response;\n            // Prefer server-provided detail when available so the UI can show precise feedback\n            const serverMessage = data === null || data === void 0 ? void 0 : data.detail;\n            switch(status){\n                case 400:\n                    return new Error(serverMessage || 'Bad request');\n                case 401:\n                    // Pass through messages such as \"Account is locked\" when supplied\n                    return new Error(serverMessage || 'Unauthorized - please login again');\n                case 403:\n                    return new Error(serverMessage || 'Access denied - insufficient permissions');\n                case 404:\n                    return new Error(serverMessage || 'Resource not found');\n                case 422:\n                    return new Error(serverMessage || 'Validation error');\n                case 429:\n                    return new Error(serverMessage || 'Too many requests - please try again later');\n                case 500:\n                    return new Error(serverMessage || 'Internal server error');\n                default:\n                    return new Error(serverMessage || 'An error occurred');\n            }\n        } else if (error && typeof error === 'object' && 'request' in error) {\n            var _axiosError_config, _axiosError_config1, _axiosError_config2, _axiosError_config3;\n            // Network error - provide more detailed diagnostics\n            const axiosError = error;\n            console.error('Network Error Details:', {\n                code: axiosError.code,\n                message: axiosError.message,\n                config: {\n                    url: (_axiosError_config = axiosError.config) === null || _axiosError_config === void 0 ? void 0 : _axiosError_config.url,\n                    method: (_axiosError_config1 = axiosError.config) === null || _axiosError_config1 === void 0 ? void 0 : _axiosError_config1.method,\n                    baseURL: (_axiosError_config2 = axiosError.config) === null || _axiosError_config2 === void 0 ? void 0 : _axiosError_config2.baseURL,\n                    timeout: (_axiosError_config3 = axiosError.config) === null || _axiosError_config3 === void 0 ? void 0 : _axiosError_config3.timeout\n                }\n            });\n            // Provide specific error messages based on error code\n            if (axiosError.code === 'ECONNREFUSED') {\n                return new Error('Connection refused - Backend server may not be running. Please check if the server is started on http://localhost:8000');\n            } else if (axiosError.code === 'ENOTFOUND') {\n                return new Error('Server not found - Please check the API URL configuration');\n            } else if (axiosError.code === 'ETIMEDOUT') {\n                return new Error('Request timeout - Server is taking too long to respond');\n            } else if (axiosError.code === 'ECONNABORTED') {\n                return new Error('Request aborted - Connection was terminated');\n            } else {\n                return new Error(\"Network error (\".concat(axiosError.code || 'UNKNOWN', \") - Please check your connection and ensure the backend server is running\"));\n            }\n        } else {\n            // Other error\n            const message = error && typeof error === 'object' && 'message' in error ? String(error.message) : 'An unexpected error occurred';\n            return new Error(message);\n        }\n    }\n    // Token management\n    setTokens(accessToken, refreshToken) {\n        if (true) {\n            localStorage.setItem('access_token', accessToken);\n            localStorage.setItem('refresh_token', refreshToken);\n        }\n    }\n    clearTokens() {\n        if (true) {\n            localStorage.removeItem('access_token');\n            localStorage.removeItem('refresh_token');\n        }\n    }\n    getAccessToken() {\n        if (true) {\n            return localStorage.getItem('access_token');\n        }\n        return null;\n    }\n    getRefreshToken() {\n        if (true) {\n            return localStorage.getItem('refresh_token');\n        }\n        return null;\n    }\n    constructor(){\n        this.client = apiClient;\n        this.cache = CacheManager.getInstance();\n    }\n}\n// Export singleton instance\nconst apiClientInstance = new ApiClient();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClientInstance);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api-client.ts\n"));

/***/ })

});