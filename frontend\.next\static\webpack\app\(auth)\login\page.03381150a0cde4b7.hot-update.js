"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(auth)/login/page",{

/***/ "(app-pages-browser)/./src/contexts/auth-context.tsx":
/*!***************************************!*\
  !*** ./src/contexts/auth-context.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthContext: () => (/* binding */ AuthContext),\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth-service */ \"(app-pages-browser)/./src/lib/auth-service.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,AuthContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n// Create context\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nvar _process_env_NEXT_PUBLIC_IDLE_LOGOUT_MINUTES;\n// Idle logout timeout – configurable via env var, defaults to 30 minutes\nconst IDLE_LOGOUT_MINUTES = parseInt((_process_env_NEXT_PUBLIC_IDLE_LOGOUT_MINUTES = \"30\") !== null && _process_env_NEXT_PUBLIC_IDLE_LOGOUT_MINUTES !== void 0 ? _process_env_NEXT_PUBLIC_IDLE_LOGOUT_MINUTES : \"30\", 10);\nconst IDLE_LOGOUT_MS = IDLE_LOGOUT_MINUTES * 60000;\n// Authentication Provider Component\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // State\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userFromToken, setUserFromToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Reactive authentication state\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // -------------------\n    // Proactive token refresh + idle logout\n    // -------------------\n    const refreshTimeoutRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(null);\n    const idleTimeoutRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(null);\n    const clearTimers = ()=>{\n        if (refreshTimeoutRef.current) {\n            clearTimeout(refreshTimeoutRef.current);\n            refreshTimeoutRef.current = null;\n        }\n        if (idleTimeoutRef.current) {\n            clearTimeout(idleTimeoutRef.current);\n            idleTimeoutRef.current = null;\n        }\n    };\n    // Logout function (moved here so it's defined before use in other hooks)\n    const logout = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"AuthProvider.useCallback[logout]\": async function(sessionId) {\n            let logoutAll = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            // Ensure any scheduled tasks are cancelled BEFORE state updates\n            clearTimers();\n            // Clear authentication state synchronously so UI can react immediately\n            setIsAuthenticated(false);\n            setUser(null);\n            setUserFromToken(null);\n            // Persisted auth data\n            _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.clearAuth();\n            // Attempt server-side logout *after* local cleanup so we don't block UI\n            const shouldCallApiLogout = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isAuthenticated() && !_lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isTokenExpired();\n            if (shouldCallApiLogout) {\n                _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.logout(sessionId, logoutAll).catch(console.warn);\n            }\n            // Use Next.js router navigation rather than full page reload for smoother UX\n            router.replace(\"/login\");\n        }\n    }[\"AuthProvider.useCallback[logout]\"], [\n        router\n    ]);\n    const scheduleTokenRefresh = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"AuthProvider.useCallback[scheduleTokenRefresh]\": ()=>{\n            if (!_lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isAuthenticated()) return;\n            const expDate = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getTokenExpiration();\n            if (!expDate) return;\n            const now = new Date();\n            // Refresh 60 seconds before expiry (or immediately if already <1 min)\n            const msUntilRefresh = Math.max(expDate.getTime() - now.getTime() - 60000, 0);\n            if (refreshTimeoutRef.current) {\n                clearTimeout(refreshTimeoutRef.current);\n            }\n            refreshTimeoutRef.current = setTimeout({\n                \"AuthProvider.useCallback[scheduleTokenRefresh]\": async ()=>{\n                    try {\n                        await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.refreshToken();\n                        scheduleTokenRefresh(); // reschedule for next expiry\n                    } catch (err) {\n                        console.error(\"Token refresh failed\", err);\n                        await logout();\n                    }\n                }\n            }[\"AuthProvider.useCallback[scheduleTokenRefresh]\"], msUntilRefresh);\n        }\n    }[\"AuthProvider.useCallback[scheduleTokenRefresh]\"], [\n        logout\n    ]);\n    const resetIdleTimer = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"AuthProvider.useCallback[resetIdleTimer]\": ()=>{\n            if (idleTimeoutRef.current) {\n                clearTimeout(idleTimeoutRef.current);\n            }\n            idleTimeoutRef.current = setTimeout({\n                \"AuthProvider.useCallback[resetIdleTimer]\": ()=>{\n                    logout();\n                }\n            }[\"AuthProvider.useCallback[resetIdleTimer]\"], IDLE_LOGOUT_MS);\n        }\n    }[\"AuthProvider.useCallback[resetIdleTimer]\"], [\n        logout\n    ]);\n    // ----------------------------------------------\n    // Setup timers only when authentication *state* changes\n    // ----------------------------------------------\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!isAuthenticated) {\n                // User just logged out – ensure timers are cleared\n                clearTimers();\n                return;\n            }\n            // User is authenticated – start / resume timers **once**\n            scheduleTokenRefresh();\n            resetIdleTimer();\n            // Attach user-activity listeners to reset the idle timer\n            const events = [\n                \"mousemove\",\n                \"keydown\",\n                \"click\",\n                // Removing programmatic scroll events avoids accidental resets\n                //'scroll',\n                \"touchstart\"\n            ];\n            events.forEach({\n                \"AuthProvider.useEffect\": (e)=>window.addEventListener(e, resetIdleTimer)\n            }[\"AuthProvider.useEffect\"]);\n            // Cleanup when component unmounts or auth state changes\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    events.forEach({\n                        \"AuthProvider.useEffect\": (e)=>window.removeEventListener(e, resetIdleTimer)\n                    }[\"AuthProvider.useEffect\"]);\n                    clearTimers();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        isAuthenticated,\n        scheduleTokenRefresh,\n        resetIdleTimer\n    ]);\n    // Initialize authentication state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initializeAuth = {\n                \"AuthProvider.useEffect.initializeAuth\": async function() {\n                    let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n                    try {\n                        setIsLoading(true);\n                        // Check if user is authenticated from token\n                        if (_lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isAuthenticated()) {\n                            const tokenUser = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUserFromToken();\n                            setUserFromToken(tokenUser);\n                            // Test backend connectivity before trying to fetch profile\n                            try {\n                                // Quick health check to verify backend is accessible\n                                await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.testConnection();\n                            } catch (connectionError) {\n                                console.warn(\"Backend not accessible during auth init, skipping profile fetch\");\n                                // Still set token-based auth, user can retry later\n                                return;\n                            }\n                            // Try to fetch full user profile\n                            try {\n                                const userProfile = await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUser();\n                                setUser(userProfile);\n                            } catch (error) {\n                                console.warn(\"Failed to fetch user profile during initialization:\", error);\n                                // Handle different types of errors\n                                if (error && typeof error === \"object\") {\n                                    // Check for HTTP response errors\n                                    if (\"response\" in error) {\n                                        var _error_response;\n                                        const status = (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status;\n                                        if (status === 401 || status === 403) {\n                                            // Invalid token - clear auth\n                                            console.log(\"Token is invalid, clearing auth\");\n                                            _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.clearAuth();\n                                            setUser(null);\n                                            setUserFromToken(null);\n                                        }\n                                    // For other HTTP errors (404, 500, etc.), keep token-based auth\n                                    } else if (\"message\" in error) {\n                                        const errorMessage = error.message;\n                                        if (errorMessage.includes(\"Network Error\") || errorMessage.includes(\"ECONNREFUSED\") || errorMessage.includes(\"ENOTFOUND\")) {\n                                            // Network connectivity issues - don't clear auth, user can retry later\n                                            console.warn(\"Network error during profile fetch, keeping token-based auth\");\n                                        } else {\n                                            // Other errors - log but don't clear auth\n                                            console.warn(\"Unknown error during profile fetch:\", errorMessage);\n                                        }\n                                    }\n                                }\n                            }\n                        } else {\n                            // Clear any stale data\n                            _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.clearAuth();\n                            setUser(null);\n                            setUserFromToken(null);\n                        }\n                    } catch (error) {\n                        console.error(\"Auth initialization error:\", error);\n                        // Check if it's a network error and we should retry\n                        if (retryCount < 2 && error && typeof error === \"object\" && \"message\" in error) {\n                            const errorMessage = error.message;\n                            if (errorMessage.includes(\"Network Error\") || errorMessage.includes(\"ECONNREFUSED\") || errorMessage.includes(\"ENOTFOUND\")) {\n                                console.log(\"Network error during auth init, retrying... (\".concat(retryCount + 1, \"/3)\"));\n                                setTimeout({\n                                    \"AuthProvider.useEffect.initializeAuth\": ()=>initializeAuth(retryCount + 1)\n                                }[\"AuthProvider.useEffect.initializeAuth\"], 2000); // Retry after 2 seconds\n                                return;\n                            }\n                        }\n                        // If not a network error or max retries reached, clear auth\n                        _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.clearAuth();\n                        setUser(null);\n                        setUserFromToken(null);\n                    } finally{\n                        setIsLoading(false);\n                        setIsInitialized(true);\n                    }\n                }\n            }[\"AuthProvider.useEffect.initializeAuth\"];\n            initializeAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Refresh user data\n    const refreshUser = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[refreshUser]\": async ()=>{\n            try {\n                // Debug statements removed for production\n                // Force check auth state first\n                const currentAuthState = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isAuthenticated();\n                setIsAuthenticated(currentAuthState);\n                if (!currentAuthState) {\n                    //\n                    return;\n                }\n                // Try to get user profile, but don't fail if it doesn't work\n                try {\n                    const userProfile = await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUser();\n                    setUser(userProfile);\n                } catch (profileError) {\n                    console.warn(\"Failed to refresh user profile:\", profileError);\n                // Keep existing user data if profile fetch fails\n                }\n                // Update token user as well\n                const tokenUser = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUserFromToken();\n                setUserFromToken(tokenUser);\n            //\n            } catch (error) {\n                var _error_response;\n                console.error(\"Failed to refresh user:\", error);\n                console.error(\"Error details:\", {\n                    message: error instanceof Error ? error.message : \"Unknown error\",\n                    status: error && typeof error === \"object\" && \"response\" in error ? (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status : \"No status\"\n                });\n                // If refresh fails, user might be logged out\n                logout();\n            }\n        }\n    }[\"AuthProvider.useCallback[refreshUser]\"], [\n        logout\n    ]);\n    // Login function\n    const login = async (credentials)=>{\n        try {\n            setIsLoading(true);\n            await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.login(credentials);\n            // Get user profile - handle gracefully if it fails\n            try {\n                const userProfile = await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUser();\n                setUser(userProfile);\n            } catch (profileError) {\n                console.warn(\"Failed to get user profile after login:\", profileError);\n            // Don't fail the login if profile fetch fails\n            // The user can still access the app with token-based auth\n            }\n            const tokenUser = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getCurrentUserFromToken();\n            setUserFromToken(tokenUser);\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Login successful!\");\n            // Redirect to dashboard - role-based rendering will be handled by the dashboard page\n            router.push(\"/dashboard\");\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            const errorMessage = error instanceof Error ? error.message : \"Login failed\";\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(errorMessage);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Register function\n    const register = async (userData)=>{\n        try {\n            setIsLoading(true);\n            const response = await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.register(userData);\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success(response.message || \"Registration successful! Please check your email to verify your account.\");\n            // Redirect to login page\n            router.push(\"/login\");\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            const errorMessage = error instanceof Error ? error.message : \"Registration failed\";\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(errorMessage);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Clear authentication data\n    const clearAuth = ()=>{\n        setUser(null);\n        setUserFromToken(null);\n        _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.clearAuth();\n    };\n    // Check if user has OAuth provider linked\n    const hasOAuthProvider = (provider)=>{\n        return _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.hasOAuthProvider(provider);\n    };\n    // Get OAuth providers list\n    const getOAuthProviders = async ()=>{\n        try {\n            return await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.getOAuthProviders();\n        } catch (error) {\n            console.error(\"Failed to get OAuth providers:\", error);\n            return {};\n        }\n    };\n    // Update authentication state when tokens change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const updateAuthState = {\n                \"AuthProvider.useEffect.updateAuthState\": ()=>{\n                    const authState = _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.authService.isAuthenticated();\n                    //\n                    setIsAuthenticated(authState);\n                }\n            }[\"AuthProvider.useEffect.updateAuthState\"];\n            // Initial check\n            updateAuthState();\n            // Listen for storage changes (when tokens are added/removed)\n            const handleStorageChange = {\n                \"AuthProvider.useEffect.handleStorageChange\": (e)=>{\n                    if (e.key === \"access_token\" || e.key === \"refresh_token\") {\n                        //\n                        updateAuthState();\n                    }\n                }\n            }[\"AuthProvider.useEffect.handleStorageChange\"];\n            window.addEventListener(\"storage\", handleStorageChange);\n            // Also check periodically for token expiration\n            const interval = setInterval(updateAuthState, 5000);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    window.removeEventListener(\"storage\", handleStorageChange);\n                    clearInterval(interval);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Context value\n    const value = {\n        user,\n        userFromToken,\n        isAuthenticated,\n        isLoading,\n        isInitialized,\n        login,\n        register,\n        logout,\n        refreshUser,\n        clearAuth,\n        hasOAuthProvider,\n        getOAuthProviders\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\contexts\\\\auth-context.tsx\",\n        lineNumber: 468,\n        columnNumber: 10\n    }, this);\n}\n_s(AuthProvider, \"vHRPUYhL0MK6zoFbLb0c6cGc8Pg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthProvider;\n// Custom hook to use auth context\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Export the context for advanced usage\n\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/auth-context.tsx\n"));

/***/ })

});