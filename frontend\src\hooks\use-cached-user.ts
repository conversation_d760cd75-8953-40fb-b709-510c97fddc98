import { useState, useEffect, useCallback } from 'react';
import { apiClientInstance } from '@/lib/api-client';

// User profile interface
interface UserProfile {
    id: string;
    email: string;
    username: string;
    first_name?: string;
    last_name?: string;
    full_name?: string;
    is_active: boolean;
    is_verified: boolean;
    role: string;
}

// Cache configuration
const USER_CACHE_KEY = 'user_profile';
const USER_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Cache utility functions
class UserCache {
    private static instance: UserCache;

    static getInstance(): UserCache {
        if (!UserCache.instance) {
            UserCache.instance = new UserCache();
        }
        return UserCache.instance;
    }

    setUser(user: UserProfile): void {
        if (typeof window !== 'undefined') {
            const cacheData = {
                data: user,
                timestamp: Date.now(),
                expiresAt: Date.now() + USER_CACHE_DURATION
            };
            localStorage.setItem(USER_CACHE_KEY, JSON.stringify(cacheData));
        }
    }

    getUser(): UserProfile | null {
        if (typeof window !== 'undefined') {
            try {
                const stored = localStorage.getItem(USER_CACHE_KEY);
                if (stored) {
                    const cacheData = JSON.parse(stored);
                    if (cacheData.expiresAt > Date.now()) {
                        return cacheData.data;
                    } else {
                        // Remove expired cache
                        localStorage.removeItem(USER_CACHE_KEY);
                    }
                }
            } catch (error) {
                console.warn('Failed to retrieve user cache:', error);
                localStorage.removeItem(USER_CACHE_KEY);
            }
        }
        return null;
    }

    clearUser(): void {
        if (typeof window !== 'undefined') {
            localStorage.removeItem(USER_CACHE_KEY);
        }
    }
}

// Hook return type
interface UseCachedUserReturn {
    user: UserProfile | null;
    loading: boolean;
    error: string | null;
    refetch: () => Promise<void>;
    clearCache: () => void;
}

/**
 * Custom hook for managing cached user data
 * Provides automatic caching, loading states, and cache invalidation
 */
export function useCachedUser(): UseCachedUserReturn {
    const [user, setUser] = useState<UserProfile | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    const userCache = UserCache.getInstance();

    // Fetch user data from API
    const fetchUser = useCallback(async (): Promise<void> => {
        try {
            setLoading(true);
            setError(null);

            // Check cache first
            const cachedUser = userCache.getUser();
            if (cachedUser) {
                setUser(cachedUser);
                setLoading(false);
                return;
            }

            // Fetch from API
            const userData = await apiClientInstance.get<UserProfile>('/auth/me', {}, true, USER_CACHE_DURATION);
            
            // Cache the user data
            userCache.setUser(userData);
            setUser(userData);
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to fetch user data';
            setError(errorMessage);
            console.error('Error fetching user data:', err);
        } finally {
            setLoading(false);
        }
    }, [userCache]);

    // Refetch user data (bypass cache)
    const refetch = useCallback(async (): Promise<void> => {
        try {
            setLoading(true);
            setError(null);

            // Clear cache and fetch fresh data
            userCache.clearUser();
            const userData = await apiClientInstance.get<UserProfile>('/auth/me', {}, false);
            
            // Cache the new user data
            userCache.setUser(userData);
            setUser(userData);
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to fetch user data';
            setError(errorMessage);
            console.error('Error refetching user data:', err);
        } finally {
            setLoading(false);
        }
    }, [userCache]);

    // Clear cache
    const clearCache = useCallback((): void => {
        userCache.clearUser();
        setUser(null);
    }, [userCache]);

    // Initialize on mount
    useEffect(() => {
        fetchUser();
    }, [fetchUser]);

    return {
        user,
        loading,
        error,
        refetch,
        clearCache
    };
}

/**
 * Hook for managing user profile updates with cache invalidation
 */
export function useUserProfileUpdate() {
    const { refetch, clearCache } = useCachedUser();

    const updateProfile = useCallback(async (updateData: Partial<UserProfile>): Promise<void> => {
        try {
            // Update profile via API
            await apiClientInstance.put('/auth/profile', updateData);
            
            // Clear cache to force refetch
            clearCache();
            
            // Refetch user data
            await refetch();
        } catch (error) {
            console.error('Error updating user profile:', error);
            throw error;
        }
    }, [refetch, clearCache]);

    return { updateProfile };
}

/**
 * Hook for user logout with cache cleanup
 */
export function useUserLogout() {
    const { clearCache } = useCachedUser();

    const logout = useCallback(async (): Promise<void> => {
        try {
            // Call logout API
            await apiClientInstance.post('/auth/logout');
            
            // Clear all caches
            clearCache();
            apiClientInstance.clearCache();
            
            // Clear tokens
            apiClientInstance.clearTokens();
            
            // Redirect to login
            if (typeof window !== 'undefined') {
                window.location.href = '/login';
            }
        } catch (error) {
            console.error('Error during logout:', error);
            // Still clear cache and tokens even if API call fails
            clearCache();
            apiClientInstance.clearCache();
            apiClientInstance.clearTokens();
            
            if (typeof window !== 'undefined') {
                window.location.href = '/login';
            }
        }
    }, [clearCache]);

    return { logout };
} 