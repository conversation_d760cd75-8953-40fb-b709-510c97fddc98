"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-connection/page",{

/***/ "(app-pages-browser)/./src/components/connection-diagnostic.tsx":
/*!**************************************************!*\
  !*** ./src/components/connection-diagnostic.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectionDiagnostic: () => (/* binding */ ConnectionDiagnostic)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Loader2,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Loader2,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Loader2,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Loader2,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Loader2,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ ConnectionDiagnostic auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n// Hook to handle client-side only values\nfunction useClientSide() {\n    _s();\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useClientSide.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"useClientSide.useEffect\"], []);\n    return isClient;\n}\n_s(useClientSide, \"k460N28PNzD7zo1YW47Q9UigQis=\");\nfunction ConnectionDiagnostic() {\n    var _connectionStatus_details;\n    _s1();\n    const [isRunning, setIsRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const diagnosticTests = [\n        {\n            name: 'Backend Connection',\n            test: 'connection'\n        },\n        {\n            name: 'API Health Check',\n            test: 'health'\n        },\n        {\n            name: 'Authentication Endpoint',\n            test: 'auth'\n        },\n        {\n            name: 'CORS Configuration',\n            test: 'cors'\n        }\n    ];\n    const runDiagnostics = async ()=>{\n        setIsRunning(true);\n        setResults([]);\n        setConnectionStatus(null);\n        // Initialize results with pending status\n        const initialResults = diagnosticTests.map((test)=>({\n                test: test.name,\n                status: 'pending',\n                message: 'Running...'\n            }));\n        setResults(initialResults);\n        try {\n            // Test 1: Basic connection\n            await updateResult('Backend Connection', async ()=>{\n                const result = await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.testConnection();\n                setConnectionStatus(result);\n                if (result.success) {\n                    return {\n                        status: 'success',\n                        message: result.message,\n                        details: result.details\n                    };\n                } else {\n                    return {\n                        status: 'error',\n                        message: result.message,\n                        details: result.details\n                    };\n                }\n            });\n            // Test 2: Health endpoint\n            await updateResult('API Health Check', async ()=>{\n                try {\n                    const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.get('/health');\n                    return {\n                        status: 'success',\n                        message: 'Health endpoint accessible',\n                        details: response\n                    };\n                } catch (error) {\n                    return {\n                        status: 'error',\n                        message: \"Health check failed: \".concat(error.message),\n                        details: error\n                    };\n                }\n            });\n            // Test 3: Auth endpoint (expect validation error)\n            await updateResult('Authentication Endpoint', async ()=>{\n                try {\n                    await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.post('/auth/login', {\n                        email: '<EMAIL>',\n                        password: 'test'\n                    });\n                    return {\n                        status: 'warning',\n                        message: 'Unexpected success - endpoint may have issues'\n                    };\n                } catch (error) {\n                    if (error.message.includes('Validation error') || error.message.includes('Invalid credentials')) {\n                        return {\n                            status: 'success',\n                            message: 'Auth endpoint accessible (expected validation error)',\n                            details: error.message\n                        };\n                    } else {\n                        return {\n                            status: 'error',\n                            message: \"Auth endpoint error: \".concat(error.message),\n                            details: error\n                        };\n                    }\n                }\n            });\n            // Test 4: CORS (simplified check)\n            await updateResult('CORS Configuration', async ()=>{\n                try {\n                    // If we can make requests, CORS is likely configured correctly\n                    await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.get('/health');\n                    return {\n                        status: 'success',\n                        message: 'CORS appears to be configured correctly'\n                    };\n                } catch (error) {\n                    if (error.message.includes('CORS')) {\n                        return {\n                            status: 'error',\n                            message: 'CORS configuration issue detected',\n                            details: error\n                        };\n                    } else {\n                        return {\n                            status: 'warning',\n                            message: 'Unable to verify CORS (other connection issues present)'\n                        };\n                    }\n                }\n            });\n        } catch (error) {\n            console.error('Diagnostic error:', error);\n        } finally{\n            setIsRunning(false);\n        }\n    };\n    const updateResult = async (testName, testFunction)=>{\n        try {\n            const result = await testFunction();\n            setResults((prev)=>prev.map((r)=>r.test === testName ? {\n                        ...r,\n                        ...result\n                    } : r));\n        } catch (error) {\n            setResults((prev)=>prev.map((r)=>r.test === testName ? {\n                        ...r,\n                        status: 'error',\n                        message: \"Test failed: \".concat(error.message),\n                        details: error\n                    } : r));\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'success':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 24\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 24\n                }, this);\n            case 'warning':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 24\n                }, this);\n            case 'pending':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4 animate-spin text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 24\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const variants = {\n            success: 'default',\n            error: 'destructive',\n            warning: 'secondary',\n            pending: 'outline'\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n            variant: variants[status] || 'outline',\n            children: status.toUpperCase()\n        }, void 0, false, {\n            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n            lineNumber: 193,\n            columnNumber: 13\n        }, this);\n    };\n    // Auto-run diagnostics on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConnectionDiagnostic.useEffect\": ()=>{\n            runDiagnostics();\n        }\n    }[\"ConnectionDiagnostic.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: \"w-full max-w-2xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 21\n                            }, this),\n                            \"Connection Diagnostics\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                        children: \"Diagnose connection issues between frontend and backend services\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                lineNumber: 206,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"space-y-4\",\n                children: [\n                    connectionStatus && !connectionStatus.success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                        variant: \"destructive\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.AlertDescription, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Connection Failed:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 29\n                                    }, this),\n                                    \" \",\n                                    connectionStatus.message,\n                                    ((_connectionStatus_details = connectionStatus.details) === null || _connectionStatus_details === void 0 ? void 0 : _connectionStatus_details.error) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 text-sm\",\n                                        children: [\n                                            \"Error Code: \",\n                                            connectionStatus.details.error\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: results.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            getStatusIcon(result.status),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: result.test\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: result.message\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 29\n                                    }, this),\n                                    getStatusBadge(result.status)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 25\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: runDiagnostics,\n                            disabled: isRunning,\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                isRunning ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 29\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 29\n                                }, this),\n                                isRunning ? 'Running Diagnostics...' : 'Run Diagnostics'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 17\n                    }, this),\n                    (connectionStatus === null || connectionStatus === void 0 ? void 0 : connectionStatus.success) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.AlertDescription, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Connection Successful!\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 29\n                                    }, this),\n                                    \" All systems appear to be working correctly. If you're still experiencing issues, try refreshing the page or clearing your browser cache.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-muted-foreground space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Backend URL:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 26\n                                    }, this),\n                                    \" \",\n                                    \"http://localhost:8000\" || 0\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Frontend URL:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 26\n                                    }, this),\n                                    \" \",\n                                     true ? window.location.origin : 0\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                lineNumber: 215,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n        lineNumber: 205,\n        columnNumber: 9\n    }, this);\n}\n_s1(ConnectionDiagnostic, \"7QdI1OzKWJM2EYivVORcNQJ8FIA=\");\n_c = ConnectionDiagnostic;\nvar _c;\n$RefreshReg$(_c, \"ConnectionDiagnostic\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/connection-diagnostic.tsx\n"));

/***/ })

});