#!/usr/bin/env python3
"""
Database Reset Script

This script completely cleans both PostgreSQL and Redis databases
and recreates all tables for fresh testing.
"""

import asyncio
import subprocess
import sys
from sqlalchemy import text
from app.database import async_engine

async def clean_postgresql():
    """Clean PostgreSQL database by dropping all tables and types."""
    print("🗑️ Cleaning PostgreSQL database...")
    
    async with async_engine.begin() as conn:
        # Drop all tables and types
        drop_commands = [
            "DROP TABLE IF EXISTS audit_logs CASCADE;",
            "DROP TABLE IF EXISTS blacklisted_tokens CASCADE;",
            "DROP TABLE IF EXISTS sessions CASCADE;", 
            "DROP TABLE IF EXISTS email_verification_tokens CASCADE;",
            "DROP TABLE IF EXISTS password_reset_tokens CASCADE;",
            "DROP TABLE IF EXISTS users CASCADE;",
            "DROP TABLE IF EXISTS alembic_version CASCADE;",
            # Drop enum types
            "DROP TYPE IF EXISTS userrole CASCADE;",
            "DROP TYPE IF EXISTS auditeventtype CASCADE;",
        ]
        
        for cmd in drop_commands:
            try:
                await conn.execute(text(cmd))
                print(f"✅ Executed: {cmd}")
            except Exception as e:
                print(f"⚠️ Warning: {cmd} - {e}")
    
    print("✅ PostgreSQL database cleaned successfully!")

def clean_redis():
    """Clean Redis database."""
    print("🗑️ Cleaning Redis database...")
    
    try:
        result = subprocess.run(
            ["docker", "exec", "-it", "aixiate-redis", "redis-cli", "FLUSHALL"],
            capture_output=True,
            text=True,
            check=True
        )
        print("✅ Redis database cleaned successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to clean Redis: {e}")
        return False
    except FileNotFoundError:
        print("❌ Docker not found. Please clean Redis manually.")
        return False

def recreate_tables():
    """Recreate database tables using Alembic."""
    print("🔄 Recreating database tables...")
    
    try:
        # Generate new migration
        result = subprocess.run(
            ["alembic", "revision", "--autogenerate", "-m", "reset_database"],
            capture_output=True,
            text=True,
            check=True
        )
        print("✅ Generated new migration")
        
        # Apply migration
        result = subprocess.run(
            ["alembic", "upgrade", "head"],
            capture_output=True,
            text=True,
            check=True
        )
        print("✅ Applied migration - tables recreated!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to recreate tables: {e}")
        print("Error output:", e.stderr)
        return False

async def main():
    """Main function to reset both databases."""
    print("🚀 Starting database reset...")
    print("=" * 50)
    
    # Clean PostgreSQL
    await clean_postgresql()
    print()
    
    # Clean Redis
    redis_success = clean_redis()
    print()
    
    # Recreate tables
    if redis_success:
        tables_success = recreate_tables()
        print()
        
        if tables_success:
            print("🎉 Database reset completed successfully!")
            print("✅ PostgreSQL: All tables recreated")
            print("✅ Redis: All cache cleared")
            print()
            print("You can now run fresh tests with clean databases.")
        else:
            print("❌ Database reset failed during table recreation.")
            sys.exit(1)
    else:
        print("❌ Database reset failed during Redis cleanup.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main()) 