"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-connection/page",{

/***/ "(app-pages-browser)/./src/components/connection-diagnostic.tsx":
/*!**************************************************!*\
  !*** ./src/components/connection-diagnostic.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectionDiagnostic: () => (/* binding */ ConnectionDiagnostic)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Loader2,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Loader2,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Loader2,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Loader2,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Loader2,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _hooks_use_client_side__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-client-side */ \"(app-pages-browser)/./src/hooks/use-client-side.ts\");\n/* __next_internal_client_entry_do_not_use__ ConnectionDiagnostic auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ConnectionDiagnostic() {\n    var _connectionStatus_details;\n    _s();\n    const [isRunning, setIsRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const currentOrigin = (0,_hooks_use_client_side__WEBPACK_IMPORTED_MODULE_7__.useCurrentOrigin)();\n    const diagnosticTests = [\n        {\n            name: 'Backend Connection',\n            test: 'connection'\n        },\n        {\n            name: 'API Health Check',\n            test: 'health'\n        },\n        {\n            name: 'Authentication Endpoint',\n            test: 'auth'\n        },\n        {\n            name: 'CORS Configuration',\n            test: 'cors'\n        }\n    ];\n    const runDiagnostics = async ()=>{\n        setIsRunning(true);\n        setResults([]);\n        setConnectionStatus(null);\n        // Initialize results with pending status\n        const initialResults = diagnosticTests.map((test)=>({\n                test: test.name,\n                status: 'pending',\n                message: 'Running...'\n            }));\n        setResults(initialResults);\n        try {\n            // Test 1: Basic connection\n            await updateResult('Backend Connection', async ()=>{\n                const result = await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.testConnection();\n                setConnectionStatus(result);\n                if (result.success) {\n                    return {\n                        status: 'success',\n                        message: result.message,\n                        details: result.details\n                    };\n                } else {\n                    return {\n                        status: 'error',\n                        message: result.message,\n                        details: result.details\n                    };\n                }\n            });\n            // Test 2: Health endpoint\n            await updateResult('API Health Check', async ()=>{\n                try {\n                    const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.get('/health');\n                    return {\n                        status: 'success',\n                        message: 'Health endpoint accessible',\n                        details: response\n                    };\n                } catch (error) {\n                    return {\n                        status: 'error',\n                        message: \"Health check failed: \".concat(error.message),\n                        details: error\n                    };\n                }\n            });\n            // Test 3: Auth endpoint (expect validation error)\n            await updateResult('Authentication Endpoint', async ()=>{\n                try {\n                    await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.post('/auth/login', {\n                        email_or_username: '<EMAIL>',\n                        password: 'testpassword'\n                    });\n                    return {\n                        status: 'warning',\n                        message: 'Unexpected success - endpoint may have issues'\n                    };\n                } catch (error) {\n                    if (error.message.includes('Validation error') || error.message.includes('Invalid credentials')) {\n                        return {\n                            status: 'success',\n                            message: 'Auth endpoint accessible (expected validation error)',\n                            details: error.message\n                        };\n                    } else {\n                        return {\n                            status: 'error',\n                            message: \"Auth endpoint error: \".concat(error.message),\n                            details: error\n                        };\n                    }\n                }\n            });\n            // Test 4: CORS (simplified check)\n            await updateResult('CORS Configuration', async ()=>{\n                try {\n                    // If we can make requests, CORS is likely configured correctly\n                    await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.get('/health');\n                    return {\n                        status: 'success',\n                        message: 'CORS appears to be configured correctly'\n                    };\n                } catch (error) {\n                    if (error.message.includes('CORS')) {\n                        return {\n                            status: 'error',\n                            message: 'CORS configuration issue detected',\n                            details: error\n                        };\n                    } else {\n                        return {\n                            status: 'warning',\n                            message: 'Unable to verify CORS (other connection issues present)'\n                        };\n                    }\n                }\n            });\n        } catch (error) {\n            console.error('Diagnostic error:', error);\n        } finally{\n            setIsRunning(false);\n        }\n    };\n    const updateResult = async (testName, testFunction)=>{\n        try {\n            const result = await testFunction();\n            setResults((prev)=>prev.map((r)=>r.test === testName ? {\n                        ...r,\n                        ...result\n                    } : r));\n        } catch (error) {\n            setResults((prev)=>prev.map((r)=>r.test === testName ? {\n                        ...r,\n                        status: 'error',\n                        message: \"Test failed: \".concat(error.message),\n                        details: error\n                    } : r));\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'success':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 24\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 24\n                }, this);\n            case 'warning':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 24\n                }, this);\n            case 'pending':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 animate-spin text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 24\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const variants = {\n            success: 'default',\n            error: 'destructive',\n            warning: 'secondary',\n            pending: 'outline'\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n            variant: variants[status] || 'outline',\n            children: status.toUpperCase()\n        }, void 0, false, {\n            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n            lineNumber: 184,\n            columnNumber: 13\n        }, this);\n    };\n    // Auto-run diagnostics on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConnectionDiagnostic.useEffect\": ()=>{\n            runDiagnostics();\n        }\n    }[\"ConnectionDiagnostic.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: \"w-full max-w-2xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 21\n                            }, this),\n                            \"Connection Diagnostics\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                        children: \"Diagnose connection issues between frontend and backend services\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                lineNumber: 197,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"space-y-4\",\n                children: [\n                    connectionStatus && !connectionStatus.success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                        variant: \"destructive\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.AlertDescription, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Connection Failed:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 29\n                                    }, this),\n                                    \" \",\n                                    connectionStatus.message,\n                                    ((_connectionStatus_details = connectionStatus.details) === null || _connectionStatus_details === void 0 ? void 0 : _connectionStatus_details.error) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 text-sm\",\n                                        children: [\n                                            \"Error Code: \",\n                                            connectionStatus.details.error\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: results.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            getStatusIcon(result.status),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: result.test\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: result.message\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 29\n                                    }, this),\n                                    getStatusBadge(result.status)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 25\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: runDiagnostics,\n                            disabled: isRunning,\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                isRunning ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 29\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 29\n                                }, this),\n                                isRunning ? 'Running Diagnostics...' : 'Run Diagnostics'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 17\n                    }, this),\n                    (connectionStatus === null || connectionStatus === void 0 ? void 0 : connectionStatus.success) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Loader2_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.AlertDescription, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Connection Successful!\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 29\n                                    }, this),\n                                    \" All systems appear to be working correctly. If you're still experiencing issues, try refreshing the page or clearing your browser cache.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-muted-foreground space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Backend URL:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 26\n                                    }, this),\n                                    \" \",\n                                    \"http://localhost:8000\" || 0\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Frontend URL:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 26\n                                    }, this),\n                                    \" \",\n                                    currentOrigin\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n                lineNumber: 206,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\connection-diagnostic.tsx\",\n        lineNumber: 196,\n        columnNumber: 9\n    }, this);\n}\n_s(ConnectionDiagnostic, \"ROHao1IJWhkG4PApFegQ406SX6o=\", false, function() {\n    return [\n        _hooks_use_client_side__WEBPACK_IMPORTED_MODULE_7__.useCurrentOrigin\n    ];\n});\n_c = ConnectionDiagnostic;\nvar _c;\n$RefreshReg$(_c, \"ConnectionDiagnostic\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/connection-diagnostic.tsx\n"));

/***/ })

});