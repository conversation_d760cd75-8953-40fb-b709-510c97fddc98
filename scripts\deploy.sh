#!/bin/bash

# Aixiate Webapp Deployment Script
# Usage: ./scripts/deploy.sh [staging|production] [backend|frontend|all]

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT=${1:-staging}
COMPONENT=${2:-all}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validation
validate_environment() {
    if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
        log_error "Invalid environment: $ENVIRONMENT. Use 'staging' or 'production'"
        exit 1
    fi
}

validate_component() {
    if [[ "$COMPONENT" != "backend" && "$COMPONENT" != "frontend" && "$COMPONENT" != "all" ]]; then
        log_error "Invalid component: $COMPONENT. Use 'backend', 'frontend', or 'all'"
        exit 1
    fi
}

# Pre-deployment checks
pre_deployment_checks() {
    log_info "Running pre-deployment checks..."
    
    # Check if required tools are installed
    command -v docker >/dev/null 2>&1 || { log_error "Docker is required but not installed."; exit 1; }
    command -v aws >/dev/null 2>&1 || { log_error "AWS CLI is required but not installed."; exit 1; }
    
    # Check AWS credentials
    if ! aws sts get-caller-identity >/dev/null 2>&1; then
        log_error "AWS credentials not configured or invalid"
        exit 1
    fi
    
    # Check if we're on the correct branch
    CURRENT_BRANCH=$(git branch --show-current)
    if [[ "$ENVIRONMENT" == "production" && "$CURRENT_BRANCH" != "main" ]]; then
        log_error "Production deployments must be from 'main' branch. Current branch: $CURRENT_BRANCH"
        exit 1
    elif [[ "$ENVIRONMENT" == "staging" && "$CURRENT_BRANCH" != "develop" ]]; then
        log_error "Staging deployments must be from 'develop' branch. Current branch: $CURRENT_BRANCH"
        exit 1
    fi
    
    log_success "Pre-deployment checks passed"
}

# Build and push Docker images
build_and_push_images() {
    local component=$1
    log_info "Building and pushing $component images for $ENVIRONMENT..."
    
    # Set registry and image names
    REGISTRY="ghcr.io"
    REPO_NAME=$(basename "$PROJECT_ROOT")
    IMAGE_TAG="${ENVIRONMENT}-$(git rev-parse --short HEAD)"
    
    if [[ "$component" == "backend" || "$component" == "all" ]]; then
        log_info "Building backend image..."
        cd "$PROJECT_ROOT/backend"
        
        docker build -t "${REGISTRY}/${REPO_NAME}-backend:${IMAGE_TAG}" .
        docker tag "${REGISTRY}/${REPO_NAME}-backend:${IMAGE_TAG}" "${REGISTRY}/${REPO_NAME}-backend:${ENVIRONMENT}-latest"
        
        docker push "${REGISTRY}/${REPO_NAME}-backend:${IMAGE_TAG}"
        docker push "${REGISTRY}/${REPO_NAME}-backend:${ENVIRONMENT}-latest"
        
        log_success "Backend image built and pushed"
    fi
    
    if [[ "$component" == "frontend" || "$component" == "all" ]]; then
        log_info "Building frontend image..."
        cd "$PROJECT_ROOT/frontend"
        
        docker build -t "${REGISTRY}/${REPO_NAME}-frontend:${IMAGE_TAG}" .
        docker tag "${REGISTRY}/${REPO_NAME}-frontend:${IMAGE_TAG}" "${REGISTRY}/${REPO_NAME}-frontend:${ENVIRONMENT}-latest"
        
        docker push "${REGISTRY}/${REPO_NAME}-frontend:${IMAGE_TAG}"
        docker push "${REGISTRY}/${REPO_NAME}-frontend:${ENVIRONMENT}-latest"
        
        log_success "Frontend image built and pushed"
    fi
}

# Database migration
run_database_migration() {
    log_info "Running database migrations for $ENVIRONMENT..."
    
    # Get the appropriate cluster and task definition
    CLUSTER="aixiate-${ENVIRONMENT}"
    TASK_DEF="aixiate-migration-${ENVIRONMENT}"
    
    # Get subnet and security group based on environment
    if [[ "$ENVIRONMENT" == "staging" ]]; then
        SUBNETS="$STAGING_SUBNET_IDS"
        SECURITY_GROUP="$STAGING_SECURITY_GROUP"
    else
        SUBNETS="$PROD_SUBNET_IDS"
        SECURITY_GROUP="$PROD_SECURITY_GROUP"
    fi
    
    # Run migration task
    TASK_ARN=$(aws ecs run-task \
        --cluster "$CLUSTER" \
        --task-definition "$TASK_DEF" \
        --launch-type FARGATE \
        --network-configuration "awsvpcConfiguration={subnets=[$SUBNETS],securityGroups=[$SECURITY_GROUP],assignPublicIp=ENABLED}" \
        --query 'tasks[0].taskArn' \
        --output text)
    
    log_info "Migration task started: $TASK_ARN"
    
    # Wait for migration to complete
    aws ecs wait tasks-stopped --cluster "$CLUSTER" --tasks "$TASK_ARN"
    
    # Check if migration was successful
    EXIT_CODE=$(aws ecs describe-tasks \
        --cluster "$CLUSTER" \
        --tasks "$TASK_ARN" \
        --query 'tasks[0].containers[0].exitCode' \
        --output text)
    
    if [[ "$EXIT_CODE" == "0" ]]; then
        log_success "Database migration completed successfully"
    else
        log_error "Database migration failed with exit code: $EXIT_CODE"
        exit 1
    fi
}

# Deploy to ECS
deploy_to_ecs() {
    local component=$1
    log_info "Deploying $component to ECS $ENVIRONMENT..."
    
    CLUSTER="aixiate-${ENVIRONMENT}"
    
    if [[ "$component" == "backend" || "$component" == "all" ]]; then
        log_info "Updating backend service..."
        aws ecs update-service \
            --cluster "$CLUSTER" \
            --service "aixiate-backend-${ENVIRONMENT}" \
            --force-new-deployment
    fi
    
    if [[ "$component" == "frontend" || "$component" == "all" ]]; then
        log_info "Updating frontend service..."
        aws ecs update-service \
            --cluster "$CLUSTER" \
            --service "aixiate-frontend-${ENVIRONMENT}" \
            --force-new-deployment
    fi
    
    # Wait for services to stabilize
    log_info "Waiting for services to stabilize..."
    
    SERVICES=()
    if [[ "$component" == "backend" || "$component" == "all" ]]; then
        SERVICES+=("aixiate-backend-${ENVIRONMENT}")
    fi
    if [[ "$component" == "frontend" || "$component" == "all" ]]; then
        SERVICES+=("aixiate-frontend-${ENVIRONMENT}")
    fi
    
    aws ecs wait services-stable \
        --cluster "$CLUSTER" \
        --services "${SERVICES[@]}"
    
    log_success "Services deployed and stabilized"
}

# Health checks
run_health_checks() {
    log_info "Running health checks for $ENVIRONMENT..."
    
    # Set URLs based on environment
    if [[ "$ENVIRONMENT" == "staging" ]]; then
        BACKEND_URL="https://api-staging.aixiate.com"
        FRONTEND_URL="https://staging.aixiate.com"
    else
        BACKEND_URL="https://api.aixiate.com"
        FRONTEND_URL="https://aixiate.com"
    fi
    
    # Backend health check
    log_info "Checking backend health..."
    for i in {1..10}; do
        if curl -f "$BACKEND_URL/health" >/dev/null 2>&1; then
            log_success "Backend health check passed"
            break
        else
            log_warning "Backend health check failed, attempt $i/10"
            sleep 10
        fi
        
        if [[ $i -eq 10 ]]; then
            log_error "Backend health check failed after 10 attempts"
            exit 1
        fi
    done
    
    # Frontend health check
    log_info "Checking frontend health..."
    for i in {1..10}; do
        if curl -f "$FRONTEND_URL" >/dev/null 2>&1; then
            log_success "Frontend health check passed"
            break
        else
            log_warning "Frontend health check failed, attempt $i/10"
            sleep 10
        fi
        
        if [[ $i -eq 10 ]]; then
            log_error "Frontend health check failed after 10 attempts"
            exit 1
        fi
    done
    
    log_success "All health checks passed"
}

# Rollback function
rollback_deployment() {
    log_warning "Rolling back deployment..."
    
    CLUSTER="aixiate-${ENVIRONMENT}"
    
    # Get previous task definition revisions
    BACKEND_PREV=$(aws ecs describe-services \
        --cluster "$CLUSTER" \
        --services "aixiate-backend-${ENVIRONMENT}" \
        --query 'services[0].deployments[1].taskDefinition' \
        --output text)
    
    FRONTEND_PREV=$(aws ecs describe-services \
        --cluster "$CLUSTER" \
        --services "aixiate-frontend-${ENVIRONMENT}" \
        --query 'services[0].deployments[1].taskDefinition' \
        --output text)
    
    # Rollback services
    if [[ "$BACKEND_PREV" != "None" ]]; then
        aws ecs update-service \
            --cluster "$CLUSTER" \
            --service "aixiate-backend-${ENVIRONMENT}" \
            --task-definition "$BACKEND_PREV"
    fi
    
    if [[ "$FRONTEND_PREV" != "None" ]]; then
        aws ecs update-service \
            --cluster "$CLUSTER" \
            --service "aixiate-frontend-${ENVIRONMENT}" \
            --task-definition "$FRONTEND_PREV"
    fi
    
    log_success "Rollback completed"
}

# Main deployment function
main() {
    log_info "Starting deployment to $ENVIRONMENT for $COMPONENT..."
    
    validate_environment
    validate_component
    pre_deployment_checks
    
    # Trap to handle errors and rollback if needed
    trap 'log_error "Deployment failed! Rolling back..."; rollback_deployment; exit 1' ERR
    
    build_and_push_images "$COMPONENT"
    
    if [[ "$COMPONENT" == "backend" || "$COMPONENT" == "all" ]]; then
        run_database_migration
    fi
    
    deploy_to_ecs "$COMPONENT"
    run_health_checks
    
    log_success "Deployment to $ENVIRONMENT completed successfully!"
    
    # Send notification (if configured)
    if [[ -n "$SLACK_WEBHOOK_URL" ]]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"✅ Deployment to $ENVIRONMENT completed successfully!\"}" \
            "$SLACK_WEBHOOK_URL"
    fi
}

# Run main function
main "$@"
