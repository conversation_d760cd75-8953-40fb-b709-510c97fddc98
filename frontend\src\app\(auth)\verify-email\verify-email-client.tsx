"use client";

import { useEffect, useState } from "react";
import { useSearchPara<PERSON>, useRouter } from "next/navigation";
import { authService } from "@/lib/auth-service";
import { CheckCircle, XCircle } from "lucide-react";
import { <PERSON><PERSON>, <PERSON>ertTitle, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { ResendVerificationForm } from "@/components/resend-verification-form";

export default function VerifyEmailClient() {
  const params = useSearchParams();
  const token = params.get("token");
  const router = useRouter();

  const [state, setState] = useState<{
    loading: boolean;
    success: boolean | null;
    message: string;
  }>({ loading: true, success: null, message: "" });

  const [resendSuccess, setResendSuccess] = useState(false);

  useEffect(() => {
    const verify = async () => {
      if (!token) {
        setState({ loading: false, success: false, message: "Missing token" });
        return;
      }
      try {
        const response = await authService.verifyEmail(token);
        setState({ loading: false, success: true, message: response.message });
      } catch (error) {
        setState({
          loading: false,
          success: false,
          message:
            error instanceof Error ? error.message : "Verification failed",
        });
      }
    };
    verify();
  }, [token]);

  // Auto-redirect to login after success
  useEffect(() => {
    if (state.success) {
      const timeout = setTimeout(() => {
        router.push("/login");
      }, 2000); // 2 seconds for user to see success message
      return () => clearTimeout(timeout);
    }
  }, [state.success, router]);

  if (state.loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
          <p className="text-sm text-muted-foreground">Verifying your email…</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen p-4 bg-muted/20 dark:bg-background">
      <Card className="w-full max-w-md shadow-xl border border-border/70 backdrop-blur-sm bg-card/80">
        <CardHeader className="pb-0 text-center">
          <CardTitle className="text-lg font-semibold tracking-tight">
            Account Email Verification
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-6 pb-8 space-y-6">
          {state.success ? (
            <div className="flex flex-col items-center text-center space-y-4">
              <CheckCircle className="size-12 text-green-600" />
              <p className="text-base font-medium text-green-700 dark:text-green-300">
                {state.message || "Your email has been verified!"}
              </p>
              <p className="text-sm text-muted-foreground">
                Redirecting you to the login page…
              </p>
            </div>
          ) : (
            <>
              {!resendSuccess && (
                <div className="flex flex-col items-center text-center space-y-4">
                  <XCircle className="size-12 text-red-600" />
                  <p className="text-base font-medium text-red-700 dark:text-red-300">
                    {state.message || "Verification failed"}
                  </p>
                </div>
              )}
              <ResendVerificationForm
                className="pt-2"
                onSuccess={() => setResendSuccess(true)}
              />
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
