"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(client)/layout",{

/***/ "(app-pages-browser)/./src/lib/api-client.ts":
/*!*******************************!*\
  !*** ./src/lib/api-client.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   apiClientInstance: () => (/* binding */ apiClientInstance),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\n// API Configuration\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\nconst API_VERSION = '/api/v1';\n// Cache configuration\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds\nconst CACHE_PREFIX = 'api_cache_';\n// Cache utility functions\nclass CacheManager {\n    static getInstance() {\n        if (!CacheManager.instance) {\n            CacheManager.instance = new CacheManager();\n        }\n        return CacheManager.instance;\n    }\n    set(key, data) {\n        let duration = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : CACHE_DURATION;\n        const expiresAt = Date.now() + duration;\n        this.cache.set(key, {\n            data,\n            timestamp: Date.now(),\n            expiresAt\n        });\n        // Also store in localStorage for persistence across page reloads\n        if (true) {\n            try {\n                localStorage.setItem(\"\".concat(CACHE_PREFIX).concat(key), JSON.stringify({\n                    data,\n                    timestamp: Date.now(),\n                    expiresAt\n                }));\n            } catch (error) {\n                console.warn('Failed to store cache in localStorage:', error);\n            }\n        }\n    }\n    get(key) {\n        // Check memory cache first\n        const memoryEntry = this.cache.get(key);\n        if (memoryEntry && memoryEntry.expiresAt > Date.now()) {\n            return memoryEntry.data;\n        }\n        // Check localStorage\n        if (true) {\n            try {\n                const stored = localStorage.getItem(\"\".concat(CACHE_PREFIX).concat(key));\n                if (stored) {\n                    const entry = JSON.parse(stored);\n                    if (entry.expiresAt > Date.now()) {\n                        // Update memory cache\n                        this.cache.set(key, entry);\n                        return entry.data;\n                    } else {\n                        // Remove expired entry\n                        localStorage.removeItem(\"\".concat(CACHE_PREFIX).concat(key));\n                    }\n                }\n            } catch (error) {\n                console.warn('Failed to retrieve cache from localStorage:', error);\n            }\n        }\n        return null;\n    }\n    delete(key) {\n        this.cache.delete(key);\n        if (true) {\n            localStorage.removeItem(\"\".concat(CACHE_PREFIX).concat(key));\n        }\n    }\n    clear(pattern) {\n        if (pattern) {\n            // Clear specific pattern\n            for (const key of this.cache.keys()){\n                if (key.includes(pattern)) {\n                    this.cache.delete(key);\n                }\n            }\n            if (true) {\n                for(let i = localStorage.length - 1; i >= 0; i--){\n                    const key = localStorage.key(i);\n                    if (key && key.startsWith(CACHE_PREFIX) && key.includes(pattern)) {\n                        localStorage.removeItem(key);\n                    }\n                }\n            }\n        } else {\n            // Clear all cache\n            this.cache.clear();\n            if (true) {\n                for(let i = localStorage.length - 1; i >= 0; i--){\n                    const key = localStorage.key(i);\n                    if (key && key.startsWith(CACHE_PREFIX)) {\n                        localStorage.removeItem(key);\n                    }\n                }\n            }\n        }\n    }\n    generateKey(config) {\n        const { method, url, params, data } = config;\n        return \"\".concat(method === null || method === void 0 ? void 0 : method.toUpperCase(), \"_\").concat(url, \"_\").concat(JSON.stringify(params), \"_\").concat(JSON.stringify(data));\n    }\n    constructor(){\n        this.cache = new Map();\n    }\n}\n// Create axios instance\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: \"\".concat(API_BASE_URL).concat(API_VERSION),\n    timeout: 30000,\n    withCredentials: false,\n    headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n    }\n});\n// Request interceptor to add auth token and debug logging\napiClient.interceptors.request.use((config)=>{\n    // Debug logging\n    if (true) {\n        var _config_method;\n        console.log('API Request:', {\n            method: (_config_method = config.method) === null || _config_method === void 0 ? void 0 : _config_method.toUpperCase(),\n            url: config.url,\n            baseURL: config.baseURL,\n            fullURL: \"\".concat(config.baseURL).concat(config.url),\n            headers: config.headers,\n            data: config.data\n        });\n    }\n    // Get token from localStorage or cookies\n    const token =  true ? localStorage.getItem('access_token') : 0;\n    if (token) {\n        config.headers.Authorization = \"Bearer \".concat(token);\n    }\n    return config;\n}, (error)=>{\n    console.error('API Request Error:', error);\n    return Promise.reject(error);\n});\n// Response interceptor for token refresh and error handling\napiClient.interceptors.response.use((response)=>{\n    // Debug logging\n    if (true) {\n        console.log('API Response:', {\n            status: response.status,\n            statusText: response.statusText,\n            url: response.config.url,\n            data: response.data\n        });\n    }\n    return response;\n}, async (error)=>{\n    var _error_response;\n    const originalRequest = error.config;\n    // Handle 401 Unauthorized - try to refresh token\n    if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        try {\n            const refreshToken =  true ? localStorage.getItem('refresh_token') : 0;\n            if (refreshToken) {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(API_BASE_URL).concat(API_VERSION, \"/auth/refresh\"), {\n                    refresh_token: refreshToken\n                });\n                const { access_token, refresh_token } = response.data;\n                // Store new tokens\n                if (true) {\n                    localStorage.setItem('access_token', access_token);\n                    localStorage.setItem('refresh_token', refresh_token);\n                }\n                // Retry original request with new token\n                originalRequest.headers.Authorization = \"Bearer \".concat(access_token);\n                return apiClient(originalRequest);\n            }\n        } catch (e) {\n            // Refresh failed - clear tokens and redirect to login\n            if (true) {\n                localStorage.removeItem('access_token');\n                localStorage.removeItem('refresh_token');\n                window.location.href = '/login';\n            }\n        }\n    }\n    return Promise.reject(error);\n});\n// API Client class with typed methods and caching\nclass ApiClient {\n    // Generic request method with caching\n    async request(config) {\n        let useCache = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false, cacheDuration = arguments.length > 2 ? arguments[2] : void 0;\n        try {\n            var _config_method, _config_method1;\n            // Check cache for GET requests\n            if (useCache && ((_config_method = config.method) === null || _config_method === void 0 ? void 0 : _config_method.toLowerCase()) === 'get') {\n                const cacheKey = this.cache.generateKey(config);\n                const cachedData = this.cache.get(cacheKey);\n                if (cachedData) {\n                    return cachedData;\n                }\n            }\n            const response = await this.client.request(config);\n            // Cache successful GET responses\n            if (useCache && ((_config_method1 = config.method) === null || _config_method1 === void 0 ? void 0 : _config_method1.toLowerCase()) === 'get' && response.status === 200) {\n                const cacheKey = this.cache.generateKey(config);\n                this.cache.set(cacheKey, response.data, cacheDuration);\n            }\n            return response.data;\n        } catch (error) {\n            throw this.handleError(error);\n        }\n    }\n    // Cached GET request\n    async get(url, params) {\n        let useCache = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true, cacheDuration = arguments.length > 3 ? arguments[3] : void 0;\n        return this.request({\n            method: 'GET',\n            url,\n            params\n        }, useCache, cacheDuration);\n    }\n    // POST request (no caching)\n    async post(url, data) {\n        return this.request({\n            method: 'POST',\n            url,\n            data\n        }, false);\n    }\n    // PUT request (no caching)\n    async put(url, data) {\n        return this.request({\n            method: 'PUT',\n            url,\n            data\n        }, false);\n    }\n    // DELETE request (no caching)\n    async delete(url) {\n        return this.request({\n            method: 'DELETE',\n            url\n        }, false);\n    }\n    // Clear cache\n    clearCache(pattern) {\n        this.cache.clear(pattern);\n    }\n    // Test connection to backend\n    async testConnection() {\n        try {\n            console.log('Testing connection to:', \"\".concat(this.client.defaults.baseURL, \"/health\"));\n            const response = await this.client.get('/health', {\n                timeout: 10000,\n                validateStatus: ()=>true // Accept any status code\n            });\n            if (response.status === 200) {\n                return {\n                    success: true,\n                    message: 'Backend connection successful',\n                    details: response.data\n                };\n            } else {\n                return {\n                    success: false,\n                    message: \"Backend responded with status \".concat(response.status),\n                    details: response.data\n                };\n            }\n        } catch (error) {\n            console.error('Connection test failed:', error);\n            if (error.code === 'ECONNREFUSED') {\n                return {\n                    success: false,\n                    message: 'Backend server is not running. Please start the backend server on http://localhost:8000',\n                    details: {\n                        error: error.code,\n                        message: error.message\n                    }\n                };\n            } else if (error.code === 'ENOTFOUND') {\n                return {\n                    success: false,\n                    message: 'Backend server not found. Please check the API URL configuration.',\n                    details: {\n                        error: error.code,\n                        message: error.message\n                    }\n                };\n            } else {\n                return {\n                    success: false,\n                    message: \"Connection failed: \".concat(error.message),\n                    details: {\n                        error: error.code,\n                        message: error.message\n                    }\n                };\n            }\n        }\n    }\n    // Handle API errors with enhanced diagnostics\n    handleError(error) {\n        console.error('API Client Error:', error);\n        if (error && typeof error === 'object' && 'response' in error) {\n            // Server responded with error status\n            const { status, data } = error.response;\n            // Prefer server-provided detail when available so the UI can show precise feedback\n            const serverMessage = data === null || data === void 0 ? void 0 : data.detail;\n            switch(status){\n                case 400:\n                    return new Error(serverMessage || 'Bad request');\n                case 401:\n                    // Pass through messages such as \"Account is locked\" when supplied\n                    return new Error(serverMessage || 'Unauthorized - please login again');\n                case 403:\n                    return new Error(serverMessage || 'Access denied - insufficient permissions');\n                case 404:\n                    return new Error(serverMessage || 'Resource not found');\n                case 422:\n                    return new Error(serverMessage || 'Validation error');\n                case 429:\n                    return new Error(serverMessage || 'Too many requests - please try again later');\n                case 500:\n                    return new Error(serverMessage || 'Internal server error');\n                default:\n                    return new Error(serverMessage || 'An error occurred');\n            }\n        } else if (error && typeof error === 'object' && 'request' in error) {\n            var _axiosError_config, _axiosError_config1, _axiosError_config2, _axiosError_config3;\n            // Network error - provide more detailed diagnostics\n            const axiosError = error;\n            console.error('Network Error Details:', {\n                code: axiosError.code,\n                message: axiosError.message,\n                config: {\n                    url: (_axiosError_config = axiosError.config) === null || _axiosError_config === void 0 ? void 0 : _axiosError_config.url,\n                    method: (_axiosError_config1 = axiosError.config) === null || _axiosError_config1 === void 0 ? void 0 : _axiosError_config1.method,\n                    baseURL: (_axiosError_config2 = axiosError.config) === null || _axiosError_config2 === void 0 ? void 0 : _axiosError_config2.baseURL,\n                    timeout: (_axiosError_config3 = axiosError.config) === null || _axiosError_config3 === void 0 ? void 0 : _axiosError_config3.timeout\n                }\n            });\n            // Provide specific error messages based on error code\n            if (axiosError.code === 'ECONNREFUSED') {\n                return new Error('Connection refused - Backend server may not be running. Please check if the server is started on http://localhost:8000');\n            } else if (axiosError.code === 'ENOTFOUND') {\n                return new Error('Server not found - Please check the API URL configuration');\n            } else if (axiosError.code === 'ETIMEDOUT') {\n                return new Error('Request timeout - Server is taking too long to respond');\n            } else if (axiosError.code === 'ECONNABORTED') {\n                return new Error('Request aborted - Connection was terminated');\n            } else {\n                return new Error(\"Network error (\".concat(axiosError.code || 'UNKNOWN', \") - Please check your connection and ensure the backend server is running\"));\n            }\n        } else {\n            // Other error\n            const message = error && typeof error === 'object' && 'message' in error ? String(error.message) : 'An unexpected error occurred';\n            return new Error(message);\n        }\n    }\n    // Token management\n    setTokens(accessToken, refreshToken) {\n        if (true) {\n            localStorage.setItem('access_token', accessToken);\n            localStorage.setItem('refresh_token', refreshToken);\n        }\n    }\n    clearTokens() {\n        if (true) {\n            localStorage.removeItem('access_token');\n            localStorage.removeItem('refresh_token');\n        }\n    }\n    getAccessToken() {\n        if (true) {\n            return localStorage.getItem('access_token');\n        }\n        return null;\n    }\n    getRefreshToken() {\n        if (true) {\n            return localStorage.getItem('refresh_token');\n        }\n        return null;\n    }\n    constructor(){\n        this.client = apiClient;\n        this.cache = CacheManager.getInstance();\n    }\n}\n// Export singleton instance\nconst apiClientInstance = new ApiClient();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClientInstance);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api-client.ts\n"));

/***/ })

});