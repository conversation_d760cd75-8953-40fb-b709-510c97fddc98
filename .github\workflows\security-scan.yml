name: Security Scanning

on:
  schedule:
    # Run security scans daily at 2 AM UTC
    - cron: '0 2 * * *'
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  # ============================================================================
  # Dependency Vulnerability Scanning
  # ============================================================================
  dependency-scan:
    name: Dependency Vulnerability Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.13'

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      # Backend dependency scanning
      - name: Install Python dependencies
        run: |
          cd backend
          pip install uv
          uv venv
          source .venv/bin/activate
          uv sync

      - name: Run Safety check (Python)
        run: |
          cd backend
          source .venv/bin/activate
          pip install safety
          safety check --json --output safety-report.json || true

      - name: Run Bandit security linter
        run: |
          cd backend
          source .venv/bin/activate
          pip install bandit
          bandit -r app/ -f json -o bandit-report.json || true

      # Frontend dependency scanning
      - name: Install Node dependencies
        run: |
          cd frontend
          npm ci

      - name: Run npm audit
        run: |
          cd frontend
          npm audit --audit-level=high --json > npm-audit-report.json || true

      - name: Run Snyk security scan
        uses: snyk/actions/setup@master
      - run: |
          cd backend && snyk test --json > snyk-backend-report.json || true
          cd frontend && snyk test --json > snyk-frontend-report.json || true
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}

      - name: Upload security reports
        uses: actions/upload-artifact@v3
        with:
          name: security-reports
          path: |
            backend/safety-report.json
            backend/bandit-report.json
            frontend/npm-audit-report.json
            backend/snyk-backend-report.json
            frontend/snyk-frontend-report.json

  # ============================================================================
  # Container Security Scanning
  # ============================================================================
  container-scan:
    name: Container Security Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Build backend image
        run: |
          cd backend
          docker build -t aixiate-backend:latest .

      - name: Build frontend image
        run: |
          cd frontend
          docker build -t aixiate-frontend:latest .

      - name: Run Trivy vulnerability scanner - Backend
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'aixiate-backend:latest'
          format: 'sarif'
          output: 'trivy-backend-results.sarif'

      - name: Run Trivy vulnerability scanner - Frontend
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'aixiate-frontend:latest'
          format: 'sarif'
          output: 'trivy-frontend-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: 'trivy-backend-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: 'trivy-frontend-results.sarif'

  # ============================================================================
  # Secret Scanning
  # ============================================================================
  secret-scan:
    name: Secret Scanning
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Full history for secret scanning

      - name: Run GitLeaks
        uses: gitleaks/gitleaks-action@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GITLEAKS_LICENSE: ${{ secrets.GITLEAKS_LICENSE }}

      - name: Run TruffleHog
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD
          extra_args: --debug --only-verified

  # ============================================================================
  # OWASP ZAP Security Testing
  # ============================================================================
  owasp-zap:
    name: OWASP ZAP Security Test
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request' || github.ref == 'refs/heads/develop'
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.13'

      - name: Start backend for testing
        run: |
          cd backend
          pip install uv
          uv venv
          source .venv/bin/activate
          uv sync
          alembic upgrade head
          uvicorn app.main:app --host 0.0.0.0 --port 8000 &
          sleep 30
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          SECRET_KEY: test-secret-key

      - name: Run OWASP ZAP Baseline Scan
        uses: zaproxy/action-baseline@v0.7.0
        with:
          target: 'http://localhost:8000'
          rules_file_name: '.zap/rules.tsv'
          cmd_options: '-a'

      - name: Run OWASP ZAP Full Scan
        uses: zaproxy/action-full-scan@v0.4.0
        with:
          target: 'http://localhost:8000'
          rules_file_name: '.zap/rules.tsv'
          cmd_options: '-a'

  # ============================================================================
  # Security Report Generation
  # ============================================================================
  security-report:
    name: Generate Security Report
    runs-on: ubuntu-latest
    needs: [dependency-scan, container-scan, secret-scan]
    if: always()
    
    steps:
      - name: Download security artifacts
        uses: actions/download-artifact@v3
        with:
          name: security-reports

      - name: Generate security summary
        run: |
          echo "# Security Scan Summary" > security-summary.md
          echo "Generated on: $(date)" >> security-summary.md
          echo "" >> security-summary.md
          
          # Process safety report
          if [ -f safety-report.json ]; then
            echo "## Python Dependencies (Safety)" >> security-summary.md
            python -c "
            import json
            with open('safety-report.json') as f:
                data = json.load(f)
                if data:
                    print(f'Found {len(data)} vulnerabilities')
                else:
                    print('No vulnerabilities found')
            " >> security-summary.md
          fi
          
          # Process npm audit report
          if [ -f npm-audit-report.json ]; then
            echo "## Node.js Dependencies (npm audit)" >> security-summary.md
            node -e "
            const fs = require('fs');
            const data = JSON.parse(fs.readFileSync('npm-audit-report.json'));
            console.log(\`Found \${data.metadata.vulnerabilities.total} total vulnerabilities\`);
            console.log(\`High: \${data.metadata.vulnerabilities.high}\`);
            console.log(\`Critical: \${data.metadata.vulnerabilities.critical}\`);
            " >> security-summary.md
          fi

      - name: Comment security summary on PR
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const summary = fs.readFileSync('security-summary.md', 'utf8');
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: summary
            });

      - name: Fail on critical vulnerabilities
        run: |
          # Check for critical vulnerabilities and fail the build
          CRITICAL_FOUND=false
          
          if [ -f npm-audit-report.json ]; then
            CRITICAL_COUNT=$(node -e "
              const data = JSON.parse(require('fs').readFileSync('npm-audit-report.json'));
              console.log(data.metadata.vulnerabilities.critical || 0);
            ")
            if [ "$CRITICAL_COUNT" -gt 0 ]; then
              echo "Critical vulnerabilities found in npm dependencies: $CRITICAL_COUNT"
              CRITICAL_FOUND=true
            fi
          fi
          
          if [ "$CRITICAL_FOUND" = true ]; then
            echo "Build failed due to critical security vulnerabilities"
            exit 1
          fi
