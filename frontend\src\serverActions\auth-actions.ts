'use server';

import { z } from 'zod';
import { authService } from '@/lib/auth-service';
import {
    loginSchema,
    registerSchema,
    passwordResetRequestSchema,
    emailSchema,
    passwordResetCompleteSchema
} from '@/lib/validation-schemas';
import { apiClientInstance } from '@/lib/api-client';
// Action state types
export interface ActionState<T = unknown> {
    success: boolean;
    message: string;
    data?: T;
    errors?: Record<string, string[]>;
}

// Login Action
export async function loginAction(
    prevState: ActionState,
    formData: FormData
): Promise<ActionState> {
    try {
        // Parse and validate form data
        const rawData = {
            email_or_username: formData.get('email_or_username') as string,
            password: formData.get('password') as string,
        };

        const validatedData = loginSchema.parse(rawData);

        // Call API directly (server-side)
        const response = await apiClientInstance.request({
            method: 'POST',
            url: '/auth/login',
            data: validatedData,
        });

        return {
            success: true,
            message: 'Login successful!',
            data: response,
        };
    } catch (error) {
        if (error instanceof z.ZodError) {
            // Validation error
            const errors: Record<string, string[]> = {};
            error.errors.forEach((err) => {
                const field = err.path.join('.');
                if (!errors[field]) {
                    errors[field] = [];
                }
                errors[field].push(err.message);
            });

            return {
                success: false,
                message: 'Please fix the validation errors below.',
                errors,
            };
        }

        // API error
        return {
            success: false,
            message: error instanceof Error ? error.message : 'Login failed',
        };
    }
}

// Register Action
export async function registerAction(
    prevState: ActionState,
    formData: FormData
): Promise<ActionState> {
    try {
        // Parse and validate form data
        const rawData = {
            email: formData.get('email') as string,
            username: formData.get('username') as string,
            password: formData.get('password') as string,
            confirmPassword: formData.get('confirmPassword') as string,
            first_name: formData.get('first_name') as string || undefined,
            last_name: formData.get('last_name') as string || undefined,
            full_name: formData.get('full_name') as string || undefined,
            terms: formData.get('terms') === 'on',
        };

        const validatedData = registerSchema.parse(rawData);

        // Prepare data for API (remove confirmPassword and terms)
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { confirmPassword, terms, ...apiData } = validatedData;
        // confirmPassword and terms are intentionally omitted from apiData

        // Call auth service
        const response = await authService.register(apiData);

        return {
            success: true,
            message: response.message || 'Registration successful! Please check your email to verify your account.',
            data: response,
        };
    } catch (error) {
        if (error instanceof z.ZodError) {
            // Validation error
            const errors: Record<string, string[]> = {};
            error.errors.forEach((err) => {
                const field = err.path.join('.');
                if (!errors[field]) {
                    errors[field] = [];
                }
                errors[field].push(err.message);
            });

            return {
                success: false,
                message: 'Please fix the validation errors below.',
                errors,
            };
        }

        // API error
        return {
            success: false,
            message: error instanceof Error ? error.message : 'Registration failed',
        };
    }
}

// Password Reset Request Action
export async function passwordResetRequestAction(
    prevState: ActionState,
    formData: FormData
): Promise<ActionState> {
    try {
        // Parse and validate form data
        const rawData = {
            email: formData.get('email') as string,
        };

        const validatedData = passwordResetRequestSchema.parse(rawData);

        // Call auth service
        const response = await authService.requestPasswordReset(validatedData.email);

        return {
            success: true,
            message: response.message || 'Password reset email sent successfully!',
            data: response,
        };
    } catch (error) {
        if (error instanceof z.ZodError) {
            // Validation error
            const errors: Record<string, string[]> = {};
            error.errors.forEach((err) => {
                const field = err.path.join('.');
                if (!errors[field]) {
                    errors[field] = [];
                }
                errors[field].push(err.message);
            });

            return {
                success: false,
                message: 'Please fix the validation errors below.',
                errors,
            };
        }

        // API error
        return {
            success: false,
            message: error instanceof Error ? error.message : 'Failed to send password reset email',
        };
    }
}

// Logout Action
export async function logoutAction(
    prevState: ActionState,
    formData: FormData
): Promise<ActionState> {
    try {
        const sessionId = formData.get('session_id') as string;
        const logoutAll = formData.get('logout_all') === 'true';

        // Call auth service
        const response = await authService.logout(
            sessionId || undefined,
            logoutAll
        );

        return {
            success: true,
            message: response.message || 'Logged out successfully',
            data: response,
        };
    } catch (error) {
        // Even if logout fails, we should clear local state
        return {
            success: false,
            message: error instanceof Error ? error.message : 'Logout failed',
        };
    }
}

// Get Current User Action
export async function getCurrentUserAction(): Promise<ActionState> {
    try {
        const user = await authService.getCurrentUser();

        return {
            success: true,
            message: 'User profile retrieved successfully',
            data: user,
        };
    } catch (error) {
        return {
            success: false,
            message: error instanceof Error ? error.message : 'Failed to get user profile',
        };
    }
}

// Refresh Token Action
export async function refreshTokenAction(): Promise<ActionState> {
    try {
        const response = await authService.refreshToken();

        return {
            success: true,
            message: 'Token refreshed successfully',
            data: response,
        };
    } catch (error) {
        return {
            success: false,
            message: error instanceof Error ? error.message : 'Failed to refresh token',
        };
    }
}

// Get User Sessions Action
export async function getUserSessionsAction(): Promise<ActionState> {
    try {
        const sessions = await authService.getUserSessions();

        return {
            success: true,
            message: 'User sessions retrieved successfully',
            data: sessions,
        };
    } catch (error) {
        return {
            success: false,
            message: error instanceof Error ? error.message : 'Failed to get user sessions',
        };
    }
}

// Revoke Session Action
export async function revokeSessionAction(
    prevState: ActionState,
    formData: FormData
): Promise<ActionState> {
    try {
        const sessionId = formData.get('session_id') as string;

        if (!sessionId) {
            return {
                success: false,
                message: 'Session ID is required',
            };
        }

        const response = await authService.revokeSession(sessionId);

        return {
            success: true,
            message: response.message || 'Session revoked successfully',
            data: response,
        };
    } catch (error) {
        return {
            success: false,
            message: error instanceof Error ? error.message : 'Failed to revoke session',
        };
    }
}

// Verify Email Action
export async function verifyEmailAction(prevState: ActionState, formData: FormData): Promise<ActionState> {
    try {
        const token = formData.get('token') as string;
        if (!token) {
            return { success: false, message: 'Missing token' };
        }
        const response = await authService.verifyEmail(token);
        return { success: true, message: response.message || 'Email verified successfully', data: response };
    } catch (error) {
        return { success: false, message: error instanceof Error ? error.message : 'Verification failed' };
    }
}

// Resend Verification Action
export async function resendVerificationAction(prevState: ActionState, formData: FormData): Promise<ActionState> {
    try {
        const email = formData.get('email') as string;
        const validatedEmail = emailSchema.parse(email);
        const response = await authService.resendVerification(validatedEmail);
        return { success: true, message: response.message || 'Verification email sent', data: response };
    } catch (error) {
        if (error instanceof z.ZodError) {
            const errors: Record<string, string[]> = {};
            error.errors.forEach((err) => {
                const field = err.path.join('.');
                if (!errors[field]) errors[field] = [];
                errors[field].push(err.message);
            });
            return { success: false, message: 'Please correct errors', errors };
        }
        return { success: false, message: error instanceof Error ? error.message : 'Failed to resend verification email' };
    }
}

// Password Reset Complete Action
export async function passwordResetCompleteAction(prevState: ActionState, formData: FormData): Promise<ActionState> {
    try {
        const rawData = {
            token: formData.get('token') as string,
            new_password: formData.get('new_password') as string,
            confirm_password: formData.get('confirm_password') as string,
        };
        const validated = passwordResetCompleteSchema.parse(rawData);
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { confirm_password, ...apiData } = validated;
        const response = await authService.completePasswordReset(apiData.token, apiData.new_password);
        return {
            success: true,
            message: response.message || 'Password reset successfully! You can now log in.',
            data: response,
        };
    } catch (error) {
        if (error instanceof z.ZodError) {
            const errors: Record<string, string[]> = {};
            error.errors.forEach((err) => {
                const field = err.path.join('.');
                if (!errors[field]) errors[field] = [];
                errors[field].push(err.message);
            });
            return { success: false, message: 'Please fix errors', errors };
        }
        return { success: false, message: error instanceof Error ? error.message : 'Password reset failed' };
    }
} 