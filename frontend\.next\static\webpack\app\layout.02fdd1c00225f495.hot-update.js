"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/lib/api-client.ts":
/*!*******************************!*\
  !*** ./src/lib/api-client.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   apiClientInstance: () => (/* binding */ apiClientInstance),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\n// API Configuration\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\nconst API_VERSION = '/api/v1';\n// Cache configuration\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds\nconst CACHE_PREFIX = 'api_cache_';\n// Cache utility functions\nclass CacheManager {\n    static getInstance() {\n        if (!CacheManager.instance) {\n            CacheManager.instance = new CacheManager();\n        }\n        return CacheManager.instance;\n    }\n    set(key, data) {\n        let duration = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : CACHE_DURATION;\n        const expiresAt = Date.now() + duration;\n        this.cache.set(key, {\n            data,\n            timestamp: Date.now(),\n            expiresAt\n        });\n        // Also store in localStorage for persistence across page reloads\n        if (true) {\n            try {\n                localStorage.setItem(\"\".concat(CACHE_PREFIX).concat(key), JSON.stringify({\n                    data,\n                    timestamp: Date.now(),\n                    expiresAt\n                }));\n            } catch (error) {\n                console.warn('Failed to store cache in localStorage:', error);\n            }\n        }\n    }\n    get(key) {\n        // Check memory cache first\n        const memoryEntry = this.cache.get(key);\n        if (memoryEntry && memoryEntry.expiresAt > Date.now()) {\n            return memoryEntry.data;\n        }\n        // Check localStorage\n        if (true) {\n            try {\n                const stored = localStorage.getItem(\"\".concat(CACHE_PREFIX).concat(key));\n                if (stored) {\n                    const entry = JSON.parse(stored);\n                    if (entry.expiresAt > Date.now()) {\n                        // Update memory cache\n                        this.cache.set(key, entry);\n                        return entry.data;\n                    } else {\n                        // Remove expired entry\n                        localStorage.removeItem(\"\".concat(CACHE_PREFIX).concat(key));\n                    }\n                }\n            } catch (error) {\n                console.warn('Failed to retrieve cache from localStorage:', error);\n            }\n        }\n        return null;\n    }\n    delete(key) {\n        this.cache.delete(key);\n        if (true) {\n            localStorage.removeItem(\"\".concat(CACHE_PREFIX).concat(key));\n        }\n    }\n    clear(pattern) {\n        if (pattern) {\n            // Clear specific pattern\n            for (const key of this.cache.keys()){\n                if (key.includes(pattern)) {\n                    this.cache.delete(key);\n                }\n            }\n            if (true) {\n                for(let i = localStorage.length - 1; i >= 0; i--){\n                    const key = localStorage.key(i);\n                    if (key && key.startsWith(CACHE_PREFIX) && key.includes(pattern)) {\n                        localStorage.removeItem(key);\n                    }\n                }\n            }\n        } else {\n            // Clear all cache\n            this.cache.clear();\n            if (true) {\n                for(let i = localStorage.length - 1; i >= 0; i--){\n                    const key = localStorage.key(i);\n                    if (key && key.startsWith(CACHE_PREFIX)) {\n                        localStorage.removeItem(key);\n                    }\n                }\n            }\n        }\n    }\n    generateKey(config) {\n        const { method, url, params, data } = config;\n        return \"\".concat(method === null || method === void 0 ? void 0 : method.toUpperCase(), \"_\").concat(url, \"_\").concat(JSON.stringify(params), \"_\").concat(JSON.stringify(data));\n    }\n    constructor(){\n        this.cache = new Map();\n    }\n}\n// Create axios instance\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: \"\".concat(API_BASE_URL).concat(API_VERSION),\n    timeout: 10000,\n    withCredentials: true,\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// Request interceptor to add auth token\napiClient.interceptors.request.use((config)=>{\n    // Get token from localStorage or cookies\n    const token =  true ? localStorage.getItem('access_token') : 0;\n    if (token) {\n        config.headers.Authorization = \"Bearer \".concat(token);\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor for token refresh and error handling\napiClient.interceptors.response.use((response)=>{\n    return response;\n}, async (error)=>{\n    var _error_response;\n    const originalRequest = error.config;\n    // Handle 401 Unauthorized - try to refresh token\n    if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        try {\n            const refreshToken =  true ? localStorage.getItem('refresh_token') : 0;\n            if (refreshToken) {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(API_BASE_URL).concat(API_VERSION, \"/auth/refresh\"), {\n                    refresh_token: refreshToken\n                });\n                const { access_token, refresh_token } = response.data;\n                // Store new tokens\n                if (true) {\n                    localStorage.setItem('access_token', access_token);\n                    localStorage.setItem('refresh_token', refresh_token);\n                }\n                // Retry original request with new token\n                originalRequest.headers.Authorization = \"Bearer \".concat(access_token);\n                return apiClient(originalRequest);\n            }\n        } catch (e) {\n            // Refresh failed - clear tokens and redirect to login\n            if (true) {\n                localStorage.removeItem('access_token');\n                localStorage.removeItem('refresh_token');\n                window.location.href = '/login';\n            }\n        }\n    }\n    return Promise.reject(error);\n});\n// API Client class with typed methods and caching\nclass ApiClient {\n    // Generic request method with caching\n    async request(config) {\n        let useCache = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false, cacheDuration = arguments.length > 2 ? arguments[2] : void 0;\n        try {\n            var _config_method, _config_method1;\n            // Check cache for GET requests\n            if (useCache && ((_config_method = config.method) === null || _config_method === void 0 ? void 0 : _config_method.toLowerCase()) === 'get') {\n                const cacheKey = this.cache.generateKey(config);\n                const cachedData = this.cache.get(cacheKey);\n                if (cachedData) {\n                    return cachedData;\n                }\n            }\n            const response = await this.client.request(config);\n            // Cache successful GET responses\n            if (useCache && ((_config_method1 = config.method) === null || _config_method1 === void 0 ? void 0 : _config_method1.toLowerCase()) === 'get' && response.status === 200) {\n                const cacheKey = this.cache.generateKey(config);\n                this.cache.set(cacheKey, response.data, cacheDuration);\n            }\n            return response.data;\n        } catch (error) {\n            throw this.handleError(error);\n        }\n    }\n    // Cached GET request\n    async get(url, params) {\n        let useCache = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true, cacheDuration = arguments.length > 3 ? arguments[3] : void 0;\n        return this.request({\n            method: 'GET',\n            url,\n            params\n        }, useCache, cacheDuration);\n    }\n    // POST request (no caching)\n    async post(url, data) {\n        return this.request({\n            method: 'POST',\n            url,\n            data\n        }, false);\n    }\n    // PUT request (no caching)\n    async put(url, data) {\n        return this.request({\n            method: 'PUT',\n            url,\n            data\n        }, false);\n    }\n    // DELETE request (no caching)\n    async delete(url) {\n        return this.request({\n            method: 'DELETE',\n            url\n        }, false);\n    }\n    // Clear cache\n    clearCache(pattern) {\n        this.cache.clear(pattern);\n    }\n    // Test connection to backend\n    async testConnection() {\n        try {\n            console.log('Testing connection to:', \"\".concat(this.client.defaults.baseURL, \"/health\"));\n            const response = await this.client.get('/health', {\n                timeout: 10000,\n                validateStatus: ()=>true // Accept any status code\n            });\n            if (response.status === 200) {\n                return {\n                    success: true,\n                    message: 'Backend connection successful',\n                    details: response.data\n                };\n            } else {\n                return {\n                    success: false,\n                    message: \"Backend responded with status \".concat(response.status),\n                    details: response.data\n                };\n            }\n        } catch (error) {\n            console.error('Connection test failed:', error);\n            if (error.code === 'ECONNREFUSED') {\n                return {\n                    success: false,\n                    message: 'Backend server is not running. Please start the backend server on http://localhost:8000',\n                    details: {\n                        error: error.code,\n                        message: error.message\n                    }\n                };\n            } else if (error.code === 'ENOTFOUND') {\n                return {\n                    success: false,\n                    message: 'Backend server not found. Please check the API URL configuration.',\n                    details: {\n                        error: error.code,\n                        message: error.message\n                    }\n                };\n            } else {\n                return {\n                    success: false,\n                    message: \"Connection failed: \".concat(error.message),\n                    details: {\n                        error: error.code,\n                        message: error.message\n                    }\n                };\n            }\n        }\n    }\n    // Handle API errors with enhanced diagnostics\n    handleError(error) {\n        console.error('API Client Error:', error);\n        if (error && typeof error === 'object' && 'response' in error) {\n            // Server responded with error status\n            const { status, data } = error.response;\n            // Prefer server-provided detail when available so the UI can show precise feedback\n            const serverMessage = data === null || data === void 0 ? void 0 : data.detail;\n            switch(status){\n                case 400:\n                    return new Error(serverMessage || 'Bad request');\n                case 401:\n                    // Pass through messages such as \"Account is locked\" when supplied\n                    return new Error(serverMessage || 'Unauthorized - please login again');\n                case 403:\n                    return new Error(serverMessage || 'Access denied - insufficient permissions');\n                case 404:\n                    return new Error(serverMessage || 'Resource not found');\n                case 422:\n                    return new Error(serverMessage || 'Validation error');\n                case 429:\n                    return new Error(serverMessage || 'Too many requests - please try again later');\n                case 500:\n                    return new Error(serverMessage || 'Internal server error');\n                default:\n                    return new Error(serverMessage || 'An error occurred');\n            }\n        } else if (error && typeof error === 'object' && 'request' in error) {\n            var _axiosError_config, _axiosError_config1, _axiosError_config2, _axiosError_config3;\n            // Network error - provide more detailed diagnostics\n            const axiosError = error;\n            console.error('Network Error Details:', {\n                code: axiosError.code,\n                message: axiosError.message,\n                config: {\n                    url: (_axiosError_config = axiosError.config) === null || _axiosError_config === void 0 ? void 0 : _axiosError_config.url,\n                    method: (_axiosError_config1 = axiosError.config) === null || _axiosError_config1 === void 0 ? void 0 : _axiosError_config1.method,\n                    baseURL: (_axiosError_config2 = axiosError.config) === null || _axiosError_config2 === void 0 ? void 0 : _axiosError_config2.baseURL,\n                    timeout: (_axiosError_config3 = axiosError.config) === null || _axiosError_config3 === void 0 ? void 0 : _axiosError_config3.timeout\n                }\n            });\n            // Provide specific error messages based on error code\n            if (axiosError.code === 'ECONNREFUSED') {\n                return new Error('Connection refused - Backend server may not be running. Please check if the server is started on http://localhost:8000');\n            } else if (axiosError.code === 'ENOTFOUND') {\n                return new Error('Server not found - Please check the API URL configuration');\n            } else if (axiosError.code === 'ETIMEDOUT') {\n                return new Error('Request timeout - Server is taking too long to respond');\n            } else if (axiosError.code === 'ECONNABORTED') {\n                return new Error('Request aborted - Connection was terminated');\n            } else {\n                return new Error(\"Network error (\".concat(axiosError.code || 'UNKNOWN', \") - Please check your connection and ensure the backend server is running\"));\n            }\n        } else {\n            // Other error\n            const message = error && typeof error === 'object' && 'message' in error ? String(error.message) : 'An unexpected error occurred';\n            return new Error(message);\n        }\n    }\n    // Token management\n    setTokens(accessToken, refreshToken) {\n        if (true) {\n            localStorage.setItem('access_token', accessToken);\n            localStorage.setItem('refresh_token', refreshToken);\n        }\n    }\n    clearTokens() {\n        if (true) {\n            localStorage.removeItem('access_token');\n            localStorage.removeItem('refresh_token');\n        }\n    }\n    getAccessToken() {\n        if (true) {\n            return localStorage.getItem('access_token');\n        }\n        return null;\n    }\n    getRefreshToken() {\n        if (true) {\n            return localStorage.getItem('refresh_token');\n        }\n        return null;\n    }\n    constructor(){\n        this.client = apiClient;\n        this.cache = CacheManager.getInstance();\n    }\n}\n// Export singleton instance\nconst apiClientInstance = new ApiClient();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClientInstance);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api-client.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5384f8ef704c\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcbmV4dGpzXFx3ZWJhcHBcXGZyb250ZW5kXFxzcmNcXHN0eWxlc1xcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1Mzg0ZjhlZjcwNGNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});