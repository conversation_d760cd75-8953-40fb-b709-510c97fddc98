# Network Error Troubleshooting Guide

## 🚨 **Error Analysis**

The error `Network error - please check your connection` indicates that the frontend cannot connect to the backend server. This is typically caused by:

1. **Backend server not running**
2. **Incorrect API URL configuration**
3. **Port conflicts**
4. **Firewall/antivirus blocking connections**
5. **CORS configuration issues**

---

## 🔧 **Quick Fix Steps**

### **Step 1: Check if Backend is Running**

**Windows:**
```cmd
# Check if port 8000 is in use
netstat -an | findstr ":8000"

# If nothing shows, backend is not running
```

**Linux/Mac:**
```bash
# Check if port 8000 is in use
lsof -i :8000

# If nothing shows, backend is not running
```

### **Step 2: Start Backend Server**

**Option A: Using our startup script (Windows)**
```cmd
cd scripts
start-services.bat start
```

**Option B: Manual startup**
```cmd
cd backend
python -m venv .venv
.venv\Scripts\activate
pip install uv
uv sync
alembic upgrade head
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### **Step 3: Verify Backend is Working**

Open your browser and go to:
- **Health Check**: http://localhost:8000/health
- **API Docs**: http://localhost:8000/docs

You should see a response like:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "version": "1.0.0"
}
```

### **Step 4: Check Frontend Configuration**

Verify `frontend/.env.local` contains:
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
```

### **Step 5: Test Connection**

Use the connection diagnostic component:
1. Go to your frontend app
2. Add the diagnostic component to a page
3. Run the connection test

---

## 🛠️ **Automated Solutions**

### **1. Use the Startup Scripts**

**Windows:**
```cmd
# Start all services
scripts\start-services.bat start

# Check status
scripts\start-services.bat status

# Stop services
scripts\start-services.bat stop
```

**Linux/Mac:**
```bash
# Start all services
./scripts/start-services.sh start

# Check status
./scripts/start-services.sh status

# Stop services
./scripts/start-services.sh stop
```

### **2. Use the Diagnostic Script**

```bash
# Run connection diagnostics
node scripts/diagnose-connection.js
```

### **3. Use Docker Compose (Alternative)**

```bash
# Start all services with Docker
docker-compose up -d

# Check logs
docker-compose logs -f

# Stop services
docker-compose down
```

---

## 🔍 **Detailed Troubleshooting**

### **Issue 1: Backend Not Starting**

**Symptoms:**
- `ECONNREFUSED` error
- Cannot access http://localhost:8000

**Solutions:**
1. **Check Python installation:**
   ```cmd
   python --version
   # Should show Python 3.11+ 
   ```

2. **Check virtual environment:**
   ```cmd
   cd backend
   .venv\Scripts\activate
   python -c "import app.main; print('Backend imports OK')"
   ```

3. **Check database connection:**
   ```cmd
   # In backend directory
   alembic current
   alembic upgrade head
   ```

4. **Check for port conflicts:**
   ```cmd
   netstat -an | findstr ":8000"
   # Kill any conflicting processes
   ```

### **Issue 2: CORS Errors**

**Symptoms:**
- Browser console shows CORS errors
- Preflight request failures

**Solutions:**
1. **Check backend CORS settings** in `backend/app/main.py`:
   ```python
   app.add_middleware(
       CORSMiddleware,
       allow_origins=["http://localhost:3000"],  # Frontend URL
       allow_credentials=True,
       allow_methods=["*"],
       allow_headers=["*"],
   )
   ```

2. **Verify frontend URL** in backend `.env`:
   ```env
   FRONTEND_BASE_URL=http://localhost:3000
   BACKEND_CORS_ORIGINS=http://localhost:3000
   ```

### **Issue 3: Environment Configuration**

**Symptoms:**
- API calls go to wrong URL
- Environment variables not loaded

**Solutions:**
1. **Check frontend environment:**
   ```bash
   # In frontend directory
   cat .env.local
   # Should contain NEXT_PUBLIC_API_URL=http://localhost:8000
   ```

2. **Check backend environment:**
   ```bash
   # In backend directory
   cat .env
   # Should contain proper database URL and settings
   ```

3. **Restart services** after environment changes:
   ```cmd
   scripts\start-services.bat restart
   ```

### **Issue 4: Firewall/Antivirus Blocking**

**Symptoms:**
- Connection timeouts
- Intermittent failures

**Solutions:**
1. **Add firewall exceptions** for:
   - Python.exe
   - Node.exe
   - Ports 3000 and 8000

2. **Temporarily disable antivirus** to test

3. **Use different ports** if blocked:
   ```env
   # Frontend .env.local
   NEXT_PUBLIC_API_URL=http://localhost:8001
   
   # Start backend on different port
   uvicorn app.main:app --port 8001
   ```

---

## 🧪 **Testing Solutions**

### **1. Manual Connection Test**

```bash
# Test backend directly
curl http://localhost:8000/health

# Test auth endpoint
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"test"}'
```

### **2. Browser Network Tab**

1. Open browser Developer Tools (F12)
2. Go to Network tab
3. Try to login
4. Check for failed requests
5. Look at request/response details

### **3. Backend Logs**

Check backend console for errors:
- Database connection issues
- Import errors
- Configuration problems

---

## 📋 **Checklist for Quick Resolution**

- [ ] Backend server is running on port 8000
- [ ] Frontend can access http://localhost:8000/health
- [ ] Environment variables are correctly set
- [ ] No firewall blocking connections
- [ ] CORS is properly configured
- [ ] Database migrations are up to date
- [ ] Virtual environment is activated
- [ ] All dependencies are installed

---

## 🆘 **Emergency Recovery**

If nothing else works:

1. **Complete restart:**
   ```cmd
   # Stop everything
   scripts\start-services.bat stop
   
   # Kill all processes on ports
   netstat -ano | findstr ":8000" 
   netstat -ano | findstr ":3000"
   # Kill PIDs manually
   
   # Restart
   scripts\start-services.bat start
   ```

2. **Reset environment:**
   ```cmd
   # Backend
   cd backend
   rmdir /s .venv
   python -m venv .venv
   .venv\Scripts\activate
   pip install uv
   uv sync
   
   # Frontend
   cd frontend
   rmdir /s node_modules
   npm install
   ```

3. **Use Docker as fallback:**
   ```cmd
   docker-compose up --build
   ```

---

## 📞 **Getting Help**

If you're still experiencing issues:

1. **Run the diagnostic script** and share the output
2. **Check browser console** for detailed error messages
3. **Check backend logs** for server-side errors
4. **Verify system requirements** (Python 3.11+, Node.js 18+)

The connection diagnostic component will help identify the specific issue and provide targeted solutions.
