@echo off
echo Testing Backend Connection...
echo ================================

REM Test backend health endpoint
echo 1. Testing backend health endpoint...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://127.0.0.1:8000/health' -Method GET -TimeoutSec 5; Write-Host 'SUCCESS: Backend is running' -ForegroundColor Green; Write-Host 'Status:' $response.status; Write-Host 'Version:' $response.version } catch { Write-Host 'ERROR: Backend not accessible' -ForegroundColor Red; Write-Host $_.Exception.Message }"

echo.

REM Test API v1 health endpoint
echo 2. Testing API v1 health endpoint...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://127.0.0.1:8000/api/v1/health' -Method GET -TimeoutSec 5; Write-Host 'SUCCESS: API v1 is accessible' -ForegroundColor Green } catch { Write-Host 'ERROR: API v1 not accessible' -ForegroundColor Red; Write-Host $_.Exception.Message }"

echo.

REM Test auth endpoint (expect 422 validation error)
echo 3. Testing auth endpoint...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://127.0.0.1:8000/api/v1/auth/login' -Method POST -ContentType 'application/json' -Body '{\"email_or_username\":\"<EMAIL>\",\"password\":\"test\"}' -TimeoutSec 5 } catch { if ($_.Exception.Response.StatusCode -eq 422 -or $_.Exception.Response.StatusCode -eq 401) { Write-Host 'SUCCESS: Auth endpoint is accessible (expected validation error)' -ForegroundColor Green } else { Write-Host 'ERROR: Auth endpoint issue' -ForegroundColor Red; Write-Host $_.Exception.Message } }"

echo.

REM Check if frontend is running
echo 4. Checking frontend status...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000' -Method GET -TimeoutSec 5; Write-Host 'SUCCESS: Frontend is running' -ForegroundColor Green } catch { Write-Host 'WARNING: Frontend not accessible' -ForegroundColor Yellow; Write-Host $_.Exception.Message }"

echo.

REM Check ports
echo 5. Checking port usage...
echo Backend (port 8000):
netstat -an | findstr ":8000"
echo Frontend (port 3000):
netstat -an | findstr ":3000"

echo.
echo ================================
echo Connection test completed.
echo.
echo If backend tests pass but frontend still has network errors:
echo 1. Restart frontend: scripts\restart-frontend.bat
echo 2. Clear browser cache
echo 3. Check browser console for detailed error messages
echo.
pause
