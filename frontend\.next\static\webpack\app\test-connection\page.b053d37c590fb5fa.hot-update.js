"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-connection/page",{

/***/ "(app-pages-browser)/./src/app/test-connection/page.tsx":
/*!******************************************!*\
  !*** ./src/app/test-connection/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestConnectionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_connection_diagnostic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/connection-diagnostic */ \"(app-pages-browser)/./src/components/connection-diagnostic.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _hooks_use_client_side__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-client-side */ \"(app-pages-browser)/./src/hooks/use-client-side.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction TestConnectionPage() {\n    _s();\n    const [testResults, setTestResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const currentOrigin = (0,_hooks_use_client_side__WEBPACK_IMPORTED_MODULE_7__.useCurrentOrigin)();\n    const runManualTests = async ()=>{\n        setIsLoading(true);\n        setTestResults([]);\n        const tests = [\n            {\n                name: 'Direct Health Check',\n                test: async ()=>{\n                    const response = await fetch('http://localhost:8000/health');\n                    const data = await response.json();\n                    return {\n                        success: true,\n                        data\n                    };\n                }\n            },\n            {\n                name: 'API Client Health Check',\n                test: async ()=>{\n                    const data = await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.get('/health');\n                    return {\n                        success: true,\n                        data\n                    };\n                }\n            },\n            {\n                name: 'API Client Connection Test',\n                test: async ()=>{\n                    const result = await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.testConnection();\n                    return result;\n                }\n            },\n            {\n                name: 'Auth Endpoint Test',\n                test: async ()=>{\n                    try {\n                        await _lib_api_client__WEBPACK_IMPORTED_MODULE_6__.apiClientInstance.post('/auth/login', {\n                            email: '<EMAIL>',\n                            password: 'test'\n                        });\n                        return {\n                            success: false,\n                            message: 'Unexpected success'\n                        };\n                    } catch (error) {\n                        if (error.message.includes('Validation error') || error.message.includes('Invalid credentials') || error.message.includes('User not found')) {\n                            return {\n                                success: true,\n                                message: 'Expected validation error',\n                                error: error.message\n                            };\n                        } else {\n                            var _error_response, _error_response1;\n                            return {\n                                success: false,\n                                message: error.message || 'Auth endpoint error',\n                                error: {\n                                    message: error.message,\n                                    code: error.code,\n                                    status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                                    data: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data\n                                }\n                            };\n                        }\n                    }\n                }\n            }\n        ];\n        const results = [];\n        for (const test of tests){\n            try {\n                const result = await test.test();\n                results.push({\n                    name: test.name,\n                    ...result\n                });\n            } catch (error) {\n                results.push({\n                    name: test.name,\n                    success: false,\n                    message: error.message,\n                    error\n                });\n            }\n        }\n        setTestResults(results);\n        setIsLoading(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestConnectionPage.useEffect\": ()=>{\n            runManualTests();\n        }\n    }[\"TestConnectionPage.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-8 space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"Connection Test Page\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground mt-2\",\n                        children: \"Diagnose connection issues between frontend and backend\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                lineNumber: 102,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            children: \"Environment Configuration\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Frontend URL:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 29\n                                        }, this),\n                                        \" \",\n                                        currentOrigin\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Backend URL:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 29\n                                        }, this),\n                                        \" \",\n                                        \"http://localhost:8000\" || 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Debug Mode:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 29\n                                        }, this),\n                                        \" \",\n                                        \"true\" || 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Node Env:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 29\n                                        }, this),\n                                        \" \",\n                                        \"development\" || 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                lineNumber: 110,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: \"Manual Connection Tests\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"These tests check different aspects of the connection\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: runManualTests,\n                                disabled: isLoading,\n                                children: isLoading ? 'Running Tests...' : 'Run Manual Tests'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 21\n                            }, this),\n                            testResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: testResults.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                        variant: result.success ? 'default' : 'destructive',\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium\",\n                                                    children: result.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm mt-1\",\n                                                    children: [\n                                                        result.success ? '✅' : '❌',\n                                                        \" \",\n                                                        result.message\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 41\n                                                }, this),\n                                                result.data && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                                    className: \"mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                            className: \"cursor-pointer text-xs\",\n                                                            children: \"Show Data\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 49\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            className: \"text-xs mt-1 p-2 bg-muted rounded\",\n                                                            children: JSON.stringify(result.data, null, 2)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 45\n                                                }, this),\n                                                result.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                                    className: \"mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                            className: \"cursor-pointer text-xs\",\n                                                            children: \"Show Error\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 49\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            className: \"text-xs mt-1 p-2 bg-muted rounded\",\n                                                            children: JSON.stringify(result.error, null, 2)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 33\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                lineNumber: 133,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_connection_diagnostic__WEBPACK_IMPORTED_MODULE_2__.ConnectionDiagnostic, {}, void 0, false, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                lineNumber: 179,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            children: \"Quick Links\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"http://localhost:8000/health\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"p-3 border rounded-lg hover:bg-muted transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: \"Backend Health\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"http://localhost:8000/health\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"http://localhost:8000/docs\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"p-3 border rounded-lg hover:bg-muted transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: \"API Documentation\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"http://localhost:8000/docs\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/login\",\n                                    className: \"p-3 border rounded-lg hover:bg-muted transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: \"Login Page\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Test authentication\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\app\\\\test-connection\\\\page.tsx\",\n        lineNumber: 101,\n        columnNumber: 9\n    }, this);\n}\n_s(TestConnectionPage, \"aYaOVuukEL0myMCT6O2lU66lGF8=\", false, function() {\n    return [\n        _hooks_use_client_side__WEBPACK_IMPORTED_MODULE_7__.useCurrentOrigin\n    ];\n});\n_c = TestConnectionPage;\nvar _c;\n$RefreshReg$(_c, \"TestConnectionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/test-connection/page.tsx\n"));

/***/ })

});