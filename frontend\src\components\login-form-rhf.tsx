"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { loginSchema, LoginFormData } from "@/lib/validation-schemas";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useState } from "react";
import { XCircle, Eye, EyeOff, Loader2 } from "lucide-react";
import { loginAction } from "@/serverActions/auth-actions";
import { useAuth } from "@/contexts/auth-context";
import type { TokenResponse } from "@/lib/auth-service";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { OAuthButton } from "@/components/oauth-button";

export function LoginFormRHF({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const { login } = useAuth();
  const [serverError, setServerError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting, isValid, isDirty },
    watch,
    trigger,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    mode: "onChange", // Enable real-time validation
  });

  const watchedFields = watch();

  // Real-time validation trigger
  const handleFieldBlur = async (fieldName: keyof LoginFormData) => {
    setIsValidating(true);
    await trigger(fieldName);
    setIsValidating(false);
  };

  const onSubmit = async (data: LoginFormData) => {
    try {
      setServerError(null);
      const formData = new FormData();
      formData.append("email_or_username", data.email_or_username);
      formData.append("password", data.password);
      const result = (await loginAction(
        { success: false, message: "" },
        formData
      )) as {
        success: boolean;
        message: string;
        data?: TokenResponse;
      };
      if (!result.success) {
        setServerError(result.message);
        return;
      }
      if (result.data?.access_token) {
        localStorage.setItem("access_token", result.data.access_token);
        localStorage.setItem("refresh_token", result.data.refresh_token);
      }
      // Call auth context login to handle redirect
      await login({
        email_or_username: data.email_or_username,
        password: data.password,
      });
    } catch {
      setServerError("Login failed. Please try again.");
    }
  };

  const router = useRouter();

  // Scroll to first error field on submit if there are errors
  const scrollToFirstError = () => {
    const errorFields = Object.keys(errors);
    if (errorFields.length > 0) {
      const el = document.getElementById(errorFields[0]);
      if (el) {
        el.scrollIntoView({ behavior: "smooth", block: "center" });
        el.focus();
      }
    }
  };

  // Attach scrollToFirstError to form submit
  const handleFormSubmit = handleSubmit(onSubmit, () => {
    scrollToFirstError();
  });

  return (
    <div className={cn("space-y-4", className)} {...props}>
      <Card className="shadow-sm border border-border bg-card max-w-sm mx-auto">
        <CardHeader className="text-center pb-4">
          <CardTitle className="text-xl font-semibold tracking-tight">
            Welcome back
          </CardTitle>
          <CardDescription className="text-sm text-muted-foreground">
            Login with your GitHub or Google account
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Social Login Buttons */}
          <div className="space-y-3">
            <OAuthButton provider="github" />
            <OAuthButton provider="google" />
          </div>

          {/* Divider */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-card px-2 text-muted-foreground">
                Or continue with
              </span>
            </div>
          </div>

          {serverError && (
            <Alert variant="destructive">
              <XCircle className="mt-1" />
              <AlertTitle className="font-semibold">
                Unable to sign in
              </AlertTitle>
              <AlertDescription>{serverError}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleFormSubmit} className="space-y-4">
            <div className="space-y-3">
              <div className="space-y-1.5">
                <Label htmlFor="email_or_username">Email or Username</Label>
                <div className="relative">
                  <Input
                    id="email_or_username"
                    placeholder="<EMAIL> or username"
                    {...register("email_or_username")}
                    onBlur={() => handleFieldBlur("email_or_username")}
                    className={cn(
                      "transition-all duration-200",
                      errors.email_or_username &&
                        "border-destructive focus-visible:ring-destructive",
                      watchedFields.email_or_username &&
                        !errors.email_or_username &&
                        "border-green-500 focus-visible:ring-green-500"
                    )}
                  />
                  {isValidating && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                    </div>
                  )}
                </div>
                {errors.email_or_username && (
                  <p className="text-xs text-destructive flex items-center gap-1">
                    <XCircle className="h-3 w-3" />
                    {errors.email_or_username.message}
                  </p>
                )}
              </div>
              <div className="space-y-1.5">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password">Password</Label>
                  <button
                    type="button"
                    onClick={() => router.push("/forgot-password")}
                    className="text-sm text-primary hover:underline"
                  >
                    Forgot your password?
                  </button>
                </div>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    {...register("password")}
                    onBlur={() => handleFieldBlur("password")}
                    className={cn(
                      "pr-10 transition-all duration-200",
                      errors.password &&
                        "border-destructive focus-visible:ring-destructive",
                      watchedFields.password &&
                        !errors.password &&
                        "border-green-500 focus-visible:ring-green-500"
                    )}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <Eye className="h-4 w-4 text-muted-foreground" />
                    )}
                  </Button>
                </div>
                {errors.password && (
                  <p className="text-xs text-destructive flex items-center gap-1">
                    <XCircle className="h-3 w-3" />
                    {errors.password.message}
                  </p>
                )}
              </div>
            </div>
            <Button
              type="submit"
              className="w-full relative"
              disabled={isSubmitting || !isValid || !isDirty}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Signing in...
                </>
              ) : (
                "Login"
              )}
            </Button>
          </form>

          {/* Sign up link */}
          <div className="text-center text-sm">
            Don&apos;t have an account?{" "}
            <Link href="/signup" className="text-primary hover:underline">
              Sign up
            </Link>
          </div>

          {/* Terms and Privacy */}
          <div className="text-center text-xs text-muted-foreground">
            By clicking continue, you agree to our{" "}
            <Link href="#" className="underline hover:text-foreground">
              Terms of Service
            </Link>{" "}
            and{" "}
            <Link href="#" className="underline hover:text-foreground">
              Privacy Policy
            </Link>
            .
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
