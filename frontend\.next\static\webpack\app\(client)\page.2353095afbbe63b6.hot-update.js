"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(client)/page",{

/***/ "(app-pages-browser)/./src/components/ui/particles.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/particles.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nfunction MousePosition() {\n    _s();\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        x: 0,\n        y: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"MousePosition.useEffect\": ()=>{\n            const handleMouseMove = {\n                \"MousePosition.useEffect.handleMouseMove\": (event)=>{\n                    setMousePosition({\n                        x: event.clientX,\n                        y: event.clientY\n                    });\n                }\n            }[\"MousePosition.useEffect.handleMouseMove\"];\n            window.addEventListener(\"mousemove\", handleMouseMove);\n            return ({\n                \"MousePosition.useEffect\": ()=>{\n                    window.removeEventListener(\"mousemove\", handleMouseMove);\n                }\n            })[\"MousePosition.useEffect\"];\n        }\n    }[\"MousePosition.useEffect\"], []);\n    return mousePosition;\n}\n_s(MousePosition, \"xsZ4oXLkOP0KnqTwAbN+/ZVPhrg=\");\n_c = MousePosition;\nfunction hexToRgb(hex) {\n    hex = hex.replace(\"#\", \"\");\n    if (hex.length === 3) {\n        hex = hex.split(\"\").map((char)=>char + char).join(\"\");\n    }\n    const hexInt = parseInt(hex, 16);\n    const red = hexInt >> 16 & 255;\n    const green = hexInt >> 8 & 255;\n    const blue = hexInt & 255;\n    return [\n        red,\n        green,\n        blue\n    ];\n}\nconst Particles = (param)=>{\n    let { className = \"\", quantity = 100, staticity = 50, ease = 50, size = 0.4, refresh = false, color = \"#ffffff\", vx = 0, vy = 0 } = param;\n    _s1();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const canvasContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const circles = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)([]);\n    const mousePosition = MousePosition();\n    const mouse = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const canvasSize = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)({\n        w: 0,\n        h: 0\n    });\n    const [dpr, setDpr] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Particles.useEffect\": ()=>{\n            if (true) {\n                setDpr(window.devicePixelRatio);\n            }\n        }\n    }[\"Particles.useEffect\"], []);\n    const clearContext = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"Particles.useCallback[clearContext]\": ()=>{\n            if (context.current) {\n                context.current.clearRect(0, 0, canvasSize.current.w, canvasSize.current.h);\n            }\n        }\n    }[\"Particles.useCallback[clearContext]\"], []);\n    const resizeCanvas = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"Particles.useCallback[resizeCanvas]\": ()=>{\n            if (canvasContainerRef.current && canvasRef.current && context.current) {\n                circles.current.length = 0;\n                canvasSize.current.w = canvasContainerRef.current.offsetWidth;\n                canvasSize.current.h = canvasContainerRef.current.offsetHeight;\n                canvasRef.current.width = canvasSize.current.w * dpr;\n                canvasRef.current.height = canvasSize.current.h * dpr;\n                canvasRef.current.style.width = \"\".concat(canvasSize.current.w, \"px\");\n                canvasRef.current.style.height = \"\".concat(canvasSize.current.h, \"px\");\n                context.current.scale(dpr, dpr);\n            }\n        }\n    }[\"Particles.useCallback[resizeCanvas]\"], [\n        dpr\n    ]);\n    const circleParams = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"Particles.useCallback[circleParams]\": ()=>{\n            const x = Math.floor(Math.random() * canvasSize.current.w);\n            const y = Math.floor(Math.random() * canvasSize.current.h);\n            const translateX = 0;\n            const translateY = 0;\n            const pSize = Math.floor(Math.random() * 2) + size;\n            const alpha = 0;\n            const targetAlpha = parseFloat((Math.random() * 0.6 + 0.1).toFixed(1));\n            const dx = (Math.random() - 0.5) * 0.1;\n            const dy = (Math.random() - 0.5) * 0.1;\n            const magnetism = 0.1 + Math.random() * 4;\n            return {\n                x,\n                y,\n                translateX,\n                translateY,\n                size: pSize,\n                alpha,\n                targetAlpha,\n                dx,\n                dy,\n                magnetism\n            };\n        }\n    }[\"Particles.useCallback[circleParams]\"], [\n        size\n    ]);\n    const rgb = hexToRgb(color);\n    const drawCircle = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"Particles.useCallback[drawCircle]\": function(circle) {\n            let update = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            if (context.current) {\n                const { x, y, translateX, translateY, size, alpha } = circle;\n                context.current.translate(translateX, translateY);\n                context.current.beginPath();\n                context.current.arc(x, y, size, 0, 2 * Math.PI);\n                context.current.fillStyle = \"rgba(\".concat(rgb.join(\", \"), \", \").concat(alpha, \")\");\n                context.current.fill();\n                context.current.setTransform(dpr, 0, 0, dpr, 0, 0);\n                if (!update) {\n                    circles.current.push(circle);\n                }\n            }\n        }\n    }[\"Particles.useCallback[drawCircle]\"], [\n        dpr,\n        rgb\n    ]);\n    const drawParticles = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"Particles.useCallback[drawParticles]\": ()=>{\n            clearContext();\n            const particleCount = quantity;\n            for(let i = 0; i < particleCount; i++){\n                const circle = circleParams();\n                drawCircle(circle);\n            }\n        }\n    }[\"Particles.useCallback[drawParticles]\"], [\n        clearContext,\n        quantity,\n        circleParams,\n        drawCircle\n    ]);\n    const initCanvas = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"Particles.useCallback[initCanvas]\": ()=>{\n            resizeCanvas();\n            drawParticles();\n        }\n    }[\"Particles.useCallback[initCanvas]\"], [\n        resizeCanvas,\n        drawParticles\n    ]);\n    const onMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"Particles.useCallback[onMouseMove]\": ()=>{\n            if (canvasRef.current) {\n                const rect = canvasRef.current.getBoundingClientRect();\n                const { w, h } = canvasSize.current;\n                const x = mousePosition.x - rect.left - w / 2;\n                const y = mousePosition.y - rect.top - h / 2;\n                const inside = x < w / 2 && x > -w / 2 && y < h / 2 && y > -h / 2;\n                if (inside) {\n                    mouse.current.x = x;\n                    mouse.current.y = y;\n                }\n            }\n        }\n    }[\"Particles.useCallback[onMouseMove]\"], [\n        mousePosition.x,\n        mousePosition.y\n    ]);\n    const remapValue = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"Particles.useCallback[remapValue]\": (value, start1, end1, start2, end2)=>{\n            const remapped = (value - start1) * (end2 - start2) / (end1 - start1) + start2;\n            return remapped > 0 ? remapped : 0;\n        }\n    }[\"Particles.useCallback[remapValue]\"], []);\n    const animate = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"Particles.useCallback[animate]\": ()=>{\n            clearContext();\n            circles.current.forEach({\n                \"Particles.useCallback[animate]\": (circle, i)=>{\n                    // Handle the alpha value\n                    const edge = [\n                        circle.x + circle.translateX - circle.size,\n                        canvasSize.current.w - circle.x - circle.translateX - circle.size,\n                        circle.y + circle.translateY - circle.size,\n                        canvasSize.current.h - circle.y - circle.translateY - circle.size\n                    ];\n                    const closestEdge = edge.reduce({\n                        \"Particles.useCallback[animate].closestEdge\": (a, b)=>Math.min(a, b)\n                    }[\"Particles.useCallback[animate].closestEdge\"]);\n                    const remapClosestEdge = parseFloat(remapValue(closestEdge, 0, 20, 0, 1).toFixed(2));\n                    if (remapClosestEdge > 1) {\n                        circle.alpha += 0.02;\n                        if (circle.alpha > circle.targetAlpha) {\n                            circle.alpha = circle.targetAlpha;\n                        }\n                    } else {\n                        circle.alpha = circle.targetAlpha * remapClosestEdge;\n                    }\n                    circle.x += circle.dx + vx;\n                    circle.y += circle.dy + vy;\n                    circle.translateX += (mouse.current.x / (staticity / circle.magnetism) - circle.translateX) / ease;\n                    circle.translateY += (mouse.current.y / (staticity / circle.magnetism) - circle.translateY) / ease;\n                    drawCircle(circle, true);\n                    // circle gets out of the canvas\n                    if (circle.x < -circle.size || circle.x > canvasSize.current.w + circle.size || circle.y < -circle.size || circle.y > canvasSize.current.h + circle.size) {\n                        // remove the circle from the array\n                        circles.current.splice(i, 1);\n                        // create a new circle\n                        const newCircle = circleParams();\n                        drawCircle(newCircle);\n                    // update the circle position\n                    }\n                }\n            }[\"Particles.useCallback[animate]\"]);\n            window.requestAnimationFrame(animate);\n        }\n    }[\"Particles.useCallback[animate]\"], [\n        clearContext,\n        drawCircle,\n        vx,\n        vy,\n        staticity,\n        ease,\n        circleParams,\n        remapValue,\n        canvasSize\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Particles.useEffect\": ()=>{\n            if (canvasRef.current) {\n                context.current = canvasRef.current.getContext(\"2d\");\n            }\n            initCanvas();\n            animate();\n            window.addEventListener(\"resize\", initCanvas);\n            return ({\n                \"Particles.useEffect\": ()=>{\n                    window.removeEventListener(\"resize\", initCanvas);\n                }\n            })[\"Particles.useEffect\"];\n        }\n    }[\"Particles.useEffect\"], [\n        color,\n        initCanvas,\n        animate\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Particles.useEffect\": ()=>{\n            onMouseMove();\n        }\n    }[\"Particles.useEffect\"], [\n        onMouseMove\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Particles.useEffect\": ()=>{\n            initCanvas();\n        }\n    }[\"Particles.useEffect\"], [\n        refresh,\n        initCanvas\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"pointer-events-none\", className),\n        ref: canvasContainerRef,\n        \"aria-hidden\": \"true\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n            ref: canvasRef,\n            className: \"size-full\"\n        }, void 0, false, {\n            fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\ui\\\\particles.tsx\",\n            lineNumber: 300,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\nextjs\\\\webapp\\\\frontend\\\\src\\\\components\\\\ui\\\\particles.tsx\",\n        lineNumber: 295,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(Particles, \"gERAJx+LGNvwFPspXHz8WPDtybU=\");\n_c1 = Particles;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Particles);\nvar _c, _c1;\n$RefreshReg$(_c, \"MousePosition\");\n$RefreshReg$(_c1, \"Particles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/particles.tsx\n"));

/***/ })

});