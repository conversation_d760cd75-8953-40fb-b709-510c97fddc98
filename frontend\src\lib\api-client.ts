import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
const API_VERSION = '/api/v1';

// Cache configuration
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds
const CACHE_PREFIX = 'api_cache_';

// Cache interface
interface CacheEntry {
    data: any;
    timestamp: number;
    expiresAt: number;
}

// Cache utility functions
class CacheManager {
    private static instance: CacheManager;
    private cache: Map<string, CacheEntry> = new Map();

    static getInstance(): CacheManager {
        if (!CacheManager.instance) {
            CacheManager.instance = new CacheManager();
        }
        return CacheManager.instance;
    }

    set(key: string, data: any, duration: number = CACHE_DURATION): void {
        const expiresAt = Date.now() + duration;
        this.cache.set(key, {
            data,
            timestamp: Date.now(),
            expiresAt
        });

        // Also store in localStorage for persistence across page reloads
        if (typeof window !== 'undefined') {
            try {
                localStorage.setItem(`${CACHE_PREFIX}${key}`, JSON.stringify({
                    data,
                    timestamp: Date.now(),
                    expiresAt
                }));
            } catch (error) {
                console.warn('Failed to store cache in localStorage:', error);
            }
        }
    }

    get(key: string): any | null {
        // Check memory cache first
        const memoryEntry = this.cache.get(key);
        if (memoryEntry && memoryEntry.expiresAt > Date.now()) {
            return memoryEntry.data;
        }

        // Check localStorage
        if (typeof window !== 'undefined') {
            try {
                const stored = localStorage.getItem(`${CACHE_PREFIX}${key}`);
                if (stored) {
                    const entry: CacheEntry = JSON.parse(stored);
                    if (entry.expiresAt > Date.now()) {
                        // Update memory cache
                        this.cache.set(key, entry);
                        return entry.data;
                    } else {
                        // Remove expired entry
                        localStorage.removeItem(`${CACHE_PREFIX}${key}`);
                    }
                }
            } catch (error) {
                console.warn('Failed to retrieve cache from localStorage:', error);
            }
        }

        return null;
    }

    delete(key: string): void {
        this.cache.delete(key);
        if (typeof window !== 'undefined') {
            localStorage.removeItem(`${CACHE_PREFIX}${key}`);
        }
    }

    clear(pattern?: string): void {
        if (pattern) {
            // Clear specific pattern
            for (const key of this.cache.keys()) {
                if (key.includes(pattern)) {
                    this.cache.delete(key);
                }
            }
            if (typeof window !== 'undefined') {
                for (let i = localStorage.length - 1; i >= 0; i--) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith(CACHE_PREFIX) && key.includes(pattern)) {
                        localStorage.removeItem(key);
                    }
                }
            }
        } else {
            // Clear all cache
            this.cache.clear();
            if (typeof window !== 'undefined') {
                for (let i = localStorage.length - 1; i >= 0; i--) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith(CACHE_PREFIX)) {
                        localStorage.removeItem(key);
                    }
                }
            }
        }
    }

    generateKey(config: AxiosRequestConfig): string {
        const { method, url, params, data } = config;
        return `${method?.toUpperCase()}_${url}_${JSON.stringify(params)}_${JSON.stringify(data)}`;
    }
}

// Create axios instance
const apiClient: AxiosInstance = axios.create({
    baseURL: `${API_BASE_URL}${API_VERSION}`,
    timeout: 30000, // Increased timeout for better reliability
    withCredentials: false, // Set to false to avoid CORS issues with credentials
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
});

// Request interceptor to add auth token and debug logging
apiClient.interceptors.request.use(
    (config) => {
        // Debug logging
        if (process.env.NEXT_PUBLIC_DEBUG === 'true') {
            console.log('API Request:', {
                method: config.method?.toUpperCase(),
                url: config.url,
                baseURL: config.baseURL,
                fullURL: `${config.baseURL}${config.url}`,
                headers: config.headers,
                data: config.data
            });
        }

        // Get token from localStorage or cookies
        const token = typeof window !== 'undefined' ? localStorage.getItem('access_token') : null;

        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }

        return config;
    },
    (error) => {
        console.error('API Request Error:', error);
        return Promise.reject(error);
    }
);

// Response interceptor for token refresh and error handling
apiClient.interceptors.response.use(
    (response: AxiosResponse) => {
        // Debug logging
        if (process.env.NEXT_PUBLIC_DEBUG === 'true') {
            console.log('API Response:', {
                status: response.status,
                statusText: response.statusText,
                url: response.config.url,
                data: response.data
            });
        }
        return response;
    },
    async (error) => {
        const originalRequest = error.config;

        // Handle 401 Unauthorized - try to refresh token
        if (error.response?.status === 401 && !originalRequest._retry) {
            originalRequest._retry = true;

            try {
                const refreshToken = typeof window !== 'undefined' ? localStorage.getItem('refresh_token') : null;

                if (refreshToken) {
                    const response = await axios.post(`${API_BASE_URL}${API_VERSION}/auth/refresh`, {
                        refresh_token: refreshToken,
                    });

                    const { access_token, refresh_token } = response.data;

                    // Store new tokens
                    if (typeof window !== 'undefined') {
                        localStorage.setItem('access_token', access_token);
                        localStorage.setItem('refresh_token', refresh_token);
                    }

                    // Retry original request with new token
                    originalRequest.headers.Authorization = `Bearer ${access_token}`;
                    return apiClient(originalRequest);
                }
            } catch {
                // Refresh failed - clear tokens and redirect to login
                if (typeof window !== 'undefined') {
                    localStorage.removeItem('access_token');
                    localStorage.removeItem('refresh_token');
                    window.location.href = '/login';
                }
            }
        }

        return Promise.reject(error);
    }
);

// API Response types
export interface ApiResponse<T = unknown> {
    success: boolean;
    message: string;
    data?: T;
}

export interface ErrorResponse {
    detail: string;
    error_code?: string;
    success: false;
}

// Error types for handling
interface AxiosErrorResponse {
    status: number;
    data: ErrorResponse;
}

interface ErrorWithResponse {
    response: AxiosErrorResponse;
}

interface ErrorWithRequest {
    request: unknown;
}

interface ErrorWithMessage {
    message: string;
}

// API Client class with typed methods and caching
export class ApiClient {
    private client: AxiosInstance;
    private cache: CacheManager;

    constructor() {
        this.client = apiClient;
        this.cache = CacheManager.getInstance();
    }

    // Generic request method with caching
    async request<T>(config: AxiosRequestConfig, useCache: boolean = false, cacheDuration?: number): Promise<T> {
        try {
            // Check cache for GET requests
            if (useCache && config.method?.toLowerCase() === 'get') {
                const cacheKey = this.cache.generateKey(config);
                const cachedData = this.cache.get(cacheKey);

                if (cachedData) {
                    return cachedData;
                }
            }

            const response = await this.client.request<T>(config);

            // Cache successful GET responses
            if (useCache && config.method?.toLowerCase() === 'get' && response.status === 200) {
                const cacheKey = this.cache.generateKey(config);
                this.cache.set(cacheKey, response.data, cacheDuration);
            }

            return response.data;
        } catch (error: unknown) {
            throw this.handleError(error);
        }
    }

    // Cached GET request
    async get<T>(url: string, params?: any, useCache: boolean = true, cacheDuration?: number): Promise<T> {
        return this.request<T>({
            method: 'GET',
            url,
            params
        }, useCache, cacheDuration);
    }

    // POST request (no caching)
    async post<T>(url: string, data?: any): Promise<T> {
        return this.request<T>({
            method: 'POST',
            url,
            data
        }, false);
    }

    // PUT request (no caching)
    async put<T>(url: string, data?: any): Promise<T> {
        return this.request<T>({
            method: 'PUT',
            url,
            data
        }, false);
    }

    // DELETE request (no caching)
    async delete<T>(url: string): Promise<T> {
        return this.request<T>({
            method: 'DELETE',
            url
        }, false);
    }

    // Clear cache
    clearCache(pattern?: string): void {
        this.cache.clear(pattern);
    }

    // Test connection to backend
    async testConnection(): Promise<{ success: boolean; message: string; details?: any }> {
        try {
            console.log('Testing connection to:', `${this.client.defaults.baseURL}/health`);

            const response = await this.client.get('/health', {
                timeout: 10000,
                validateStatus: () => true // Accept any status code
            });

            if (response.status === 200) {
                return {
                    success: true,
                    message: 'Backend connection successful',
                    details: response.data
                };
            } else {
                return {
                    success: false,
                    message: `Backend responded with status ${response.status}`,
                    details: response.data
                };
            }
        } catch (error: any) {
            console.error('Connection test failed:', error);

            if (error.code === 'ECONNREFUSED') {
                return {
                    success: false,
                    message: 'Backend server is not running. Please start the backend server on http://localhost:8000',
                    details: { error: error.code, message: error.message }
                };
            } else if (error.code === 'ENOTFOUND') {
                return {
                    success: false,
                    message: 'Backend server not found. Please check the API URL configuration.',
                    details: { error: error.code, message: error.message }
                };
            } else {
                return {
                    success: false,
                    message: `Connection failed: ${error.message}`,
                    details: { error: error.code, message: error.message }
                };
            }
        }
    }

    // Handle API errors with enhanced diagnostics
    private handleError(error: unknown): Error {
        console.error('API Client Error:', error);

        if (error && typeof error === 'object' && 'response' in error) {
            // Server responded with error status
            const { status, data } = (error as ErrorWithResponse).response;

            // Prefer server-provided detail when available so the UI can show precise feedback
            const serverMessage = data?.detail;

            switch (status) {
                case 400:
                    return new Error(serverMessage || 'Bad request');
                case 401:
                    // Pass through messages such as "Account is locked" when supplied
                    return new Error(serverMessage || 'Unauthorized - please login again');
                case 403:
                    return new Error(serverMessage || 'Access denied - insufficient permissions');
                case 404:
                    return new Error(serverMessage || 'Resource not found');
                case 422:
                    return new Error(serverMessage || 'Validation error');
                case 429:
                    return new Error(serverMessage || 'Too many requests - please try again later');
                case 500:
                    return new Error(serverMessage || 'Internal server error');
                default:
                    return new Error(serverMessage || 'An error occurred');
            }
        } else if (error && typeof error === 'object' && 'request' in error) {
            // Network error - provide more detailed diagnostics
            const axiosError = error as any;
            console.error('Network Error Details:', {
                code: axiosError.code,
                message: axiosError.message,
                config: {
                    url: axiosError.config?.url,
                    method: axiosError.config?.method,
                    baseURL: axiosError.config?.baseURL,
                    timeout: axiosError.config?.timeout
                }
            });

            // Provide specific error messages based on error code
            if (axiosError.code === 'ECONNREFUSED') {
                return new Error('Connection refused - Backend server may not be running. Please check if the server is started on http://localhost:8000');
            } else if (axiosError.code === 'ENOTFOUND') {
                return new Error('Server not found - Please check the API URL configuration');
            } else if (axiosError.code === 'ETIMEDOUT') {
                return new Error('Request timeout - Server is taking too long to respond');
            } else if (axiosError.code === 'ECONNABORTED') {
                return new Error('Request aborted - Connection was terminated');
            } else {
                return new Error(`Network error (${axiosError.code || 'UNKNOWN'}) - Please check your connection and ensure the backend server is running`);
            }
        } else {
            // Other error
            const message = error && typeof error === 'object' && 'message' in error
                ? String((error as ErrorWithMessage).message)
                : 'An unexpected error occurred';
            return new Error(message);
        }
    }

    // Token management
    setTokens(accessToken: string, refreshToken: string) {
        if (typeof window !== 'undefined') {
            localStorage.setItem('access_token', accessToken);
            localStorage.setItem('refresh_token', refreshToken);
        }
    }

    clearTokens() {
        if (typeof window !== 'undefined') {
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
        }
    }

    getAccessToken(): string | null {
        if (typeof window !== 'undefined') {
            return localStorage.getItem('access_token');
        }
        return null;
    }

    getRefreshToken(): string | null {
        if (typeof window !== 'undefined') {
            return localStorage.getItem('refresh_token');
        }
        return null;
    }
}

// Export singleton instance
export const apiClientInstance = new ApiClient();
export default apiClientInstance; 